"""
اختبارات وحدة معالجة الفيديو
Video processor unit tests
"""

import unittest
import tempfile
import os
from pathlib import Path
import sys

# إضافة مجلد src إلى مسار Python
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from core.video_processor import VideoProcessor
from utils.file_utils import FileManager
from config.settings import AppSettings

class TestVideoProcessor(unittest.TestCase):
    """اختبارات معالج الفيديو"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.processor = VideoProcessor()
        self.file_manager = FileManager()
        self.temp_dir = tempfile.mkdtemp()
        
        # إنشاء ملف فيديو وهمي للاختبار
        self.test_video_path = self._create_test_video()
    
    def tearDown(self):
        """تنظيف بعد الاختبارات"""
        # حذف الملفات المؤقتة
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def _create_test_video(self):
        """إنشاء ملف فيديو وهمي للاختبار"""
        # هذه دالة وهمية - في الواقع نحتاج ملف فيديو حقيقي للاختبار
        test_video = Path(self.temp_dir) / "test_video.mp4"
        
        # إنشاء ملف فارغ (للاختبار فقط)
        test_video.touch()
        
        return str(test_video)
    
    def test_ffmpeg_availability(self):
        """اختبار توفر FFmpeg"""
        # هذا الاختبار يتحقق من وجود FFmpeg
        try:
            processor = VideoProcessor()
            self.assertIsNotNone(processor.ffmpeg_path)
        except RuntimeError:
            self.skipTest("FFmpeg غير متوفر")
    
    def test_get_video_info_invalid_file(self):
        """اختبار الحصول على معلومات ملف غير صحيح"""
        with self.assertRaises(Exception):
            self.processor.get_video_info("nonexistent_file.mp4")
    
    def test_trim_video_invalid_times(self):
        """اختبار قص الفيديو بأوقات غير صحيحة"""
        output_path = Path(self.temp_dir) / "trimmed.mp4"
        
        # وقت البداية أكبر من وقت النهاية
        result = self.processor.trim_video(
            self.test_video_path, 
            str(output_path), 
            10.0,  # start
            5.0    # end
        )
        
        self.assertFalse(result)
    
    def test_resize_video_invalid_dimensions(self):
        """اختبار تغيير حجم الفيديو بأبعاد غير صحيحة"""
        output_path = Path(self.temp_dir) / "resized.mp4"
        
        # أبعاد سالبة
        result = self.processor.resize_video(
            self.test_video_path,
            str(output_path),
            -1920,  # width
            1080    # height
        )
        
        self.assertFalse(result)
    
    def test_add_text_overlay_empty_text(self):
        """اختبار إضافة نص فارغ"""
        output_path = Path(self.temp_dir) / "text_overlay.mp4"
        
        result = self.processor.add_text_overlay(
            self.test_video_path,
            str(output_path),
            ""  # نص فارغ
        )
        
        self.assertFalse(result)
    
    def test_convert_format_invalid_format(self):
        """اختبار تحويل إلى تنسيق غير صحيح"""
        output_path = Path(self.temp_dir) / "converted.xyz"
        
        result = self.processor.convert_format(
            self.test_video_path,
            str(output_path),
            "xyz"  # تنسيق غير موجود
        )
        
        self.assertFalse(result)
    
    def test_extract_audio_valid_input(self):
        """اختبار استخراج الصوت"""
        output_path = Path(self.temp_dir) / "audio.mp3"
        
        # هذا الاختبار سيفشل مع ملف وهمي، لكنه يختبر البنية
        result = self.processor.extract_audio(
            self.test_video_path,
            str(output_path)
        )
        
        # نتوقع الفشل مع ملف وهمي
        self.assertFalse(result)

class TestVideoProcessorIntegration(unittest.TestCase):
    """اختبارات التكامل لمعالج الفيديو"""
    
    def setUp(self):
        """إعداد اختبارات التكامل"""
        self.processor = VideoProcessor()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """تنظيف بعد الاختبارات"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    @unittest.skipUnless(os.getenv('INTEGRATION_TESTS'), 'اختبارات التكامل معطلة')
    def test_full_video_processing_workflow(self):
        """اختبار سير عمل معالجة الفيديو الكامل"""
        # هذا الاختبار يتطلب ملف فيديو حقيقي
        # يمكن تشغيله فقط عند توفر متغير البيئة INTEGRATION_TESTS
        
        # مسار ملف فيديو للاختبار (يجب أن يكون موجوداً)
        test_video = os.getenv('TEST_VIDEO_PATH')
        if not test_video or not os.path.exists(test_video):
            self.skipTest("ملف فيديو الاختبار غير متوفر")
        
        # 1. الحصول على معلومات الفيديو
        video_info = self.processor.get_video_info(test_video)
        self.assertIsNotNone(video_info)
        self.assertIn('duration', video_info)
        self.assertGreater(video_info['duration'], 0)
        
        # 2. قص الفيديو
        trimmed_path = Path(self.temp_dir) / "trimmed.mp4"
        duration = video_info['duration']
        start_time = min(5.0, duration * 0.1)
        end_time = min(start_time + 10.0, duration * 0.9)
        
        trim_result = self.processor.trim_video(
            test_video, str(trimmed_path), start_time, end_time
        )
        self.assertTrue(trim_result)
        self.assertTrue(trimmed_path.exists())
        
        # 3. إضافة نص
        text_path = Path(self.temp_dir) / "with_text.mp4"
        text_result = self.processor.add_text_overlay(
            str(trimmed_path), str(text_path), "نص تجريبي"
        )
        self.assertTrue(text_result)
        self.assertTrue(text_path.exists())
        
        # 4. تغيير الحجم
        resized_path = Path(self.temp_dir) / "resized.mp4"
        resize_result = self.processor.resize_video(
            str(text_path), str(resized_path), 1280, 720
        )
        self.assertTrue(resize_result)
        self.assertTrue(resized_path.exists())

class TestVideoProcessorPerformance(unittest.TestCase):
    """اختبارات الأداء لمعالج الفيديو"""
    
    def setUp(self):
        """إعداد اختبارات الأداء"""
        self.processor = VideoProcessor()
    
    @unittest.skipUnless(os.getenv('PERFORMANCE_TESTS'), 'اختبارات الأداء معطلة')
    def test_video_info_performance(self):
        """اختبار أداء الحصول على معلومات الفيديو"""
        import time
        
        test_video = os.getenv('TEST_VIDEO_PATH')
        if not test_video or not os.path.exists(test_video):
            self.skipTest("ملف فيديو الاختبار غير متوفر")
        
        # قياس الوقت
        start_time = time.time()
        video_info = self.processor.get_video_info(test_video)
        end_time = time.time()
        
        processing_time = end_time - start_time
        
        # يجب أن يكون الحصول على المعلومات سريعاً (أقل من 5 ثواني)
        self.assertLess(processing_time, 5.0, 
                       f"الحصول على معلومات الفيديو استغرق {processing_time:.2f} ثانية")
        
        self.assertIsNotNone(video_info)
    
    @unittest.skipUnless(os.getenv('PERFORMANCE_TESTS'), 'اختبارات الأداء معطلة')
    def test_trim_performance(self):
        """اختبار أداء قص الفيديو"""
        import time
        
        test_video = os.getenv('TEST_VIDEO_PATH')
        if not test_video or not os.path.exists(test_video):
            self.skipTest("ملف فيديو الاختبار غير متوفر")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = Path(temp_dir) / "trimmed.mp4"
            
            # قياس الوقت
            start_time = time.time()
            result = self.processor.trim_video(
                test_video, str(output_path), 0, 10  # قص 10 ثواني
            )
            end_time = time.time()
            
            processing_time = end_time - start_time
            
            # قص 10 ثواني يجب أن يكون سريعاً (أقل من 30 ثانية)
            self.assertLess(processing_time, 30.0,
                           f"قص الفيديو استغرق {processing_time:.2f} ثانية")
            
            self.assertTrue(result)

if __name__ == '__main__':
    # تشغيل الاختبارات
    unittest.main(verbosity=2)
