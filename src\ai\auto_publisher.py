"""
وحدة النشر التلقائي
Auto-publishing module for social media platforms
"""

import logging
import json
from typing import Optional, Dict, List, Any, Tuple
from pathlib import Path
from datetime import datetime
import webbrowser
import urllib.parse

from config.settings import AppSettings

logger = logging.getLogger(__name__)

class AutoPublisher:
    """ناشر تلقائي للمنصات الاجتماعية"""
    
    def __init__(self):
        self.supported_platforms = {
            "youtube": {
                "name": "YouTube",
                "max_file_size": 128 * 1024 * 1024 * 1024,  # 128GB
                "supported_formats": [".mp4", ".mov", ".avi", ".wmv", ".flv", ".webm"],
                "max_title_length": 100,
                "max_description_length": 5000,
                "upload_url": "https://studio.youtube.com/channel/UC/videos/upload"
            },
            "tiktok": {
                "name": "Tik<PERSON><PERSON>",
                "max_file_size": 4 * 1024 * 1024 * 1024,  # 4GB
                "supported_formats": [".mp4", ".mov"],
                "max_title_length": 150,
                "max_description_length": 2200,
                "upload_url": "https://www.tiktok.com/upload"
            },
            "instagram": {
                "name": "Instagram",
                "max_file_size": 4 * 1024 * 1024 * 1024,  # 4GB
                "supported_formats": [".mp4", ".mov"],
                "max_title_length": 125,
                "max_description_length": 2200,
                "upload_url": "https://www.instagram.com/"
            },
            "facebook": {
                "name": "Facebook",
                "max_file_size": 10 * 1024 * 1024 * 1024,  # 10GB
                "supported_formats": [".mp4", ".mov", ".avi"],
                "max_title_length": 255,
                "max_description_length": 63206,
                "upload_url": "https://www.facebook.com/"
            },
            "twitter": {
                "name": "Twitter/X",
                "max_file_size": 512 * 1024 * 1024,  # 512MB
                "supported_formats": [".mp4", ".mov"],
                "max_title_length": 280,
                "max_description_length": 280,
                "upload_url": "https://twitter.com/compose/tweet"
            }
        }
        
        logger.info("تم تهيئة الناشر التلقائي")
    
    def validate_video_for_platform(self, video_path: str, 
                                   platform: str) -> Tuple[bool, List[str]]:
        """التحقق من صحة الفيديو للمنصة"""
        issues = []
        
        try:
            if platform not in self.supported_platforms:
                issues.append(f"المنصة {platform} غير مدعومة")
                return False, issues
            
            platform_info = self.supported_platforms[platform]
            video_file = Path(video_path)
            
            # التحقق من وجود الملف
            if not video_file.exists():
                issues.append("الملف غير موجود")
                return False, issues
            
            # التحقق من التنسيق
            file_extension = video_file.suffix.lower()
            if file_extension not in platform_info["supported_formats"]:
                issues.append(f"تنسيق الملف {file_extension} غير مدعوم في {platform_info['name']}")
            
            # التحقق من حجم الملف
            file_size = video_file.stat().st_size
            if file_size > platform_info["max_file_size"]:
                size_mb = file_size / (1024 * 1024)
                max_size_mb = platform_info["max_file_size"] / (1024 * 1024)
                issues.append(f"حجم الملف ({size_mb:.1f}MB) يتجاوز الحد المسموح ({max_size_mb:.1f}MB)")
            
            # التحقق من مواصفات الفيديو للمنصات المحددة
            if platform == "tiktok":
                issues.extend(self._validate_tiktok_specs(video_path))
            elif platform == "instagram":
                issues.extend(self._validate_instagram_specs(video_path))
            
            is_valid = len(issues) == 0
            return is_valid, issues
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من الفيديو: {e}")
            issues.append(f"خطأ في التحقق: {e}")
            return False, issues
    
    def _validate_tiktok_specs(self, video_path: str) -> List[str]:
        """التحقق من مواصفات TikTok"""
        issues = []
        
        try:
            from core.video_processor import VideoProcessor
            processor = VideoProcessor()
            video_info = processor.get_video_info(video_path)
            
            duration = video_info.get('duration', 0)
            width = video_info['video']['width']
            height = video_info['video']['height']
            aspect_ratio = width / height if height > 0 else 0
            
            # مدة الفيديو
            if duration > 600:  # 10 دقائق
                issues.append("مدة الفيديو تتجاوز 10 دقائق (حد TikTok)")
            elif duration < 3:
                issues.append("مدة الفيديو قصيرة جداً (أقل من 3 ثواني)")
            
            # نسبة العرض إلى الارتفاع
            if not (0.5 <= aspect_ratio <= 1.0):
                issues.append("نسبة العرض إلى الارتفاع غير مناسبة لـ TikTok (يُفضل 9:16 أو 1:1)")
            
            # الدقة
            if height < 720:
                issues.append("دقة الفيديو منخفضة (يُفضل 720p أو أعلى)")
                
        except Exception as e:
            logger.error(f"خطأ في التحقق من مواصفات TikTok: {e}")
            issues.append("خطأ في التحقق من المواصفات")
        
        return issues
    
    def _validate_instagram_specs(self, video_path: str) -> List[str]:
        """التحقق من مواصفات Instagram"""
        issues = []
        
        try:
            from core.video_processor import VideoProcessor
            processor = VideoProcessor()
            video_info = processor.get_video_info(video_path)
            
            duration = video_info.get('duration', 0)
            width = video_info['video']['width']
            height = video_info['video']['height']
            aspect_ratio = width / height if height > 0 else 0
            
            # مدة الفيديو للـ Reels
            if duration > 90:  # 90 ثانية للـ Reels
                issues.append("مدة الفيديو تتجاوز 90 ثانية (حد Instagram Reels)")
            elif duration < 3:
                issues.append("مدة الفيديو قصيرة جداً (أقل من 3 ثواني)")
            
            # نسبة العرض إلى الارتفاع
            if not (0.5 <= aspect_ratio <= 1.91):
                issues.append("نسبة العرض إلى الارتفاع غير مناسبة لـ Instagram")
            
            # الدقة
            if width < 720 or height < 720:
                issues.append("دقة الفيديو منخفضة (يُفضل 720x720 أو أعلى)")
                
        except Exception as e:
            logger.error(f"خطأ في التحقق من مواصفات Instagram: {e}")
            issues.append("خطأ في التحقق من المواصفات")
        
        return issues
    
    def prepare_content_for_platform(self, content: Dict[str, Any], 
                                   platform: str) -> Dict[str, Any]:
        """تحضير المحتوى للمنصة المحددة"""
        try:
            if platform not in self.supported_platforms:
                return content
            
            platform_info = self.supported_platforms[platform]
            prepared_content = content.copy()
            
            # تقصير العنوان إذا لزم الأمر
            title = content.get("title", "")
            max_title_length = platform_info["max_title_length"]
            if len(title) > max_title_length:
                prepared_content["title"] = title[:max_title_length-3] + "..."
            
            # تقصير الوصف إذا لزم الأمر
            description = content.get("description", "")
            max_desc_length = platform_info["max_description_length"]
            if len(description) > max_desc_length:
                prepared_content["description"] = description[:max_desc_length-3] + "..."
            
            # إضافة هاشتاغات مناسبة للمنصة
            hashtags = content.get("hashtags", [])
            platform_hashtags = self._get_platform_specific_hashtags(platform)
            
            # دمج الهاشتاغات
            all_hashtags = list(set(hashtags + platform_hashtags))
            
            # تحديد عدد الهاشتاغات المناسب للمنصة
            if platform == "instagram":
                all_hashtags = all_hashtags[:30]  # حد أقصى 30 هاشتاغ
            elif platform == "tiktok":
                all_hashtags = all_hashtags[:20]  # حد أقصى 20 هاشتاغ
            elif platform == "twitter":
                all_hashtags = all_hashtags[:5]   # حد أقصى 5 هاشتاغات
            
            prepared_content["hashtags"] = all_hashtags
            
            # إضافة معلومات خاصة بالمنصة
            prepared_content["platform_info"] = {
                "platform": platform,
                "platform_name": platform_info["name"],
                "upload_url": platform_info["upload_url"]
            }
            
            logger.info(f"تم تحضير المحتوى لـ {platform_info['name']}")
            return prepared_content
            
        except Exception as e:
            logger.error(f"خطأ في تحضير المحتوى: {e}")
            return content
    
    def _get_platform_specific_hashtags(self, platform: str) -> List[str]:
        """الحصول على هاشتاغات خاصة بالمنصة"""
        platform_hashtags = {
            "youtube": ["#YouTube", "#فيديو", "#محتوى"],
            "tiktok": ["#TikTok", "#فيديو_قصير", "#ترند"],
            "instagram": ["#Instagram", "#Reels", "#محتوى_عربي"],
            "facebook": ["#Facebook", "#فيديو", "#مشاركة"],
            "twitter": ["#Twitter", "#فيديو"]
        }
        
        return platform_hashtags.get(platform, [])
    
    def generate_upload_instructions(self, video_path: str, content: Dict[str, Any], 
                                   platform: str) -> Dict[str, Any]:
        """توليد تعليمات الرفع"""
        try:
            if platform not in self.supported_platforms:
                return {}
            
            platform_info = self.supported_platforms[platform]
            prepared_content = self.prepare_content_for_platform(content, platform)
            
            instructions = {
                "platform": platform_info["name"],
                "upload_url": platform_info["upload_url"],
                "video_path": video_path,
                "content": prepared_content,
                "steps": self._get_upload_steps(platform),
                "tips": self._get_platform_tips(platform)
            }
            
            return instructions
            
        except Exception as e:
            logger.error(f"خطأ في توليد تعليمات الرفع: {e}")
            return {}
    
    def _get_upload_steps(self, platform: str) -> List[str]:
        """الحصول على خطوات الرفع للمنصة"""
        steps = {
            "youtube": [
                "1. اذهب إلى YouTube Studio",
                "2. انقر على 'إنشاء' ثم 'رفع فيديو'",
                "3. اختر ملف الفيديو",
                "4. أدخل العنوان والوصف",
                "5. أضف الهاشتاغات في الوصف",
                "6. اختر الصورة المصغرة",
                "7. حدد إعدادات الخصوصية",
                "8. انقر على 'نشر'"
            ],
            "tiktok": [
                "1. افتح تطبيق TikTok",
                "2. انقر على '+' لإنشاء فيديو",
                "3. انقر على 'رفع' واختر الفيديو",
                "4. أضف التأثيرات إذا رغبت",
                "5. اكتب الوصف والهاشتاغات",
                "6. اختر الغلاف",
                "7. حدد إعدادات الخصوصية",
                "8. انقر على 'نشر'"
            ],
            "instagram": [
                "1. افتح تطبيق Instagram",
                "2. انقر على '+' ثم 'Reel'",
                "3. انقر على أيقونة المعرض واختر الفيديو",
                "4. أضف الموسيقى والتأثيرات إذا رغبت",
                "5. اكتب التعليق والهاشتاغات",
                "6. اختر الغلاف",
                "7. حدد إعدادات المشاركة",
                "8. انقر على 'مشاركة'"
            ],
            "facebook": [
                "1. اذهب إلى صفحتك على Facebook",
                "2. انقر على 'إنشاء منشور'",
                "3. انقر على 'صورة/فيديو'",
                "4. اختر ملف الفيديو",
                "5. اكتب النص والهاشتاغات",
                "6. حدد الجمهور",
                "7. انقر على 'نشر'"
            ],
            "twitter": [
                "1. اذهب إلى Twitter",
                "2. انقر على 'تغريد'",
                "3. انقر على أيقونة الوسائط",
                "4. اختر ملف الفيديو",
                "5. اكتب النص والهاشتاغات",
                "6. انقر على 'تغريد'"
            ]
        }
        
        return steps.get(platform, [])
    
    def _get_platform_tips(self, platform: str) -> List[str]:
        """الحصول على نصائح للمنصة"""
        tips = {
            "youtube": [
                "استخدم صورة مصغرة جذابة",
                "اكتب عنواناً واضحاً ومثيراً للاهتمام",
                "أضف الهاشتاغات في نهاية الوصف",
                "حدد فئة مناسبة للفيديو",
                "فعّل الترجمة التلقائية"
            ],
            "tiktok": [
                "استخدم الهاشتاغات الرائجة",
                "اجعل أول 3 ثواني جذابة",
                "استخدم الموسيقى الشائعة",
                "انشر في الأوقات النشطة",
                "تفاعل مع التعليقات بسرعة"
            ],
            "instagram": [
                "استخدم نسبة 9:16 للـ Reels",
                "أضف نصاً على الفيديو",
                "استخدم الهاشتاغات المناسبة",
                "انشر بانتظام",
                "استخدم الستوريز للترويج"
            ],
            "facebook": [
                "أضف وصفاً مفصلاً",
                "استخدم الهاشتاغات بحكمة",
                "شارك في المجموعات ذات الصلة",
                "تفاعل مع التعليقات",
                "استخدم Facebook Creator Studio"
            ],
            "twitter": [
                "اجعل النص قصيراً ومؤثراً",
                "استخدم هاشتاغات قليلة ومناسبة",
                "انشر في الأوقات النشطة",
                "تفاعل مع المتابعين",
                "استخدم الخيوط للمحتوى الطويل"
            ]
        }
        
        return tips.get(platform, [])
    
    def open_upload_page(self, platform: str, content: Optional[Dict] = None):
        """فتح صفحة الرفع للمنصة"""
        try:
            if platform not in self.supported_platforms:
                logger.error(f"المنصة {platform} غير مدعومة")
                return
            
            upload_url = self.supported_platforms[platform]["upload_url"]
            
            # إضافة معاملات URL إذا كان المحتوى متوفراً
            if content and platform == "twitter":
                text = content.get("title", "") + " " + " ".join(content.get("hashtags", []))
                encoded_text = urllib.parse.quote(text[:280])  # حد Twitter
                upload_url += f"?text={encoded_text}"
            
            webbrowser.open(upload_url)
            logger.info(f"تم فتح صفحة الرفع لـ {self.supported_platforms[platform]['name']}")
            
        except Exception as e:
            logger.error(f"خطأ في فتح صفحة الرفع: {e}")
    
    def save_upload_package(self, video_path: str, content: Dict[str, Any], 
                          platforms: List[str], output_dir: str) -> bool:
        """حفظ حزمة الرفع للمنصات المختلفة"""
        try:
            output_path = Path(output_dir)
            output_path.mkdir(exist_ok=True)
            
            package_data = {
                "video_path": video_path,
                "created_at": datetime.now().isoformat(),
                "platforms": {}
            }
            
            for platform in platforms:
                if platform in self.supported_platforms:
                    # التحقق من صحة الفيديو
                    is_valid, issues = self.validate_video_for_platform(video_path, platform)
                    
                    # تحضير المحتوى
                    prepared_content = self.prepare_content_for_platform(content, platform)
                    
                    # توليد التعليمات
                    instructions = self.generate_upload_instructions(
                        video_path, content, platform
                    )
                    
                    package_data["platforms"][platform] = {
                        "is_valid": is_valid,
                        "issues": issues,
                        "content": prepared_content,
                        "instructions": instructions
                    }
            
            # حفظ البيانات
            package_file = output_path / "upload_package.json"
            with open(package_file, 'w', encoding='utf-8') as f:
                json.dump(package_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"تم حفظ حزمة الرفع: {package_file}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في حفظ حزمة الرفع: {e}")
            return False
    
    def get_supported_platforms(self) -> Dict[str, Dict]:
        """الحصول على قائمة المنصات المدعومة"""
        return self.supported_platforms.copy()
