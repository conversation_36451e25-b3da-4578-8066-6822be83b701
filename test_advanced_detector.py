#!/usr/bin/env python3
"""
اختبار نظام اكتشاف اللقطات المتقدم
Test Advanced Highlight Detection System
"""

import sys
import os
import logging
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from ai.advanced_highlight_detector import AdvancedHighlightDetector, HighlightType

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_advanced_detector.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def test_advanced_detector():
    """اختبار النظام المتقدم"""
    try:
        print("🧪 اختبار نظام اكتشاف اللقطات المتقدم")
        print("=" * 50)
        
        # إنشاء كاشف اللقطات المتقدم
        detector = AdvancedHighlightDetector()
        print("✅ تم إنشاء كاشف اللقطات المتقدم بنجاح")
        
        # اختبار الكلمات المفتاحية المحسنة
        test_text = "wow this is insane! omg I can't believe this happened! haha that's so funny lol"
        print(f"\n📝 اختبار النص: {test_text}")
        
        # اختبار تحليل الكلمات المفتاحية
        keyword_analysis = detector._analyze_keywords_enhanced(test_text)
        print(f"🔍 تحليل الكلمات المفتاحية:")
        for key, value in keyword_analysis.items():
            print(f"   {key}: {value}")
        
        # اختبار تحليل الأنماط النصية
        pattern_analysis = detector._analyze_text_patterns(test_text)
        print(f"\n🎯 تحليل الأنماط النصية:")
        for key, value in pattern_analysis.items():
            print(f"   {key}: {value}")
        
        # اختبار حساب النقاط
        print(f"\n📊 اختبار حساب النقاط:")
        for highlight_type in HighlightType:
            text_score = detector._calculate_text_score(highlight_type, {
                **keyword_analysis,
                **pattern_analysis
            })
            print(f"   {highlight_type.value}: {text_score:.3f}")
        
        # اختبار إنشاء العناوين
        print(f"\n📰 اختبار إنشاء العناوين:")
        from ai.advanced_highlight_detector import HighlightMoment
        
        for highlight_type in HighlightType:
            mock_highlight = HighlightMoment(
                start_time=0,
                end_time=30,
                highlight_type=highlight_type,
                confidence=0.8,
                text=test_text,
                audio_features={},
                visual_features={},
                sentiment_score=0.7,
                source='test'
            )
            title = detector._generate_engaging_title(mock_highlight)
            print(f"   {highlight_type.value}: {title}")
        
        # اختبار إنشاء الهاشتاجات
        print(f"\n🏷️ اختبار إنشاء الهاشتاجات:")
        hashtags = detector._generate_hashtags(mock_highlight)
        print(f"   {', '.join(hashtags)}")
        
        print(f"\n✅ جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        logger.error(f"خطأ في اختبار النظام المتقدم: {e}")
        return False

def test_with_video_file():
    """اختبار مع ملف فيديو إذا كان متوفراً"""
    try:
        print(f"\n🎬 اختبار مع ملف فيديو")
        print("=" * 30)
        
        # البحث عن ملفات فيديو في مجلد downloads
        downloads_dir = Path("downloads")
        if not downloads_dir.exists():
            print("❌ مجلد downloads غير موجود")
            return False
        
        video_files = list(downloads_dir.glob("*.mp4"))
        if not video_files:
            print("❌ لا توجد ملفات فيديو في مجلد downloads")
            return False
        
        video_file = video_files[0]
        print(f"📁 استخدام الملف: {video_file.name}")
        
        # إنشاء كاشف اللقطات
        detector = AdvancedHighlightDetector()
        
        # اختبار اكتشاف اللقطات (مع عدد قليل للاختبار السريع)
        print("🔍 بدء اكتشاف اللقطات...")
        highlights = detector.detect_highlights(str(video_file), target_count=2)
        
        if highlights:
            print(f"✅ تم العثور على {len(highlights)} لقطة بارزة:")
            for i, highlight in enumerate(highlights, 1):
                print(f"   {i}. {highlight.highlight_type.value} - "
                      f"{highlight.start_time:.1f}s-{highlight.end_time:.1f}s - "
                      f"ثقة: {highlight.confidence:.2f}")
                if highlight.text:
                    preview = highlight.text[:50] + "..." if len(highlight.text) > 50 else highlight.text
                    print(f"      النص: \"{preview}\"")
        else:
            print("❌ لم يتم العثور على لقطات بارزة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الفيديو: {e}")
        logger.error(f"خطأ في اختبار الفيديو: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    try:
        print("🚀 بدء اختبار نظام اكتشاف اللقطات المتقدم")
        print("=" * 60)
        
        # اختبار النظام الأساسي
        basic_test = test_advanced_detector()
        
        # اختبار مع ملف فيديو (اختياري)
        video_test = test_with_video_file()
        
        print(f"\n📋 ملخص النتائج:")
        print(f"   الاختبار الأساسي: {'✅ نجح' if basic_test else '❌ فشل'}")
        print(f"   اختبار الفيديو: {'✅ نجح' if video_test else '❌ فشل'}")
        
        if basic_test:
            print(f"\n🎉 النظام المتقدم جاهز للاستخدام!")
            print(f"💡 يمكنك الآن استخدام التطبيق مع النظام المحسن لاكتشاف اللقطات")
        else:
            print(f"\n⚠️ هناك مشاكل في النظام تحتاج إلى إصلاح")
        
        return 0 if basic_test else 1
        
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {e}")
        logger.error(f"خطأ عام في الاختبار: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
