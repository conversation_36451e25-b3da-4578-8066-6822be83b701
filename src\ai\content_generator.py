"""
مولد المحتوى الذكي
Smart content generator for titles, descriptions, and CTAs
"""

import logging
import re
from typing import Optional, Dict, List, Any
import json

from ai.huggingface_client import HuggingFaceClient
from ai.google_cloud_client import GoogleCloudClient

logger = logging.getLogger(__name__)

class ContentGenerator:
    """مولد المحتوى الذكي للعناوين والأوصاف وعبارات الحث"""
    
    def __init__(self):
        self.hf_client = HuggingFaceClient()
        self.gc_client = GoogleCloudClient()
        
        # قوالب المحتوى
        self.title_templates = {
            "educational": [
                "كيفية {topic} في {duration} دقائق",
                "دليل شامل لـ {topic}",
                "أسرار {topic} التي لا يعرفها أحد",
                "تعلم {topic} من الصفر"
            ],
            "entertainment": [
                "أفضل لحظات {topic}",
                "مقاطع مضحكة من {topic}",
                "أجمل {topic} شاهدتها",
                "لن تصدق ما حدث في {topic}"
            ],
            "motivational": [
                "كيف غيّر {topic} حياتي",
                "قصة نجاح مذهلة في {topic}",
                "تحدي {topic} - النتائج صادمة",
                "من الصفر إلى القمة في {topic}"
            ]
        }
        
        self.cta_templates = [
            "اشترك ليصلك المزيد!",
            "لا تنس الإعجاب والاشتراك",
            "شاركنا رأيك في التعليقات",
            "فعّل الجرس ليصلك كل جديد",
            "شارك الفيديو مع أصدقائك"
        ]
        
        logger.info("تم تهيئة مولد المحتوى الذكي")
    
    def analyze_video_content(self, video_path: str, 
                            captions: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """تحليل محتوى الفيديو لاستخراج المواضيع والمشاعر"""
        try:
            logger.info(f"بدء تحليل محتوى الفيديو: {video_path}")
            
            analysis = {
                "topics": [],
                "sentiment": {},
                "keywords": [],
                "content_type": "general",
                "duration_category": "short"
            }
            
            # تحليل النص إذا كان متوفراً
            if captions:
                text_content = " ".join([caption["text"] for caption in captions])
                analysis.update(self._analyze_text_content(text_content))
            
            # تحليل الفيديو باستخدام Google Cloud
            if self.gc_client.is_available():
                video_analysis = self.gc_client.analyze_video_content(video_path)
                if video_analysis:
                    analysis.update(self._process_video_analysis(video_analysis))
            
            # تحديد فئة المدة
            from core.video_processor import VideoProcessor
            processor = VideoProcessor()
            video_info = processor.get_video_info(video_path)
            duration = video_info.get('duration', 0)
            
            if duration < 60:
                analysis["duration_category"] = "short"
            elif duration < 300:
                analysis["duration_category"] = "medium"
            else:
                analysis["duration_category"] = "long"
            
            logger.info("تم تحليل محتوى الفيديو بنجاح")
            return analysis
            
        except Exception as e:
            logger.error(f"خطأ في تحليل محتوى الفيديو: {e}")
            return {}
    
    def _analyze_text_content(self, text: str) -> Dict[str, Any]:
        """تحليل المحتوى النصي"""
        analysis = {}
        
        try:
            # تحليل المشاعر
            if self.hf_client.api_key:
                sentiment = self.hf_client.analyze_sentiment(text)
                if sentiment:
                    analysis["sentiment"] = sentiment
            
            # استخراج الكلمات المفتاحية
            keywords = self._extract_keywords(text)
            analysis["keywords"] = keywords
            
            # تحديد نوع المحتوى
            content_type = self._classify_content_type(text)
            analysis["content_type"] = content_type
            
        except Exception as e:
            logger.error(f"خطأ في تحليل النص: {e}")
        
        return analysis
    
    def _process_video_analysis(self, video_analysis: Dict) -> Dict[str, Any]:
        """معالجة نتائج تحليل الفيديو"""
        processed = {}
        
        try:
            # استخراج المواضيع من التسميات
            labels = video_analysis.get("labels", [])
            topics = []
            
            for label in labels[:10]:  # أخذ أفضل 10 تسميات
                if label.get("confidence", 0) > 0.5:
                    topics.append({
                        "topic": label["description"],
                        "confidence": label["confidence"]
                    })
            
            processed["topics"] = topics
            
            # تحليل المشاهد
            shots = video_analysis.get("shots", [])
            if shots:
                processed["scene_count"] = len(shots)
                processed["average_scene_duration"] = sum(
                    shot["end_time"] - shot["start_time"] for shot in shots
                ) / len(shots) if shots else 0
            
        except Exception as e:
            logger.error(f"خطأ في معالجة تحليل الفيديو: {e}")
        
        return processed
    
    def _extract_keywords(self, text: str) -> List[str]:
        """استخراج الكلمات المفتاحية من النص"""
        keywords = []
        
        try:
            # تنظيف النص
            cleaned_text = re.sub(r'[^\w\s]', ' ', text)
            words = cleaned_text.split()
            
            # إزالة الكلمات الشائعة (stop words)
            stop_words = {
                'في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'ذلك', 'تلك',
                'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with'
            }
            
            # حساب تكرار الكلمات
            word_freq = {}
            for word in words:
                word = word.lower().strip()
                if len(word) > 2 and word not in stop_words:
                    word_freq[word] = word_freq.get(word, 0) + 1
            
            # ترتيب الكلمات حسب التكرار
            sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
            keywords = [word for word, freq in sorted_words[:10] if freq > 1]
            
        except Exception as e:
            logger.error(f"خطأ في استخراج الكلمات المفتاحية: {e}")
        
        return keywords
    
    def _classify_content_type(self, text: str) -> str:
        """تصنيف نوع المحتوى"""
        text_lower = text.lower()
        
        # كلمات مفتاحية لكل نوع محتوى
        educational_keywords = ['تعلم', 'كيفية', 'طريقة', 'شرح', 'دليل', 'خطوات']
        entertainment_keywords = ['مضحك', 'ممتع', 'مسلي', 'كوميدي', 'ترفيه']
        motivational_keywords = ['نجح', 'تحدي', 'إنجاز', 'هدف', 'حلم', 'طموح']
        
        educational_score = sum(1 for keyword in educational_keywords if keyword in text_lower)
        entertainment_score = sum(1 for keyword in entertainment_keywords if keyword in text_lower)
        motivational_score = sum(1 for keyword in motivational_keywords if keyword in text_lower)
        
        if educational_score >= entertainment_score and educational_score >= motivational_score:
            return "educational"
        elif entertainment_score >= motivational_score:
            return "entertainment"
        elif motivational_score > 0:
            return "motivational"
        else:
            return "general"
    
    def generate_video_title(self, analysis: Dict[str, Any], 
                           custom_topic: Optional[str] = None) -> List[str]:
        """توليد عناوين للفيديو"""
        titles = []
        
        try:
            content_type = analysis.get("content_type", "general")
            topics = analysis.get("topics", [])
            keywords = analysis.get("keywords", [])
            
            # تحديد الموضوع الرئيسي
            main_topic = custom_topic
            if not main_topic and topics:
                main_topic = topics[0]["topic"]
            elif not main_topic and keywords:
                main_topic = keywords[0]
            else:
                main_topic = "المحتوى"
            
            # استخدام القوالب
            templates = self.title_templates.get(content_type, self.title_templates["educational"])
            
            for template in templates[:3]:  # أخذ أفضل 3 قوالب
                if "{topic}" in template:
                    title = template.format(topic=main_topic)
                    titles.append(title)
            
            # توليد عناوين باستخدام الذكاء الاصطناعي
            if self.hf_client.api_key:
                ai_title = self.hf_client.generate_video_title(
                    f"فيديو عن {main_topic} من نوع {content_type}"
                )
                if ai_title:
                    titles.append(ai_title)
            
            # إضافة عناوين مخصصة بناءً على المشاعر
            sentiment = analysis.get("sentiment", {})
            if sentiment.get("sentiment") == "إيجابي":
                titles.append(f"تجربة رائعة مع {main_topic}")
            elif sentiment.get("sentiment") == "سلبي":
                titles.append(f"تحديات {main_topic} وكيفية التغلب عليها")
            
            logger.info(f"تم توليد {len(titles)} عنوان")
            
        except Exception as e:
            logger.error(f"خطأ في توليد العناوين: {e}")
        
        return titles[:5]  # إرجاع أفضل 5 عناوين
    
    def generate_video_description(self, analysis: Dict[str, Any], 
                                 title: str) -> str:
        """توليد وصف للفيديو"""
        try:
            topics = analysis.get("topics", [])
            keywords = analysis.get("keywords", [])
            content_type = analysis.get("content_type", "general")
            
            # بناء الوصف
            description_parts = []
            
            # المقدمة
            description_parts.append(f"في هذا الفيديو: {title}")
            
            # المحتوى الرئيسي
            if topics:
                main_topics = [topic["topic"] for topic in topics[:3]]
                description_parts.append(f"نتناول: {', '.join(main_topics)}")
            
            # الكلمات المفتاحية
            if keywords:
                description_parts.append(f"الكلمات المفتاحية: {', '.join(keywords[:5])}")
            
            # دعوة للعمل
            if content_type == "educational":
                description_parts.append("لا تنس الاشتراك للمزيد من المحتوى التعليمي!")
            elif content_type == "entertainment":
                description_parts.append("استمتع بالمشاهدة ولا تنس الإعجاب!")
            else:
                description_parts.append("شاركنا رأيك في التعليقات!")
            
            description = "\n\n".join(description_parts)
            
            logger.info("تم توليد وصف الفيديو")
            return description
            
        except Exception as e:
            logger.error(f"خطأ في توليد الوصف: {e}")
            return ""
    
    def generate_cta_suggestions(self, analysis: Dict[str, Any]) -> List[str]:
        """توليد اقتراحات لعبارات الحث على اتخاذ إجراء"""
        ctas = []
        
        try:
            content_type = analysis.get("content_type", "general")
            sentiment = analysis.get("sentiment", {})
            
            # عبارات أساسية
            ctas.extend(self.cta_templates[:3])
            
            # عبارات مخصصة حسب نوع المحتوى
            if content_type == "educational":
                ctas.extend([
                    "هل تعلمت شيئاً جديداً؟ شاركنا!",
                    "اطرح أسئلتك في التعليقات",
                    "شارك الفيديو مع من يحتاج هذه المعلومات"
                ])
            elif content_type == "entertainment":
                ctas.extend([
                    "أي جزء أعجبك أكثر؟",
                    "شارك الضحكة مع أصدقائك",
                    "اكتب تعليقك المضحك"
                ])
            elif content_type == "motivational":
                ctas.extend([
                    "ما هو هدفك التالي؟",
                    "شاركنا قصة نجاحك",
                    "ابدأ رحلتك اليوم!"
                ])
            
            # عبارات حسب المشاعر
            if sentiment.get("sentiment") == "إيجابي":
                ctas.append("انشر الإيجابية - شارك الفيديو!")
            
            # توليد عبارات باستخدام الذكاء الاصطناعي
            if self.hf_client.api_key:
                topics = analysis.get("topics", [])
                if topics:
                    main_topic = topics[0]["topic"]
                    ai_cta = self.hf_client.generate_cta(main_topic)
                    if ai_cta:
                        ctas.append(ai_cta)
            
            logger.info(f"تم توليد {len(ctas)} اقتراح CTA")
            
        except Exception as e:
            logger.error(f"خطأ في توليد عبارات الحث: {e}")
        
        return ctas[:8]  # إرجاع أفضل 8 اقتراحات
    
    def generate_hashtags(self, analysis: Dict[str, Any]) -> List[str]:
        """توليد هاشتاغات للفيديو"""
        hashtags = []
        
        try:
            keywords = analysis.get("keywords", [])
            topics = analysis.get("topics", [])
            content_type = analysis.get("content_type", "general")
            
            # هاشتاغات من الكلمات المفتاحية
            for keyword in keywords[:5]:
                hashtag = f"#{keyword.replace(' ', '_')}"
                hashtags.append(hashtag)
            
            # هاشتاغات من المواضيع
            for topic in topics[:3]:
                topic_name = topic["topic"].replace(' ', '_')
                hashtag = f"#{topic_name}"
                if hashtag not in hashtags:
                    hashtags.append(hashtag)
            
            # هاشتاغات عامة حسب نوع المحتوى
            general_hashtags = {
                "educational": ["#تعليم", "#تعلم", "#معلومات"],
                "entertainment": ["#ترفيه", "#مضحك", "#ممتع"],
                "motivational": ["#تحفيز", "#نجاح", "#إلهام"],
                "general": ["#فيديو", "#محتوى", "#مشاركة"]
            }
            
            hashtags.extend(general_hashtags.get(content_type, general_hashtags["general"]))
            
            # إزالة التكرارات
            unique_hashtags = list(dict.fromkeys(hashtags))
            
            logger.info(f"تم توليد {len(unique_hashtags)} هاشتاغ")
            
        except Exception as e:
            logger.error(f"خطأ في توليد الهاشتاغات: {e}")
        
        return unique_hashtags[:15]  # إرجاع أفضل 15 هاشتاغ
    
    def save_content_suggestions(self, suggestions: Dict[str, Any], 
                               output_path: str) -> bool:
        """حفظ اقتراحات المحتوى"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(suggestions, f, ensure_ascii=False, indent=2)
            
            logger.info(f"تم حفظ اقتراحات المحتوى: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في حفظ الاقتراحات: {e}")
            return False
