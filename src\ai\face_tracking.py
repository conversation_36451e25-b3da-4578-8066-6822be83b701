"""
وحدة تتبع الوجه التلقائي
Automatic face tracking module
"""

import cv2
import numpy as np
import logging
from typing import Optional, Dict, List, Tuple, Any
from pathlib import Path
import json

from config.settings import AppSettings
from utils.file_utils import FileManager
from core.video_processor import VideoProcessor

logger = logging.getLogger(__name__)

class FaceTracker:
    """متتبع الوجوه في الفيديو"""
    
    def __init__(self):
        self.file_manager = FileManager()
        self.video_processor = VideoProcessor()
        
        # تحميل نماذج كشف الوجه
        self.face_cascade = None
        self.face_detector = None
        self._load_face_detectors()
        
        logger.info("تم تهيئة متتبع الوجوه")
    
    def _load_face_detectors(self):
        """تحميل نماذج كشف الوجه"""
        try:
            # تحميل Haar Cascade (مدمج مع OpenCV)
            self.face_cascade = cv2.CascadeClassifier(
                cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
            )
            
            # محاولة تحميل DNN face detector (أكثر دقة)
            try:
                # يمكن تحميل نموذج DNN من OpenCV
                net_path = AppSettings.ASSETS_DIR / "models" / "opencv_face_detector_uint8.pb"
                config_path = AppSettings.ASSETS_DIR / "models" / "opencv_face_detector.pbtxt"
                
                if net_path.exists() and config_path.exists():
                    self.face_detector = cv2.dnn.readNetFromTensorflow(
                        str(net_path), str(config_path)
                    )
                    logger.info("تم تحميل نموذج DNN لكشف الوجوه")
                else:
                    logger.info("نموذج DNN غير متوفر، سيتم استخدام Haar Cascade")
                    
            except Exception as e:
                logger.warning(f"لا يمكن تحميل نموذج DNN: {e}")
                
        except Exception as e:
            logger.error(f"خطأ في تحميل نماذج كشف الوجه: {e}")
    
    def detect_faces_in_frame(self, frame: np.ndarray, 
                             method: str = "haar") -> List[Dict]:
        """كشف الوجوه في إطار واحد"""
        faces = []
        
        try:
            if method == "dnn" and self.face_detector is not None:
                faces = self._detect_faces_dnn(frame)
            else:
                faces = self._detect_faces_haar(frame)
                
        except Exception as e:
            logger.error(f"خطأ في كشف الوجوه: {e}")
        
        return faces
    
    def _detect_faces_haar(self, frame: np.ndarray) -> List[Dict]:
        """كشف الوجوه باستخدام Haar Cascade"""
        faces = []
        
        try:
            if self.face_cascade is None:
                return faces
            
            # تحويل إلى رمادي
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # كشف الوجوه
            face_rects = self.face_cascade.detectMultiScale(
                gray,
                scaleFactor=1.1,
                minNeighbors=5,
                minSize=(30, 30)
            )
            
            # تحويل النتائج إلى تنسيق موحد
            for (x, y, w, h) in face_rects:
                face_info = {
                    "x": int(x),
                    "y": int(y),
                    "width": int(w),
                    "height": int(h),
                    "confidence": 0.8,  # ثقة افتراضية لـ Haar
                    "center_x": int(x + w/2),
                    "center_y": int(y + h/2)
                }
                faces.append(face_info)
                
        except Exception as e:
            logger.error(f"خطأ في Haar Cascade: {e}")
        
        return faces
    
    def _detect_faces_dnn(self, frame: np.ndarray) -> List[Dict]:
        """كشف الوجوه باستخدام DNN"""
        faces = []
        
        try:
            if self.face_detector is None:
                return faces
            
            h, w = frame.shape[:2]
            
            # إعداد الإدخال للشبكة
            blob = cv2.dnn.blobFromImage(
                frame, 1.0, (300, 300), [104, 117, 123]
            )
            
            self.face_detector.setInput(blob)
            detections = self.face_detector.forward()
            
            # معالجة النتائج
            for i in range(detections.shape[2]):
                confidence = detections[0, 0, i, 2]
                
                if confidence > 0.5:  # حد الثقة
                    box = detections[0, 0, i, 3:7] * np.array([w, h, w, h])
                    x, y, x1, y1 = box.astype("int")
                    
                    face_info = {
                        "x": int(x),
                        "y": int(y),
                        "width": int(x1 - x),
                        "height": int(y1 - y),
                        "confidence": float(confidence),
                        "center_x": int((x + x1) / 2),
                        "center_y": int((y + y1) / 2)
                    }
                    faces.append(face_info)
                    
        except Exception as e:
            logger.error(f"خطأ في DNN: {e}")
        
        return faces
    
    def track_faces_in_video(self, video_path: str, 
                           sample_rate: int = 30) -> Optional[Dict]:
        """تتبع الوجوه في الفيديو كاملاً"""
        try:
            logger.info(f"بدء تتبع الوجوه في الفيديو: {video_path}")
            
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                logger.error("لا يمكن فتح الفيديو")
                return None
            
            # معلومات الفيديو
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = total_frames / fps
            
            tracking_data = {
                "video_info": {
                    "fps": fps,
                    "total_frames": total_frames,
                    "duration": duration
                },
                "faces_timeline": [],
                "face_statistics": {}
            }
            
            frame_count = 0
            faces_per_frame = []
            
            # معالجة الإطارات
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # معالجة كل إطار nth حسب sample_rate
                if frame_count % sample_rate == 0:
                    timestamp = frame_count / fps
                    faces = self.detect_faces_in_frame(frame)
                    
                    frame_data = {
                        "timestamp": timestamp,
                        "frame_number": frame_count,
                        "faces": faces,
                        "face_count": len(faces)
                    }
                    
                    tracking_data["faces_timeline"].append(frame_data)
                    faces_per_frame.append(len(faces))
                
                frame_count += 1
                
                # تحديث التقدم
                if frame_count % 100 == 0:
                    progress = (frame_count / total_frames) * 100
                    logger.info(f"تقدم التتبع: {progress:.1f}%")
            
            cap.release()
            
            # حساب الإحصائيات
            tracking_data["face_statistics"] = self._calculate_face_statistics(
                faces_per_frame, tracking_data["faces_timeline"]
            )
            
            logger.info("تم تتبع الوجوه بنجاح")
            return tracking_data
            
        except Exception as e:
            logger.error(f"خطأ في تتبع الوجوه: {e}")
            return None
    
    def _calculate_face_statistics(self, faces_per_frame: List[int], 
                                 timeline: List[Dict]) -> Dict:
        """حساب إحصائيات الوجوه"""
        stats = {
            "total_frames_analyzed": len(faces_per_frame),
            "frames_with_faces": sum(1 for count in faces_per_frame if count > 0),
            "average_faces_per_frame": np.mean(faces_per_frame) if faces_per_frame else 0,
            "max_faces_in_frame": max(faces_per_frame) if faces_per_frame else 0,
            "face_presence_percentage": 0,
            "dominant_face_regions": []
        }
        
        if len(faces_per_frame) > 0:
            stats["face_presence_percentage"] = (
                stats["frames_with_faces"] / stats["total_frames_analyzed"]
            ) * 100
        
        # تحليل المناطق المهيمنة للوجوه
        face_positions = []
        for frame_data in timeline:
            for face in frame_data["faces"]:
                face_positions.append((face["center_x"], face["center_y"]))
        
        if face_positions:
            stats["dominant_face_regions"] = self._find_dominant_regions(face_positions)
        
        return stats
    
    def _find_dominant_regions(self, positions: List[Tuple[int, int]]) -> List[Dict]:
        """العثور على المناطق المهيمنة للوجوه"""
        regions = []
        
        try:
            if not positions:
                return regions
            
            # تجميع المواقع إلى مناطق
            positions_array = np.array(positions)
            
            # استخدام K-means لتجميع المواقع
            from sklearn.cluster import KMeans
            
            n_clusters = min(3, len(positions))  # حد أقصى 3 مناطق
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            clusters = kmeans.fit_predict(positions_array)
            
            # تحليل كل منطقة
            for i in range(n_clusters):
                cluster_positions = positions_array[clusters == i]
                
                if len(cluster_positions) > 0:
                    center = np.mean(cluster_positions, axis=0)
                    region_info = {
                        "center_x": int(center[0]),
                        "center_y": int(center[1]),
                        "frequency": len(cluster_positions),
                        "percentage": (len(cluster_positions) / len(positions)) * 100
                    }
                    regions.append(region_info)
            
            # ترتيب حسب التكرار
            regions.sort(key=lambda x: x["frequency"], reverse=True)
            
        except ImportError:
            logger.warning("مكتبة scikit-learn غير متوفرة لتحليل المناطق")
        except Exception as e:
            logger.error(f"خطأ في تحليل المناطق: {e}")
        
        return regions
    
    def create_face_focused_video(self, video_path: str, output_path: str,
                                target_width: int = 1080, target_height: int = 1920,
                                padding_factor: float = 1.5) -> bool:
        """إنشاء فيديو مركز على الوجه"""
        try:
            logger.info(f"بدء إنشاء فيديو مركز على الوجه: {video_path}")
            
            # تتبع الوجوه أولاً
            tracking_data = self.track_faces_in_video(video_path, sample_rate=10)
            if not tracking_data:
                logger.error("فشل في تتبع الوجوه")
                return False
            
            # العثور على المنطقة المهيمنة
            stats = tracking_data["face_statistics"]
            if not stats["dominant_face_regions"]:
                logger.warning("لم يتم العثور على وجوه كافية للتركيز")
                return False
            
            dominant_region = stats["dominant_face_regions"][0]
            focus_x = dominant_region["center_x"]
            focus_y = dominant_region["center_y"]
            
            # حساب منطقة القص
            crop_width = int(target_width / padding_factor)
            crop_height = int(target_height / padding_factor)
            
            # إنشاء فلتر FFmpeg للقص والتكبير
            crop_filter = f"crop={crop_width}:{crop_height}:{focus_x - crop_width//2}:{focus_y - crop_height//2}"
            scale_filter = f"scale={target_width}:{target_height}"
            video_filter = f"{crop_filter},{scale_filter}"
            
            # تطبيق الفلتر باستخدام FFmpeg
            success = self._apply_face_focus_filter(
                video_path, output_path, video_filter
            )
            
            if success:
                logger.info("تم إنشاء الفيديو المركز على الوجه بنجاح")
            else:
                logger.error("فشل في إنشاء الفيديو المركز على الوجه")
            
            return success
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء الفيديو المركز على الوجه: {e}")
            return False
    
    def _apply_face_focus_filter(self, input_path: str, output_path: str, 
                               video_filter: str) -> bool:
        """تطبيق فلتر التركيز على الوجه"""
        try:
            import subprocess
            
            cmd = [
                AppSettings.FFMPEG_PATH,
                '-i', input_path,
                '-vf', video_filter,
                '-c:a', 'copy',
                '-y',
                output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                return True
            else:
                logger.error(f"خطأ في FFmpeg: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في تطبيق الفلتر: {e}")
            return False
    
    def save_tracking_data(self, tracking_data: Dict, output_path: str) -> bool:
        """حفظ بيانات التتبع"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(tracking_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"تم حفظ بيانات التتبع: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات التتبع: {e}")
            return False
    
    def load_tracking_data(self, input_path: str) -> Optional[Dict]:
        """تحميل بيانات التتبع"""
        try:
            with open(input_path, 'r', encoding='utf-8') as f:
                tracking_data = json.load(f)
            
            logger.info(f"تم تحميل بيانات التتبع: {input_path}")
            return tracking_data
            
        except Exception as e:
            logger.error(f"خطأ في تحميل بيانات التتبع: {e}")
            return None
