#!/usr/bin/env python3
"""
اختبار سريع لمحلل البثوث المباشرة
Quick test for the livestream analyzer
"""

import sys
import os
from pathlib import Path

# إضافة مجلد src إلى مسار Python
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_analyzer():
    """اختبار محلل البثوث المباشرة"""
    
    print("🎬 اختبار محلل البثوث المباشرة")
    print("=" * 40)
    
    try:
        # اختبار الاستيراد
        print("1. اختبار الاستيراد...")
        
        from ai.livestream_analyzer import LivestreamAnalyzer
        print("✅ تم استيراد LivestreamAnalyzer بنجاح")
        
        from core.video_processor import VideoProcessor
        print("✅ تم استيراد VideoProcessor بنجاح")
        
        from utils.file_utils import FileManager
        print("✅ تم استيراد FileManager بنجاح")
        
        # اختبار إنشاء الكائنات
        print("\n2. اختبار إنشاء الكائنات...")
        
        analyzer = LivestreamAnalyzer()
        print("✅ تم إنشاء LivestreamAnalyzer بنجاح")
        
        processor = VideoProcessor()
        print("✅ تم إنشاء VideoProcessor بنجاح")
        
        file_manager = FileManager()
        print("✅ تم إنشاء FileManager بنجاح")
        
        # اختبار الوظائف الأساسية
        print("\n3. اختبار الوظائف الأساسية...")
        
        # اختبار تصنيف اللحظات
        test_text = "wow this is insane omg no way"
        moment_type, confidence = analyzer._classify_moment_type(test_text)
        print(f"✅ تصنيف النص: '{test_text}' -> {moment_type} (ثقة: {confidence:.2f})")
        
        # اختبار إنشاء قطع التحليل
        chunks = analyzer._create_analysis_chunks(3600)  # ساعة واحدة
        print(f"✅ تقسيم ساعة واحدة إلى {len(chunks)} قطعة")
        
        # اختبار معلومات النظام
        try:
            from utils.performance_optimizer import PerformanceOptimizer
            optimizer = PerformanceOptimizer()
            system_info = optimizer.get_system_info()
            print(f"✅ معلومات النظام: {system_info['memory']['total_gb']:.1f}GB RAM")
        except Exception as e:
            print(f"⚠️  تحذير: لا يمكن الحصول على معلومات النظام: {e}")
        
        # اختبار FFmpeg
        print("\n4. اختبار FFmpeg...")
        
        ffmpeg_available = processor._check_ffmpeg()
        if ffmpeg_available:
            print("✅ FFmpeg متوفر ويعمل")
        else:
            print("❌ FFmpeg غير متوفر أو لا يعمل")
            return False
        
        # اختبار إنشاء الملفات المؤقتة
        print("\n5. اختبار إدارة الملفات...")
        
        temp_file = file_manager.create_temp_file(suffix='.txt')
        if temp_file.exists():
            print("✅ تم إنشاء ملف مؤقت بنجاح")
            temp_file.unlink()  # حذف الملف المؤقت
            print("✅ تم حذف الملف المؤقت بنجاح")
        else:
            print("❌ فشل في إنشاء ملف مؤقت")
            return False
        
        print("\n" + "=" * 40)
        print("✅ جميع الاختبارات نجحت!")
        print("محلل البثوث المباشرة جاهز للاستخدام")
        print("=" * 40)
        
        # عرض معلومات الاستخدام
        print("\nكيفية الاستخدام:")
        print("1. تشغيل الواجهة الرسومية:")
        print("   python run_livestream_analyzer.py")
        print("\n2. تشغيل التطبيق الكامل:")
        print("   python main.py")
        print("\n3. اختبار مع فيديو:")
        print("   ضع ملف فيديو في المجلد واستخدم الواجهة")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("تأكد من تثبيت جميع المتطلبات:")
        print("pip install -r requirements.txt")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_keywords():
    """اختبار الكلمات المفتاحية"""
    
    print("\n🔍 اختبار الكلمات المفتاحية:")
    print("-" * 30)
    
    try:
        from ai.livestream_analyzer import LivestreamAnalyzer
        analyzer = LivestreamAnalyzer()
        
        test_cases = [
            ("wow this is amazing omg", "exciting"),
            ("haha that's so funny lol", "funny"),
            ("what the hell just happened", "shocking"),
            ("this is normal gameplay", "none"),
            ("يا إلهي هذا مجنون", "exciting"),
            ("ضحك مضحك جداً", "funny"),
            ("صدمة غير متوقعة", "shocking")
        ]
        
        for text, expected in test_cases:
            moment_type, confidence = analyzer._classify_moment_type(text)
            status = "✅" if moment_type == expected or (expected == "none" and confidence < 0.5) else "⚠️"
            print(f"{status} '{text}' -> {moment_type} ({confidence:.2f})")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الكلمات المفتاحية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🧪 اختبار شامل لمحلل البثوث المباشرة")
    print("=" * 50)
    
    # اختبار المحلل
    analyzer_ok = test_analyzer()
    
    if analyzer_ok:
        # اختبار الكلمات المفتاحية
        keywords_ok = test_keywords()
        
        if keywords_ok:
            print("\n🎉 جميع الاختبارات نجحت!")
            print("الأداة جاهزة للاستخدام")
            return 0
        else:
            print("\n⚠️  بعض اختبارات الكلمات المفتاحية فشلت")
            return 1
    else:
        print("\n❌ فشل في الاختبارات الأساسية")
        return 1

if __name__ == "__main__":
    exit_code = main()
    
    print(f"\nكود الخروج: {exit_code}")
    if exit_code == 0:
        print("يمكنك الآن تشغيل: python run_livestream_analyzer.py")
    
    sys.exit(exit_code)
