"""
محسن الأداء
Performance optimizer for video processing
"""

import logging
import psutil
import threading
import queue
import time
from typing import Optional, Dict, List, Any, Callable
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing as mp
from pathlib import Path

from config.settings import AppSettings

logger = logging.getLogger(__name__)

class PerformanceOptimizer:
    """محسن الأداء للمعالجة المتوازية والذاكرة"""
    
    def __init__(self):
        self.cpu_count = mp.cpu_count()
        self.memory_info = psutil.virtual_memory()
        self.optimal_workers = min(self.cpu_count, 4)  # حد أقصى 4 عمليات متوازية
        
        # إعدادات الذاكرة
        self.memory_threshold = 0.8  # 80% من الذاكرة
        self.chunk_size = 1024 * 1024  # 1MB chunks
        
        logger.info(f"تم تهيئة محسن الأداء - CPU: {self.cpu_count}, RAM: {self.memory_info.total // (1024**3)}GB")
    
    def get_system_info(self) -> Dict[str, Any]:
        """الحصول على معلومات النظام"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                "cpu": {
                    "count": self.cpu_count,
                    "usage_percent": cpu_percent,
                    "frequency": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
                },
                "memory": {
                    "total_gb": memory.total / (1024**3),
                    "available_gb": memory.available / (1024**3),
                    "usage_percent": memory.percent,
                    "free_gb": memory.free / (1024**3)
                },
                "disk": {
                    "total_gb": disk.total / (1024**3),
                    "free_gb": disk.free / (1024**3),
                    "usage_percent": (disk.used / disk.total) * 100
                }
            }
        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات النظام: {e}")
            return {}
    
    def check_system_resources(self) -> Dict[str, bool]:
        """فحص موارد النظام"""
        try:
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            checks = {
                "memory_ok": memory.percent < (self.memory_threshold * 100),
                "disk_space_ok": disk.free > (2 * 1024**3),  # 2GB مساحة فارغة
                "cpu_available": psutil.cpu_percent(interval=0.1) < 90
            }
            
            return checks
        except Exception as e:
            logger.error(f"خطأ في فحص موارد النظام: {e}")
            return {"memory_ok": False, "disk_space_ok": False, "cpu_available": False}
    
    def optimize_worker_count(self, task_type: str = "cpu_bound") -> int:
        """تحسين عدد العمليات المتوازية"""
        try:
            system_checks = self.check_system_resources()
            
            if not system_checks["memory_ok"]:
                # تقليل العمليات إذا كانت الذاكرة منخفضة
                return max(1, self.optimal_workers // 2)
            
            if task_type == "cpu_bound":
                # للمهام المعتمدة على المعالج
                return min(self.cpu_count, self.optimal_workers)
            elif task_type == "io_bound":
                # للمهام المعتمدة على الإدخال/الإخراج
                return min(self.cpu_count * 2, 8)
            else:
                return self.optimal_workers
                
        except Exception as e:
            logger.error(f"خطأ في تحسين عدد العمليات: {e}")
            return 2  # قيمة افتراضية آمنة
    
    def process_in_chunks(self, data: List[Any], chunk_size: Optional[int] = None,
                         processor_func: Callable = None) -> List[Any]:
        """معالجة البيانات على دفعات"""
        if chunk_size is None:
            chunk_size = max(1, len(data) // self.optimal_workers)
        
        chunks = [data[i:i + chunk_size] for i in range(0, len(data), chunk_size)]
        results = []
        
        try:
            with ThreadPoolExecutor(max_workers=self.optimal_workers) as executor:
                futures = [executor.submit(processor_func, chunk) for chunk in chunks]
                
                for future in futures:
                    try:
                        result = future.result(timeout=300)  # 5 دقائق timeout
                        if result:
                            results.extend(result if isinstance(result, list) else [result])
                    except Exception as e:
                        logger.error(f"خطأ في معالجة الدفعة: {e}")
            
            return results
            
        except Exception as e:
            logger.error(f"خطأ في المعالجة المتوازية: {e}")
            return []
    
    def parallel_video_processing(self, tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """معالجة فيديوهات متعددة بالتوازي"""
        try:
            worker_count = self.optimize_worker_count("cpu_bound")
            results = []
            
            def process_video_task(task):
                """معالجة مهمة فيديو واحدة"""
                try:
                    task_type = task.get("type")
                    input_path = task.get("input_path")
                    output_path = task.get("output_path")
                    params = task.get("params", {})
                    
                    # استيراد معالج الفيديو
                    from core.video_processor import VideoProcessor
                    processor = VideoProcessor()
                    
                    start_time = time.time()
                    success = False
                    
                    if task_type == "trim":
                        success = processor.trim_video(
                            input_path, output_path,
                            params.get("start_time", 0),
                            params.get("end_time", 10)
                        )
                    elif task_type == "resize":
                        success = processor.resize_video(
                            input_path, output_path,
                            params.get("width", 1920),
                            params.get("height", 1080),
                            params.get("quality", "medium")
                        )
                    elif task_type == "add_text":
                        success = processor.add_text_overlay(
                            input_path, output_path,
                            params.get("text", ""),
                            params.get("position", "center"),
                            params.get("font_size", 24),
                            params.get("color", "white")
                        )
                    
                    end_time = time.time()
                    
                    return {
                        "task": task,
                        "success": success,
                        "duration": end_time - start_time,
                        "output_path": output_path if success else None
                    }
                    
                except Exception as e:
                    logger.error(f"خطأ في معالجة المهمة: {e}")
                    return {
                        "task": task,
                        "success": False,
                        "error": str(e),
                        "duration": 0
                    }
            
            # تنفيذ المهام بالتوازي
            with ThreadPoolExecutor(max_workers=worker_count) as executor:
                futures = [executor.submit(process_video_task, task) for task in tasks]
                
                for future in futures:
                    try:
                        result = future.result(timeout=600)  # 10 دقائق لكل مهمة
                        results.append(result)
                    except Exception as e:
                        logger.error(f"خطأ في تنفيذ المهمة المتوازية: {e}")
                        results.append({
                            "success": False,
                            "error": str(e),
                            "duration": 0
                        })
            
            logger.info(f"تم تنفيذ {len(results)} مهمة بالتوازي")
            return results
            
        except Exception as e:
            logger.error(f"خطأ في المعالجة المتوازية للفيديو: {e}")
            return []
    
    def optimize_memory_usage(self, operation: str) -> Dict[str, Any]:
        """تحسين استخدام الذاكرة"""
        try:
            memory_before = psutil.virtual_memory()
            
            # إعدادات تحسين الذاكرة حسب نوع العملية
            optimizations = {
                "video_processing": {
                    "chunk_size": min(self.chunk_size, memory_before.available // 4),
                    "buffer_size": 8192,
                    "max_workers": max(1, self.optimal_workers // 2)
                },
                "ai_processing": {
                    "batch_size": 1,
                    "max_sequence_length": 512,
                    "use_cpu": memory_before.available < (4 * 1024**3)  # أقل من 4GB
                },
                "file_operations": {
                    "read_chunk_size": 64 * 1024,  # 64KB
                    "write_buffer": 128 * 1024,    # 128KB
                    "concurrent_files": max(1, self.optimal_workers // 2)
                }
            }
            
            config = optimizations.get(operation, optimizations["video_processing"])
            
            # إضافة معلومات الذاكرة
            config.update({
                "memory_available_gb": memory_before.available / (1024**3),
                "memory_usage_percent": memory_before.percent,
                "recommended_max_file_size_mb": memory_before.available // (1024**2) // 4
            })
            
            return config
            
        except Exception as e:
            logger.error(f"خطأ في تحسين الذاكرة: {e}")
            return {"chunk_size": 1024*1024, "buffer_size": 8192, "max_workers": 2}
    
    def monitor_performance(self, duration: int = 60) -> Dict[str, Any]:
        """مراقبة الأداء لفترة محددة"""
        try:
            logger.info(f"بدء مراقبة الأداء لمدة {duration} ثانية")
            
            cpu_samples = []
            memory_samples = []
            start_time = time.time()
            
            while time.time() - start_time < duration:
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                
                cpu_samples.append(cpu_percent)
                memory_samples.append(memory.percent)
                
                time.sleep(1)
            
            # حساب الإحصائيات
            avg_cpu = sum(cpu_samples) / len(cpu_samples)
            max_cpu = max(cpu_samples)
            avg_memory = sum(memory_samples) / len(memory_samples)
            max_memory = max(memory_samples)
            
            performance_data = {
                "duration": duration,
                "cpu": {
                    "average": round(avg_cpu, 2),
                    "maximum": round(max_cpu, 2),
                    "samples": len(cpu_samples)
                },
                "memory": {
                    "average": round(avg_memory, 2),
                    "maximum": round(max_memory, 2),
                    "samples": len(memory_samples)
                },
                "recommendations": []
            }
            
            # توصيات بناءً على النتائج
            if avg_cpu > 80:
                performance_data["recommendations"].append(
                    "استخدام المعالج مرتفع - فكر في تقليل العمليات المتوازية"
                )
            
            if avg_memory > 85:
                performance_data["recommendations"].append(
                    "استخدام الذاكرة مرتفع - فكر في معالجة ملفات أصغر"
                )
            
            if max_cpu > 95:
                performance_data["recommendations"].append(
                    "المعالج وصل للحد الأقصى - قد تحتاج لتحسين الكود"
                )
            
            logger.info("تم انتهاء مراقبة الأداء")
            return performance_data
            
        except Exception as e:
            logger.error(f"خطأ في مراقبة الأداء: {e}")
            return {}
    
    def cleanup_temp_files(self, max_age_hours: int = 24) -> Dict[str, Any]:
        """تنظيف الملفات المؤقتة القديمة"""
        try:
            temp_dir = AppSettings.TEMP_DIR
            current_time = time.time()
            max_age_seconds = max_age_hours * 3600
            
            cleaned_files = []
            total_size_freed = 0
            errors = []
            
            if not temp_dir.exists():
                return {"cleaned_files": 0, "size_freed_mb": 0, "errors": []}
            
            for file_path in temp_dir.rglob("*"):
                try:
                    if file_path.is_file():
                        file_age = current_time - file_path.stat().st_mtime
                        
                        if file_age > max_age_seconds:
                            file_size = file_path.stat().st_size
                            file_path.unlink()
                            
                            cleaned_files.append(str(file_path))
                            total_size_freed += file_size
                            
                except Exception as e:
                    errors.append(f"خطأ في حذف {file_path}: {e}")
            
            # حذف المجلدات الفارغة
            for dir_path in temp_dir.rglob("*"):
                try:
                    if dir_path.is_dir() and not any(dir_path.iterdir()):
                        dir_path.rmdir()
                except Exception as e:
                    errors.append(f"خطأ في حذف المجلد {dir_path}: {e}")
            
            cleanup_result = {
                "cleaned_files": len(cleaned_files),
                "size_freed_mb": round(total_size_freed / (1024**2), 2),
                "errors": errors,
                "temp_dir": str(temp_dir)
            }
            
            logger.info(f"تم تنظيف {len(cleaned_files)} ملف، تم توفير {cleanup_result['size_freed_mb']}MB")
            return cleanup_result
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف الملفات المؤقتة: {e}")
            return {"cleaned_files": 0, "size_freed_mb": 0, "errors": [str(e)]}
    
    def get_optimization_recommendations(self) -> List[str]:
        """الحصول على توصيات تحسين الأداء"""
        try:
            recommendations = []
            system_info = self.get_system_info()
            
            # توصيات الذاكرة
            memory_usage = system_info.get("memory", {}).get("usage_percent", 0)
            if memory_usage > 80:
                recommendations.append("استخدام الذاكرة مرتفع - أغلق التطبيقات غير الضرورية")
            elif memory_usage < 50:
                recommendations.append("يمكن زيادة عدد العمليات المتوازية لتحسين الأداء")
            
            # توصيات المعالج
            cpu_usage = system_info.get("cpu", {}).get("usage_percent", 0)
            if cpu_usage > 90:
                recommendations.append("استخدام المعالج مرتفع - قلل من العمليات المتوازية")
            
            # توصيات التخزين
            disk_usage = system_info.get("disk", {}).get("usage_percent", 0)
            if disk_usage > 90:
                recommendations.append("مساحة التخزين منخفضة - احذف الملفات غير الضرورية")
            
            # توصيات عامة
            if self.cpu_count >= 8:
                recommendations.append("نظامك يدعم المعالجة المتوازية المتقدمة")
            
            total_memory_gb = system_info.get("memory", {}).get("total_gb", 0)
            if total_memory_gb >= 16:
                recommendations.append("ذاكرة كافية لمعالجة ملفات فيديو كبيرة")
            elif total_memory_gb < 8:
                recommendations.append("ذاكرة محدودة - استخدم ملفات فيديو أصغر")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على توصيات التحسين: {e}")
            return ["خطأ في تحليل النظام"]
