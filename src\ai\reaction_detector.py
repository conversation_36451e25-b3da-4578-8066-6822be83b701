"""
كاشف ردات الفعل المتقدم
Advanced Reaction Detection System

يكتشف ردات فعل الأشخاص في الفيديو ويحدد التوقيت الأمثل للقطع
"""

import cv2
import numpy as np
import logging
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path
from collections import deque
import statistics

# محاولة استيراد MediaPipe
try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
except ImportError:
    MEDIAPIPE_AVAILABLE = False
    mp = None

# محاولة استيراد المحلل المتقدم
try:
    from ai.advanced_emotion_analyzer import AdvancedEmotionAnalyzer
    ADVANCED_ANALYZER_AVAILABLE = True
except ImportError:
    ADVANCED_ANALYZER_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class ReactionMoment:
    """لحظة ردة فعل"""
    start_time: float
    peak_time: float
    end_time: float
    intensity: float
    emotion_type: str
    confidence: float
    face_changes: Dict[str, float]
    body_movement: float
    audio_changes: Dict[str, float]

class ReactionDetector:
    """كاشف ردات الفعل المتقدم"""
    
    def __init__(self):
        # إعداد MediaPipe للوجوه (إذا كان متوفراً)
        if MEDIAPIPE_AVAILABLE:
            self.mp_face_mesh = mp.solutions.face_mesh
            self.mp_pose = mp.solutions.pose
            self.mp_hands = mp.solutions.hands

            # إعداد كاشفات الوجه والجسم
            try:
                self.face_mesh = self.mp_face_mesh.FaceMesh(
                    static_image_mode=False,
                    max_num_faces=5,  # كشف حتى 5 وجوه للردات الجماعية
                    refine_landmarks=True,
                    min_detection_confidence=0.4,  # حساسية أعلى
                    min_tracking_confidence=0.4
                )

                self.pose = self.mp_pose.Pose(
                    static_image_mode=False,
                    model_complexity=1,
                    smooth_landmarks=True,
                    min_detection_confidence=0.5,
                    min_tracking_confidence=0.5
                )
                logger.info("تم تهيئة MediaPipe بنجاح")
            except Exception as e:
                logger.warning(f"فشل في تهيئة MediaPipe: {e}")
                self.face_mesh = None
                self.pose = None
        else:
            logger.warning("MediaPipe غير متوفر - سيتم استخدام كشف أساسي")
            self.mp_face_mesh = None
            self.mp_pose = None
            self.mp_hands = None
            self.face_mesh = None
            self.pose = None

            # إعداد كاشف وجه أساسي باستخدام OpenCV
            try:
                self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
                logger.info("تم تهيئة كاشف الوجه الأساسي (OpenCV)")
            except Exception as e:
                logger.warning(f"فشل في تهيئة كاشف الوجه الأساسي: {e}")
                self.face_cascade = None
        
        # إعدادات التحليل
        self.reaction_window = 15.0  # نافذة 15 ثانية للبحث عن ردة فعل
        self.min_reaction_duration = 0.5  # أقل مدة لردة فعل
        self.max_reaction_duration = 12.0  # أقصى مدة لردة فعل

        # عتبات الكشف
        self.emotion_threshold = 0.25  # أكثر حساسية للمشاعر
        self.movement_threshold = 0.3   # أكثر حساسية للحركة
        self.intensity_threshold = 0.4  # أكثر حساسية للشدة
        self.crowd_reaction_threshold = 0.6  # عتبة ردة فعل جماعية
        
        # تخزين البيانات المؤقتة
        self.face_history = deque(maxlen=30)  # آخر 30 إطار
        self.movement_history = deque(maxlen=30)
        self.emotion_history = deque(maxlen=30)
        self.crowd_history = deque(maxlen=30)  # تاريخ ردات الفعل الجماعية

        # أنواع ردات الفعل المختلفة
        self.reaction_types = {
            'individual_surprise': 'مفاجأة فردية',
            'individual_excitement': 'إثارة فردية',
            'individual_joy': 'فرح فردي',
            'individual_shock': 'صدمة فردية',
            'crowd_applause': 'تصفيق جماعي',
            'crowd_cheer': 'هتاف جماعي',
            'crowd_gasp': 'تنهد جماعي',
            'crowd_laughter': 'ضحك جماعي',
            'phone_recording': 'تسجيل بالهواتف',
            'pointing_gestures': 'إشارات وإيماءات',
            'jumping_celebration': 'قفز احتفالي',
            'standing_ovation': 'وقوف تصفيق'
        }

        # تهيئة المحلل المتقدم إذا كان متوفراً
        if ADVANCED_ANALYZER_AVAILABLE:
            try:
                self.advanced_analyzer = AdvancedEmotionAnalyzer()
                logger.info("تم تهيئة المحلل المتقدم بنجاح")
            except Exception as e:
                logger.warning(f"فشل في تهيئة المحلل المتقدم: {e}")
                self.advanced_analyzer = None
        else:
            self.advanced_analyzer = None

        logger.info("تم تهيئة كاشف ردات الفعل المتقدم")

    def detect_reactions_in_video(self, video_path: str, trigger_moments: List[Tuple[float, float]]) -> List[ReactionMoment]:
        """كشف ردات الفعل في الفيديو بعد اللحظات المحفزة"""
        try:
            reactions = []
            
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            for trigger_start, trigger_end in trigger_moments:
                logger.info(f"البحث عن ردة فعل بعد اللحظة: {trigger_start:.1f}-{trigger_end:.1f}s")
                
                # تحليل النافذة بعد اللحظة المحفزة
                reaction_start = trigger_end
                reaction_end = min(trigger_end + self.reaction_window, 
                                 cap.get(cv2.CAP_PROP_FRAME_COUNT) / fps)
                
                reaction = self._analyze_reaction_window(
                    cap, fps, reaction_start, reaction_end, trigger_start
                )
                
                if reaction:
                    reactions.append(reaction)
            
            cap.release()
            return reactions
            
        except Exception as e:
            logger.error(f"خطأ في كشف ردات الفعل: {e}")
            return []

    def _analyze_reaction_window(self, cap: cv2.VideoCapture, fps: float, 
                                start_time: float, end_time: float, 
                                trigger_time: float) -> Optional[ReactionMoment]:
        """تحليل نافذة زمنية للبحث عن ردة فعل"""
        try:
            # الانتقال لنقطة البداية
            start_frame = int(start_time * fps)
            cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
            
            # تخزين البيانات
            frame_data = []
            current_time = start_time
            
            while current_time < end_time:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # تحليل الإطار
                frame_analysis = self._analyze_frame(frame, current_time)
                if frame_analysis:
                    frame_data.append(frame_analysis)
                
                current_time = cap.get(cv2.CAP_PROP_POS_FRAMES) / fps
            
            # البحث عن ردة فعل في البيانات
            reaction = self._find_reaction_in_data(frame_data, trigger_time)
            return reaction
            
        except Exception as e:
            logger.error(f"خطأ في تحليل نافذة ردة الفعل: {e}")
            return None

    def _analyze_frame(self, frame: np.ndarray, timestamp: float) -> Optional[Dict[str, Any]]:
        """تحليل إطار واحد"""
        try:
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            analysis = {
                'timestamp': timestamp,
                'face_emotions': {},
                'body_movement': 0.0,
                'face_landmarks': None,
                'pose_landmarks': None,
                'crowd_reactions': {},
                'special_gestures': {},
                'multiple_faces': 0,
                'phone_detection': 0
            }
            
            # تحليل الوجه والمشاعر
            if MEDIAPIPE_AVAILABLE and self.face_mesh:
                face_results = self.face_mesh.process(rgb_frame)
                if face_results.multi_face_landmarks:
                    analysis['multiple_faces'] = len(face_results.multi_face_landmarks)

                    # تحليل كل وجه
                    all_emotions = {}
                    for i, face_landmarks in enumerate(face_results.multi_face_landmarks):
                        emotions = self._analyze_facial_emotions(face_landmarks, frame.shape)
                        # دمج المشاعر من جميع الوجوه
                        for emotion, value in emotions.items():
                            if emotion in all_emotions:
                                all_emotions[emotion] = max(all_emotions[emotion], value)
                            else:
                                all_emotions[emotion] = value

                    analysis['face_emotions'] = all_emotions
                    analysis['face_landmarks'] = face_results.multi_face_landmarks[0]  # الوجه الأول

                    # كشف ردات الفعل الجماعية
                    if analysis['multiple_faces'] >= 2:
                        crowd_reactions = self._analyze_crowd_reactions(face_results.multi_face_landmarks, frame.shape)
                        analysis['crowd_reactions'] = crowd_reactions
            else:
                # استخدام كشف وجه أساسي
                analysis.update(self._basic_face_detection(frame))
            
            # تحليل حركة الجسم
            if MEDIAPIPE_AVAILABLE and self.pose:
                pose_results = self.pose.process(rgb_frame)
                if pose_results.pose_landmarks:
                    movement = self._analyze_body_movement(pose_results.pose_landmarks)
                    analysis['body_movement'] = movement
                    analysis['pose_landmarks'] = pose_results.pose_landmarks

                    # تحليل الإيماءات الخاصة
                    special_gestures = self._analyze_special_gestures(pose_results.pose_landmarks)
                    analysis['special_gestures'] = special_gestures
            else:
                # تحليل حركة أساسي
                analysis['body_movement'] = self._basic_movement_detection(frame)
                analysis['special_gestures'] = {}

            # كشف الهواتف والتسجيل
            phone_detection = self._detect_phone_recording(rgb_frame)
            analysis['phone_detection'] = phone_detection
            
            return analysis
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الإطار: {e}")
            return None

    def _analyze_facial_emotions(self, face_landmarks, frame_shape) -> Dict[str, float]:
        """تحليل المشاعر من ملامح الوجه"""
        try:
            emotions = {
                'surprise': 0.0,
                'joy': 0.0,
                'shock': 0.0,
                'excitement': 0.0,
                'confusion': 0.0
            }
            
            # تحويل النقاط إلى إحداثيات
            h, w = frame_shape[:2]
            landmarks = []
            for landmark in face_landmarks.landmark:
                x = int(landmark.x * w)
                y = int(landmark.y * h)
                landmarks.append([x, y])
            
            landmarks = np.array(landmarks)
            
            # تحليل العينين (نقاط 33, 7, 163, 144, 145, 153, 154, 155, 133, 173, 157, 158, 159, 160, 161, 246)
            left_eye = landmarks[[33, 7, 163, 144, 145, 153]]
            right_eye = landmarks[[362, 382, 381, 380, 374, 373]]
            
            # حساب انفتاح العينين
            left_eye_openness = self._calculate_eye_openness(left_eye)
            right_eye_openness = self._calculate_eye_openness(right_eye)
            avg_eye_openness = (left_eye_openness + right_eye_openness) / 2
            
            # العيون المفتوحة جداً = مفاجأة/صدمة
            if avg_eye_openness > 0.7:
                emotions['surprise'] = min(avg_eye_openness, 1.0)
                emotions['shock'] = min(avg_eye_openness * 0.8, 1.0)
            
            # تحليل الفم (نقاط 61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318)
            mouth_landmarks = landmarks[[61, 84, 17, 314, 405, 320]]
            mouth_openness = self._calculate_mouth_openness(mouth_landmarks)
            
            # الفم المفتوح = مفاجأة/إثارة
            if mouth_openness > 0.5:
                emotions['surprise'] += mouth_openness * 0.6
                emotions['excitement'] = mouth_openness * 0.8
            
            # تحليل الحواجب
            left_eyebrow = landmarks[[70, 63, 105, 66, 107]]
            right_eyebrow = landmarks[[296, 334, 293, 300, 276]]
            eyebrow_raise = self._calculate_eyebrow_raise(left_eyebrow, right_eyebrow, landmarks)
            
            # الحواجب المرفوعة = مفاجأة
            if eyebrow_raise > 0.3:
                emotions['surprise'] += eyebrow_raise * 0.5
            
            # تطبيع القيم
            for emotion in emotions:
                emotions[emotion] = min(emotions[emotion], 1.0)
            
            return emotions
            
        except Exception as e:
            logger.error(f"خطأ في تحليل المشاعر: {e}")
            return {}

    def _calculate_eye_openness(self, eye_landmarks) -> float:
        """حساب مدى انفتاح العين"""
        try:
            # حساب المسافة العمودية بين الجفون
            vertical_dist = np.linalg.norm(eye_landmarks[1] - eye_landmarks[5])
            # حساب المسافة الأفقية
            horizontal_dist = np.linalg.norm(eye_landmarks[0] - eye_landmarks[3])
            
            # نسبة الانفتاح
            if horizontal_dist > 0:
                openness = vertical_dist / horizontal_dist
                return min(openness * 3, 1.0)  # تطبيع
            return 0.0
        except:
            return 0.0

    def _calculate_mouth_openness(self, mouth_landmarks) -> float:
        """حساب مدى انفتاح الفم"""
        try:
            # حساب المسافة بين الشفة العلوية والسفلية
            vertical_dist = np.linalg.norm(mouth_landmarks[1] - mouth_landmarks[4])
            # حساب عرض الفم
            horizontal_dist = np.linalg.norm(mouth_landmarks[0] - mouth_landmarks[3])
            
            if horizontal_dist > 0:
                openness = vertical_dist / horizontal_dist
                return min(openness * 2, 1.0)
            return 0.0
        except:
            return 0.0

    def _calculate_eyebrow_raise(self, left_eyebrow, right_eyebrow, all_landmarks) -> float:
        """حساب مدى رفع الحواجب"""
        try:
            # نقاط مرجعية للوجه
            nose_tip = all_landmarks[1]  # طرف الأنف
            
            # حساب متوسط ارتفاع الحواجب
            left_avg_y = np.mean(left_eyebrow[:, 1])
            right_avg_y = np.mean(right_eyebrow[:, 1])
            avg_eyebrow_y = (left_avg_y + right_avg_y) / 2
            
            # المسافة من الحواجب إلى الأنف (كلما قلت = حواجب مرفوعة)
            distance_to_nose = abs(avg_eyebrow_y - nose_tip[1])
            
            # تطبيع القيمة (قيمة أعلى = حواجب أكثر ارتفاعاً)
            normalized = max(0, (50 - distance_to_nose) / 50)
            return min(normalized, 1.0)
        except:
            return 0.0

    def _analyze_body_movement(self, pose_landmarks) -> float:
        """تحليل حركة الجسم"""
        try:
            # نقاط مهمة للحركة
            key_points = [
                pose_landmarks.landmark[11],  # كتف يسار
                pose_landmarks.landmark[12],  # كتف يمين
                pose_landmarks.landmark[15],  # يد يسار
                pose_landmarks.landmark[16],  # يد يمين
                pose_landmarks.landmark[0],   # أنف (رأس)
            ]
            
            # حساب التغيير من الإطار السابق
            if len(self.movement_history) > 0:
                prev_points = self.movement_history[-1]
                movement = 0.0
                
                for i, point in enumerate(key_points):
                    if i < len(prev_points):
                        curr_pos = np.array([point.x, point.y])
                        prev_pos = np.array([prev_points[i].x, prev_points[i].y])
                        movement += np.linalg.norm(curr_pos - prev_pos)
                
                movement = min(movement * 10, 1.0)  # تطبيع
            else:
                movement = 0.0
            
            # حفظ النقاط الحالية
            self.movement_history.append(key_points)
            
            return movement
            
        except Exception as e:
            logger.error(f"خطأ في تحليل حركة الجسم: {e}")
            return 0.0

    def _find_reaction_in_data(self, frame_data: List[Dict[str, Any]], 
                              trigger_time: float) -> Optional[ReactionMoment]:
        """البحث عن ردة فعل في بيانات الإطارات"""
        try:
            if len(frame_data) < 10:  # حاجة لبيانات كافية
                return None
            
            # حساب متوسطات متحركة للمشاعر والحركة
            emotion_scores = []
            movement_scores = []
            timestamps = []
            
            for data in frame_data:
                timestamp = data['timestamp']
                timestamps.append(timestamp)
                
                # حساب نقاط المشاعر (فردية)
                emotions = data['face_emotions']
                emotion_score = (
                    emotions.get('surprise', 0) * 0.4 +
                    emotions.get('shock', 0) * 0.3 +
                    emotions.get('excitement', 0) * 0.2 +
                    emotions.get('joy', 0) * 0.1
                )

                # إضافة نقاط ردات الفعل الجماعية
                crowd_reactions = data.get('crowd_reactions', {})
                crowd_score = (
                    crowd_reactions.get('collective_surprise', 0) * 0.5 +
                    crowd_reactions.get('collective_joy', 0) * 0.3 +
                    crowd_reactions.get('collective_excitement', 0) * 0.2
                )

                # إضافة نقاط الإيماءات الخاصة
                gestures = data.get('special_gestures', {})
                gesture_score = (
                    gestures.get('applause', 0) * 0.4 +
                    gestures.get('arms_raised', 0) * 0.3 +
                    gestures.get('jumping', 0) * 0.2 +
                    gestures.get('pointing', 0) * 0.1
                )

                # إضافة نقاط تسجيل الهواتف
                phone_score = data.get('phone_detection', 0) * 0.3

                # النقاط الإجمالية للمشاعر
                total_emotion_score = emotion_score + crowd_score * 1.5 + gesture_score + phone_score
                emotion_scores.append(total_emotion_score)

                # نقاط الحركة
                base_movement = data['body_movement']
                gesture_movement = sum(gestures.values()) * 0.3
                total_movement = base_movement + gesture_movement
                movement_scores.append(min(total_movement, 1.0))
            
            # البحث عن ذروة ردة الفعل
            combined_scores = []
            for i in range(len(emotion_scores)):
                combined = emotion_scores[i] * 0.7 + movement_scores[i] * 0.3
                combined_scores.append(combined)
            
            # العثور على أعلى نقطة
            if not combined_scores:
                return None
                
            max_score = max(combined_scores)
            if max_score < self.intensity_threshold:
                return None
            
            peak_index = combined_scores.index(max_score)
            peak_time = timestamps[peak_index]
            
            # تحديد بداية ونهاية ردة الفعل
            reaction_start = self._find_reaction_start(combined_scores, peak_index, timestamps)
            reaction_end = self._find_reaction_end(combined_scores, peak_index, timestamps)
            
            # التحقق من صحة المدة
            duration = reaction_end - reaction_start
            if duration < self.min_reaction_duration or duration > self.max_reaction_duration:
                return None
            
            # تحديد نوع المشاعر السائد
            peak_data = frame_data[peak_index]
            peak_emotions = peak_data['face_emotions']
            crowd_reactions = peak_data.get('crowd_reactions', {})
            gestures = peak_data.get('special_gestures', {})

            # تحديد النوع الأقوى
            all_reactions = {}

            # ردات فعل فردية
            for emotion, value in peak_emotions.items():
                all_reactions[f'individual_{emotion}'] = value

            # ردات فعل جماعية
            for reaction, value in crowd_reactions.items():
                all_reactions[f'crowd_{reaction}'] = value * 1.2  # وزن أعلى للردات الجماعية

            # إيماءات خاصة
            for gesture, value in gestures.items():
                if value > 0.5:  # فقط الإيماءات القوية
                    all_reactions[f'gesture_{gesture}'] = value

            # تسجيل بالهواتف
            phone_detection = peak_data.get('phone_detection', 0)
            if phone_detection > 0.4:
                all_reactions['phone_recording'] = phone_detection

            # اختيار أقوى نوع
            if all_reactions:
                emotion_type = max(all_reactions.items(), key=lambda x: x[1])[0]
            else:
                emotion_type = 'unknown'
            
            reaction = ReactionMoment(
                start_time=reaction_start,
                peak_time=peak_time,
                end_time=reaction_end,
                intensity=max_score,
                emotion_type=emotion_type,
                confidence=min(max_score * 1.2, 1.0),
                face_changes=peak_emotions,
                body_movement=movement_scores[peak_index],
                audio_changes={}  # سيتم إضافته لاحقاً
            )
            
            logger.info(f"تم اكتشاف ردة فعل: {reaction_start:.1f}-{reaction_end:.1f}s، النوع: {emotion_type}")
            return reaction
            
        except Exception as e:
            logger.error(f"خطأ في البحث عن ردة فعل: {e}")
            return None

    def _find_reaction_start(self, scores: List[float], peak_index: int, timestamps: List[float]) -> float:
        """العثور على بداية ردة الفعل"""
        threshold = scores[peak_index] * 0.3
        
        for i in range(peak_index, -1, -1):
            if scores[i] < threshold:
                return timestamps[min(i + 1, len(timestamps) - 1)]
        
        return timestamps[0]

    def _find_reaction_end(self, scores: List[float], peak_index: int, timestamps: List[float]) -> float:
        """العثور على نهاية ردة الفعل"""
        threshold = scores[peak_index] * 0.3
        
        for i in range(peak_index, len(scores)):
            if scores[i] < threshold:
                return timestamps[i]
        
        return timestamps[-1]

    def extend_highlight_with_reaction(self, original_start: float, original_end: float,
                                     reactions: List[ReactionMoment]) -> Tuple[float, float]:
        """توسيع اللقطة الأصلية لتشمل ردة الفعل"""
        try:
            # البحث عن ردة فعل قريبة من نهاية اللقطة الأصلية
            best_reaction = None
            min_gap = float('inf')
            
            for reaction in reactions:
                gap = reaction.start_time - original_end
                if 0 <= gap <= 3.0 and gap < min_gap:  # ردة فعل خلال 3 ثوانٍ
                    best_reaction = reaction
                    min_gap = gap
            
            if best_reaction:
                # توسيع اللقطة لتشمل ردة الفعل
                new_end = best_reaction.end_time + 0.5  # إضافة نصف ثانية بعد انتهاء ردة الفعل
                logger.info(f"تم توسيع اللقطة من {original_end:.1f}s إلى {new_end:.1f}s لتشمل ردة الفعل")
                return original_start, new_end
            
            return original_start, original_end
            
        except Exception as e:
            logger.error(f"خطأ في توسيع اللقطة: {e}")
            return original_start, original_end

    def _analyze_crowd_reactions(self, multi_face_landmarks, frame_shape) -> Dict[str, float]:
        """تحليل ردات الفعل الجماعية"""
        try:
            crowd_reactions = {
                'collective_surprise': 0.0,
                'collective_joy': 0.0,
                'collective_excitement': 0.0,
                'synchronized_movement': 0.0
            }

            if len(multi_face_landmarks) < 2:
                return crowd_reactions

            # تحليل تزامن المشاعر
            emotions_list = []
            for face_landmarks in multi_face_landmarks:
                emotions = self._analyze_facial_emotions(face_landmarks, frame_shape)
                emotions_list.append(emotions)

            # حساب التزامن في المشاعر
            if emotions_list:
                # مفاجأة جماعية
                surprise_values = [e.get('surprise', 0) for e in emotions_list]
                if len([s for s in surprise_values if s > 0.4]) >= len(surprise_values) * 0.6:
                    crowd_reactions['collective_surprise'] = np.mean(surprise_values)

                # فرح جماعي
                joy_values = [e.get('joy', 0) for e in emotions_list]
                if len([j for j in joy_values if j > 0.4]) >= len(joy_values) * 0.6:
                    crowd_reactions['collective_joy'] = np.mean(joy_values)

                # إثارة جماعية
                excitement_values = [e.get('excitement', 0) for e in emotions_list]
                if len([ex for ex in excitement_values if ex > 0.4]) >= len(excitement_values) * 0.6:
                    crowd_reactions['collective_excitement'] = np.mean(excitement_values)

            return crowd_reactions

        except Exception as e:
            logger.error(f"خطأ في تحليل ردات الفعل الجماعية: {e}")
            return {}

    def _analyze_special_gestures(self, pose_landmarks) -> Dict[str, float]:
        """تحليل الإيماءات والحركات الخاصة"""
        try:
            gestures = {
                'applause': 0.0,           # تصفيق
                'pointing': 0.0,           # إشارة
                'jumping': 0.0,            # قفز
                'arms_raised': 0.0,        # رفع الذراعين
                'phone_holding': 0.0,      # حمل الهاتف
                'standing_up': 0.0,        # الوقوف
                'leaning_forward': 0.0     # الانحناء للأمام
            }

            # نقاط مهمة
            left_wrist = pose_landmarks.landmark[15]
            right_wrist = pose_landmarks.landmark[16]
            left_shoulder = pose_landmarks.landmark[11]
            right_shoulder = pose_landmarks.landmark[12]
            left_hip = pose_landmarks.landmark[23]
            right_hip = pose_landmarks.landmark[24]
            nose = pose_landmarks.landmark[0]

            # كشف التصفيق (حركة اليدين المتقابلة)
            hands_distance = abs(left_wrist.x - right_wrist.x)
            if hands_distance < 0.1 and left_wrist.y < left_shoulder.y and right_wrist.y < right_shoulder.y:
                gestures['applause'] = min(1.0 - hands_distance * 5, 1.0)

            # كشف رفع الذراعين (احتفال)
            left_arm_raised = left_wrist.y < left_shoulder.y - 0.1
            right_arm_raised = right_wrist.y < right_shoulder.y - 0.1
            if left_arm_raised and right_arm_raised:
                gestures['arms_raised'] = 0.8
            elif left_arm_raised or right_arm_raised:
                gestures['arms_raised'] = 0.5

            # كشف الإشارة (يد واحدة مرفوعة)
            if (left_wrist.y < left_shoulder.y - 0.05 and right_wrist.y > right_shoulder.y) or \
               (right_wrist.y < right_shoulder.y - 0.05 and left_wrist.y > left_shoulder.y):
                gestures['pointing'] = 0.7

            # كشف حمل الهاتف (يد قريبة من الوجه)
            left_to_face = abs(left_wrist.x - nose.x) + abs(left_wrist.y - nose.y)
            right_to_face = abs(right_wrist.x - nose.x) + abs(right_wrist.y - nose.y)
            if left_to_face < 0.15 or right_to_face < 0.15:
                gestures['phone_holding'] = 0.6

            # كشف الانحناء للأمام (اهتمام)
            hip_center_y = (left_hip.y + right_hip.y) / 2
            if nose.y < hip_center_y - 0.3:  # الرأس أعلى من المعتاد
                gestures['leaning_forward'] = 0.5

            # حفظ في التاريخ للمقارنة
            if len(self.movement_history) > 5:
                # مقارنة مع الإطارات السابقة لكشف القفز
                try:
                    prev_hip_y = np.mean([
                        (point[2].y + point[3].y) / 2 for point in self.movement_history[-5:]
                        if len(point) > 3 and hasattr(point[2], 'y') and hasattr(point[3], 'y')
                    ])
                    current_hip_y = hip_center_y
                    if prev_hip_y - current_hip_y > 0.05:  # ارتفاع مفاجئ
                        gestures['jumping'] = min((prev_hip_y - current_hip_y) * 10, 1.0)
                except:
                    pass  # تجاهل الأخطاء في حساب القفز

            return gestures

        except Exception as e:
            logger.error(f"خطأ في تحليل الإيماءات الخاصة: {e}")
            return {}

    def _detect_phone_recording(self, frame: np.ndarray) -> float:
        """كشف تسجيل بالهواتف"""
        try:
            # تحويل إلى رمادي
            gray = cv2.cvtColor(frame, cv2.COLOR_RGB2GRAY)

            # كشف المستطيلات (شاشات الهواتف)
            # استخدام كشف الحواف
            edges = cv2.Canny(gray, 50, 150)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            phone_score = 0.0
            for contour in contours:
                # حساب نسبة العرض إلى الارتفاع
                x, y, w, h = cv2.boundingRect(contour)
                if w > 20 and h > 30:  # حجم مناسب
                    aspect_ratio = w / h
                    if 0.4 < aspect_ratio < 0.8:  # نسبة شاشة الهاتف
                        area = cv2.contourArea(contour)
                        if area > 500:  # مساحة مناسبة
                            phone_score += 0.2

            return min(phone_score, 1.0)

        except Exception as e:
            logger.error(f"خطأ في كشف الهواتف: {e}")
            return 0.0

    def _basic_face_detection(self, frame: np.ndarray) -> Dict[str, Any]:
        """كشف وجه أساسي باستخدام OpenCV"""
        try:
            analysis = {
                'face_emotions': {},
                'multiple_faces': 0,
                'crowd_reactions': {}
            }

            if self.face_cascade is None:
                return analysis

            # تحويل إلى رمادي
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            # كشف الوجوه
            faces = self.face_cascade.detectMultiScale(
                gray,
                scaleFactor=1.1,
                minNeighbors=5,
                minSize=(30, 30)
            )

            analysis['multiple_faces'] = len(faces)

            if len(faces) > 0:
                # تحليل بسيط للوجوه
                for (x, y, w, h) in faces:
                    face_roi = gray[y:y+h, x:x+w]

                    # تحليل بسيط للسطوع والتباين (مؤشر على التعبير)
                    brightness = np.mean(face_roi)
                    contrast = np.std(face_roi)

                    # تقدير بسيط للمشاعر
                    if contrast > 50:  # تباين عالي = تعبير قوي
                        analysis['face_emotions']['excitement'] = min(contrast / 100, 1.0)
                    if brightness > 120:  # سطوع عالي = ابتسامة محتملة
                        analysis['face_emotions']['joy'] = min((brightness - 120) / 50, 1.0)

                # ردة فعل جماعية بسيطة
                if len(faces) >= 2:
                    analysis['crowd_reactions']['collective_activity'] = min(len(faces) / 5.0, 1.0)

            return analysis

        except Exception as e:
            logger.error(f"خطأ في كشف الوجه الأساسي: {e}")
            return {'face_emotions': {}, 'multiple_faces': 0, 'crowd_reactions': {}}

    def _basic_movement_detection(self, frame: np.ndarray) -> float:
        """كشف حركة أساسي"""
        try:
            # حساب الحركة من الإطار السابق
            if len(self.movement_history) > 0:
                prev_frame = self.movement_history[-1]
                if prev_frame is not None and hasattr(prev_frame, 'shape') and prev_frame.shape == frame.shape:
                    # حساب الفرق بين الإطارات
                    diff = cv2.absdiff(frame, prev_frame)
                    gray_diff = cv2.cvtColor(diff, cv2.COLOR_BGR2GRAY)

                    # حساب مستوى الحركة
                    movement = np.mean(gray_diff) / 255.0

                    # حفظ الإطار الحالي
                    self.movement_history.append(frame.copy())

                    return min(movement * 5, 1.0)  # تطبيع

            # حفظ الإطار الأول
            self.movement_history.append(frame.copy())
            return 0.0

        except Exception as e:
            logger.error(f"خطأ في كشف الحركة الأساسي: {e}")
            return 0.0
