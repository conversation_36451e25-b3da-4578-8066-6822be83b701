"""
اختبارات وحدات الذكاء الاصطناعي
AI modules unit tests
"""

import unittest
import tempfile
import os
from pathlib import Path
import sys
from unittest.mock import Mock, patch, MagicMock

# إضافة مجلد src إلى مسار Python
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from ai.huggingface_client import HuggingFaceClient
from ai.google_cloud_client import GoogleCloudClient
from ai.ai_manager import AIManager
from ai.caption_generator import CaptionGenerator
from ai.content_generator import ContentGenerator
from ai.face_tracking import FaceTracker

class TestHuggingFaceClient(unittest.TestCase):
    """اختبارات عميل Hugging Face"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        # استخدام مفتاح وهمي للاختبار
        self.client = HuggingFaceClient(api_key="test_key")
    
    def test_client_initialization(self):
        """اختبار تهيئة العميل"""
        self.assertIsNotNone(self.client)
        self.assertEqual(self.client.api_key, "test_key")
        self.assertEqual(self.client.daily_requests, 0)
    
    def test_client_without_api_key(self):
        """اختبار العميل بدون مفتاح API"""
        client = HuggingFaceClient(api_key=None)
        self.assertIsNone(client.api_key)
    
    @patch('requests.Session.post')
    def test_speech_to_text_success(self, mock_post):
        """اختبار تحويل الكلام إلى نص بنجاح"""
        # محاكاة استجابة ناجحة
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"text": "مرحبا بك"}
        mock_post.return_value = mock_response
        
        # إنشاء ملف صوتي وهمي
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_audio:
            temp_audio.write(b"fake audio data")
            temp_audio_path = temp_audio.name
        
        try:
            result = self.client.speech_to_text(temp_audio_path)
            self.assertEqual(result, "مرحبا بك")
            self.assertEqual(self.client.daily_requests, 1)
        finally:
            os.unlink(temp_audio_path)
    
    @patch('requests.Session.post')
    def test_speech_to_text_failure(self, mock_post):
        """اختبار فشل تحويل الكلام إلى نص"""
        # محاكاة استجابة فاشلة
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"
        mock_post.return_value = mock_response
        
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_audio:
            temp_audio.write(b"fake audio data")
            temp_audio_path = temp_audio.name
        
        try:
            result = self.client.speech_to_text(temp_audio_path)
            self.assertIsNone(result)
        finally:
            os.unlink(temp_audio_path)
    
    def test_speech_to_text_nonexistent_file(self):
        """اختبار تحويل الكلام إلى نص مع ملف غير موجود"""
        result = self.client.speech_to_text("nonexistent_file.wav")
        self.assertIsNone(result)
    
    @patch('requests.Session.post')
    def test_translate_text_success(self, mock_post):
        """اختبار ترجمة النص بنجاح"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = [{"translation_text": "Hello"}]
        mock_post.return_value = mock_response
        
        result = self.client.translate_text("مرحبا", "ar", "en")
        self.assertEqual(result, "Hello")
    
    def test_translate_text_empty_input(self):
        """اختبار ترجمة نص فارغ"""
        result = self.client.translate_text("", "ar", "en")
        self.assertEqual(result, "")
    
    @patch('requests.Session.post')
    def test_analyze_sentiment_success(self, mock_post):
        """اختبار تحليل المشاعر بنجاح"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = [{"label": "POSITIVE", "score": 0.9}]
        mock_post.return_value = mock_response
        
        result = self.client.analyze_sentiment("هذا رائع!")
        self.assertIsNotNone(result)
        self.assertEqual(result["sentiment"], "إيجابي")
        self.assertEqual(result["confidence"], 0.9)
    
    def test_analyze_sentiment_empty_text(self):
        """اختبار تحليل المشاعر لنص فارغ"""
        result = self.client.analyze_sentiment("")
        self.assertIsNone(result)
    
    def test_usage_stats(self):
        """اختبار إحصائيات الاستخدام"""
        stats = self.client.get_usage_stats()
        self.assertIn("daily_requests", stats)
        self.assertIn("monthly_requests", stats)
        self.assertIn("daily_limit", stats)
        self.assertIn("monthly_limit", stats)
    
    def test_reset_daily_counter(self):
        """اختبار إعادة تعيين العداد اليومي"""
        self.client.daily_requests = 50
        self.client.reset_daily_counter()
        self.assertEqual(self.client.daily_requests, 0)

class TestGoogleCloudClient(unittest.TestCase):
    """اختبارات عميل Google Cloud"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.client = GoogleCloudClient()
    
    def test_client_initialization(self):
        """اختبار تهيئة العميل"""
        self.assertIsNotNone(self.client)
    
    def test_is_available_without_credentials(self):
        """اختبار توفر الخدمة بدون اعتمادات"""
        # بدون اعتمادات صحيحة، يجب أن تكون الخدمة غير متوفرة
        available = self.client.is_available()
        # قد تكون متوفرة أو غير متوفرة حسب البيئة
        self.assertIsInstance(available, bool)

class TestAIManager(unittest.TestCase):
    """اختبارات مدير الذكاء الاصطناعي"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        with patch('ai.ai_manager.HuggingFaceClient'), \
             patch('ai.ai_manager.GoogleCloudClient'), \
             patch('ai.ai_manager.VideoProcessor'), \
             patch('ai.ai_manager.FileManager'):
            self.ai_manager = AIManager()
    
    def test_manager_initialization(self):
        """اختبار تهيئة المدير"""
        self.assertIsNotNone(self.ai_manager)
        self.assertIn("huggingface", self.ai_manager.services_status)
        self.assertIn("google_cloud", self.ai_manager.services_status)
    
    def test_get_services_status(self):
        """اختبار الحصول على حالة الخدمات"""
        status = self.ai_manager.get_services_status()
        self.assertIsInstance(status, dict)
        self.assertIn("huggingface", status)
        self.assertIn("google_cloud", status)

class TestCaptionGenerator(unittest.TestCase):
    """اختبارات مولد التعليقات التوضيحية"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        with patch('ai.caption_generator.HuggingFaceClient'), \
             patch('ai.caption_generator.GoogleCloudClient'), \
             patch('ai.caption_generator.VideoProcessor'), \
             patch('ai.caption_generator.FileManager'):
            self.generator = CaptionGenerator()
    
    def test_generator_initialization(self):
        """اختبار تهيئة المولد"""
        self.assertIsNotNone(self.generator)
        self.assertEqual(self.generator.max_caption_length, 80)
        self.assertEqual(self.generator.max_caption_duration, 5.0)
    
    def test_clean_transcript(self):
        """اختبار تنظيف النص"""
        dirty_text = "مرحبا um هذا اختبار [noise] (background)"
        cleaned = self.generator._clean_transcript(dirty_text)
        self.assertNotIn("um", cleaned)
        self.assertNotIn("[noise]", cleaned)
        self.assertNotIn("(background)", cleaned)
    
    def test_split_into_sentences(self):
        """اختبار تقسيم النص إلى جمل"""
        text = "هذه الجملة الأولى. وهذه الجملة الثانية! وهذه الثالثة؟"
        sentences = self.generator._split_into_sentences(text)
        self.assertEqual(len(sentences), 3)
        self.assertIn("هذه الجملة الأولى", sentences[0])
    
    def test_split_long_sentence(self):
        """اختبار تقسيم الجملة الطويلة"""
        long_sentence = "هذه جملة طويلة جداً " * 20  # جملة طويلة
        parts = self.generator._split_long_sentence(long_sentence)
        self.assertGreater(len(parts), 1)
        for part in parts:
            self.assertLessEqual(len(part), self.generator.max_caption_length)
    
    def test_seconds_to_srt_time(self):
        """اختبار تحويل الثواني إلى تنسيق SRT"""
        srt_time = self.generator._seconds_to_srt_time(3661.5)  # 1:01:01.500
        self.assertEqual(srt_time, "01:01:01,500")
    
    def test_seconds_to_vtt_time(self):
        """اختبار تحويل الثواني إلى تنسيق VTT"""
        vtt_time = self.generator._seconds_to_vtt_time(3661.5)  # 1:01:01.500
        self.assertEqual(vtt_time, "01:01:01.500")

class TestContentGenerator(unittest.TestCase):
    """اختبارات مولد المحتوى"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        with patch('ai.content_generator.HuggingFaceClient'), \
             patch('ai.content_generator.GoogleCloudClient'):
            self.generator = ContentGenerator()
    
    def test_generator_initialization(self):
        """اختبار تهيئة المولد"""
        self.assertIsNotNone(self.generator)
        self.assertIn("educational", self.generator.title_templates)
        self.assertIn("entertainment", self.generator.title_templates)
    
    def test_extract_keywords(self):
        """اختبار استخراج الكلمات المفتاحية"""
        text = "هذا فيديو تعليمي عن البرمجة والتطوير والتقنية"
        keywords = self.generator._extract_keywords(text)
        self.assertIsInstance(keywords, list)
        # يجب أن تحتوي على كلمات مهمة وليس كلمات شائعة
        self.assertNotIn("هذا", keywords)
    
    def test_classify_content_type(self):
        """اختبار تصنيف نوع المحتوى"""
        educational_text = "تعلم كيفية البرمجة خطوة بخطوة"
        content_type = self.generator._classify_content_type(educational_text)
        self.assertEqual(content_type, "educational")
        
        entertainment_text = "فيديو مضحك ومسلي للغاية"
        content_type = self.generator._classify_content_type(entertainment_text)
        self.assertEqual(content_type, "entertainment")
    
    def test_generate_video_title(self):
        """اختبار توليد عناوين الفيديو"""
        analysis = {
            "content_type": "educational",
            "topics": [{"topic": "البرمجة", "confidence": 0.9}],
            "keywords": ["تعلم", "برمجة", "تطوير"]
        }
        
        titles = self.generator.generate_video_title(analysis)
        self.assertIsInstance(titles, list)
        self.assertGreater(len(titles), 0)
        # يجب أن تحتوي العناوين على الموضوع الرئيسي
        self.assertTrue(any("البرمجة" in title for title in titles))
    
    def test_generate_hashtags(self):
        """اختبار توليد الهاشتاغات"""
        analysis = {
            "keywords": ["تعلم", "برمجة"],
            "topics": [{"topic": "تطوير الويب", "confidence": 0.8}],
            "content_type": "educational"
        }
        
        hashtags = self.generator.generate_hashtags(analysis)
        self.assertIsInstance(hashtags, list)
        self.assertGreater(len(hashtags), 0)
        # يجب أن تبدأ بـ #
        self.assertTrue(all(tag.startswith("#") for tag in hashtags))

class TestFaceTracker(unittest.TestCase):
    """اختبارات متتبع الوجوه"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        with patch('ai.face_tracking.FileManager'), \
             patch('ai.face_tracking.VideoProcessor'):
            self.tracker = FaceTracker()
    
    def test_tracker_initialization(self):
        """اختبار تهيئة المتتبع"""
        self.assertIsNotNone(self.tracker)
    
    @patch('cv2.CascadeClassifier')
    def test_load_face_detectors(self, mock_cascade):
        """اختبار تحميل كاشفات الوجوه"""
        mock_cascade.return_value = Mock()
        self.tracker._load_face_detectors()
        # يجب أن يتم تحميل Haar Cascade على الأقل
        self.assertIsNotNone(self.tracker.face_cascade)

if __name__ == '__main__':
    # تشغيل الاختبارات
    unittest.main(verbosity=2)
