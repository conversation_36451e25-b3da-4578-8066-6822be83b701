# دليل المستخدم - Video Editor Pro

## مرحباً بك في Video Editor Pro

Video Editor Pro هو أداة تحرير فيديو احترافية ومجانية بالكامل، مصممة لتلبية احتياجات منشئي المحتوى. تعتمد الأداة على تقنيات الذكاء الاصطناعي والأدوات مفتوحة المصدر لتوفير تجربة تحرير متقدمة.

## 📋 جدول المحتويات

1. [التثبيت والإعداد](#التثبيت-والإعداد)
2. [البدء السريع](#البدء-السريع)
3. [واجهة المستخدم](#واجهة-المستخدم)
4. [المميزات الأساسية](#المميزات-الأساسية)
5. [ميزات الذكاء الاصطناعي](#ميزات-الذكاء-الاصطناعي)
6. [النشر والتصدير](#النشر-والتصدير)
7. [حل المشاكل](#حل-المشاكل)
8. [الأسئلة الشائعة](#الأسئلة-الشائعة)

## 🚀 التثبيت والإعداد

### متطلبات النظام

- **نظام التشغيل**: Windows 10/11, macOS 10.14+, أو Linux Ubuntu 18.04+
- **Python**: الإصدار 3.8 أو أحدث
- **الذاكرة**: 4GB RAM (8GB مُوصى به)
- **التخزين**: 2GB مساحة فارغة
- **الإنترنت**: مطلوب للميزات المدعومة بالذكاء الاصطناعي

### خطوات التثبيت

#### 1. تحميل المشروع
```bash
git clone https://github.com/your-username/video-editor-pro.git
cd video-editor-pro
```

#### 2. إنشاء البيئة الافتراضية
```bash
python -m venv venv

# Windows:
venv\Scripts\activate

# macOS/Linux:
source venv/bin/activate
```

#### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

#### 4. تثبيت FFmpeg

**Windows:**
1. قم بتحميل FFmpeg من [الموقع الرسمي](https://ffmpeg.org/download.html)
2. استخرج الملفات وأضف مجلد `bin` إلى متغير البيئة PATH

**macOS:**
```bash
brew install ffmpeg
```

**Linux:**
```bash
sudo apt update
sudo apt install ffmpeg
```

#### 5. إعداد مفاتيح API (اختياري)

انسخ ملف `.env.example` إلى `.env` وأضف مفاتيح API الخاصة بك:

```bash
cp .env.example .env
```

قم بتحرير ملف `.env` وأضف:
```
HUGGINGFACE_API_KEY=your_huggingface_api_key
GOOGLE_CLOUD_PROJECT_ID=your_project_id
GOOGLE_CLOUD_CREDENTIALS_PATH=path/to/credentials.json
```

## ⚡ البدء السريع

### تشغيل التطبيق
```bash
python main.py
```

### أول فيديو لك

1. **فتح فيديو**: انقر على "فتح فيديو" واختر ملف الفيديو
2. **قص الفيديو**: استخدم أداة "قص" لتحديد الجزء المطلوب
3. **إضافة نص**: انقر على "إضافة نص" لإضافة عنوان أو تعليق
4. **تصدير**: انقر على "تصدير" لحفظ الفيديو النهائي

## 🖥️ واجهة المستخدم

### النافذة الرئيسية

تتكون واجهة المستخدم من:

#### 1. شريط القوائم
- **ملف**: فتح، حفظ، تصدير الفيديوهات
- **تحرير**: عمليات التراجع والإعادة
- **ذكاء اصطناعي**: الميزات المدعومة بالذكاء الاصطناعي
- **مساعدة**: الدعم والمعلومات

#### 2. شريط الأدوات
أزرار سريعة للعمليات الشائعة:
- فتح فيديو
- حفظ المشروع
- قص الفيديو
- إضافة نص
- تحويل التنسيق
- تصدير الفيديو

#### 3. الشريط الجانبي
- **معلومات الفيديو**: عرض تفاصيل الملف المحمل
- **أدوات سريعة**: عمليات تحرير سريعة
- **ذكاء اصطناعي**: الميزات الذكية

#### 4. منطقة العمل الرئيسية
- **معاينة الفيديو**: عرض الفيديو الحالي
- **الجدول الزمني**: التحكم في تشغيل الفيديو
- **أزرار التحكم**: تشغيل، إيقاف، تقديم، إرجاع

#### 5. شريط الحالة
- عرض حالة العمليات الجارية
- شريط التقدم للعمليات الطويلة

## 🎬 المميزات الأساسية

### قص الفيديو

1. انقر على "قص" في شريط الأدوات
2. حدد وقت البداية والنهاية
3. انقر على "موافق" لتطبيق القص

**نصائح:**
- استخدم معاينة الفيديو لتحديد النقاط بدقة
- يمكن قص عدة مقاطع من نفس الفيديو

### إضافة النصوص

1. انقر على "إضافة نص"
2. اكتب النص المطلوب
3. اختر الموقع (أعلى، وسط، أسفل)
4. حدد حجم الخط واللون
5. انقر على "موافق"

**خيارات الموقع:**
- أعلى يسار/وسط/يمين
- وسط يسار/وسط/يمين
- أسفل يسار/وسط/يمين

### تحويل الفيديو

1. انقر على "تحويل"
2. اختر الدقة الجديدة أو استخدم الإعدادات المسبقة:
   - HD (1280x720)
   - Full HD (1920x1080)
   - عمودي (1080x1920) - مثالي لـ TikTok
   - مربع (1080x1080) - مثالي لـ Instagram
3. اختر مستوى الجودة
4. انقر على "موافق"

### تأثيرات الانتقال

- **الظهور التدريجي**: تأثير ظهور ناعم في بداية الفيديو
- **الاختفاء التدريجي**: تأثير اختفاء ناعم في نهاية الفيديو
- **التكبير**: تأثير تكبير ديناميكي على أجزاء محددة

## 🤖 ميزات الذكاء الاصطناعي

### القص التلقائي

يحلل الفيديو ويقترح أفضل المقاطع للمشاركة:

1. انقر على "قص تلقائي" في قائمة الذكاء الاصطناعي
2. حدد المدة المطلوبة للمقاطع
3. اختر عدد المقاطع المطلوبة
4. انتظر التحليل والنتائج

**يعتمد على:**
- تحليل الصوت لتحديد اللحظات المهمة
- كشف الوجوه والحركة
- تحليل المشاعر في الكلام

### تتبع الوجه التلقائي

يتتبع الوجوه ويحافظ عليها في مركز الشاشة:

1. انقر على "تتبع الوجه"
2. اختر نسبة العرض إلى الارتفاع المطلوبة
3. حدد مستوى التكبير
4. انتظر المعالجة

**مثالي لـ:**
- تحويل الفيديوهات الأفقية إلى عمودية
- التركيز على المتحدث في الفيديو
- إنشاء مقاطع للمنصات الاجتماعية

### التعليقات التوضيحية التلقائية

يحول الكلام المنطوق إلى نص:

1. انقر على "تعليقات توضيحية"
2. اختر اللغة (عربي/إنجليزي)
3. انتظر تحليل الصوت
4. راجع وعدّل النصوص إذا لزم الأمر
5. اختر تنسيق التصدير (SRT, VTT, أو JSON)

### ترجمة التعليقات

يترجم التعليقات التوضيحية إلى لغات أخرى:

1. قم بتوليد التعليقات التوضيحية أولاً
2. انقر على "ترجمة"
3. اختر اللغة الهدف
4. انتظر الترجمة
5. راجع النتائج

**اللغات المدعومة:**
- العربية ↔ الإنجليزية
- العربية ↔ الفرنسية
- العربية ↔ الإسبانية
- وغيرها...

### توليد المحتوى الذكي

يقترح عناوين وأوصاف للفيديو:

1. بعد تحرير الفيديو، انقر على "توليد محتوى"
2. سيحلل النظام محتوى الفيديو
3. سيقترح:
   - عناوين جذابة
   - أوصاف مناسبة
   - هاشتاغات ذات صلة
   - عبارات حث على اتخاذ إجراء

## 📤 النشر والتصدير

### تصدير الفيديو

1. انقر على "تصدير" في شريط الأدوات
2. اختر مجلد الحفظ
3. حدد اسم الملف
4. اختر التنسيق (MP4, AVI, MOV)
5. انقر على "حفظ"

### النشر على المنصات الاجتماعية

يساعدك في تحضير المحتوى للنشر:

1. انقر على "نشر" في القائمة
2. اختر المنصة المطلوبة:
   - YouTube
   - TikTok
   - Instagram
   - Facebook
   - Twitter
3. سيتم تحضير المحتوى وفقاً لمتطلبات المنصة
4. ستحصل على:
   - تعليمات الرفع
   - المحتوى المُحسَّن
   - نصائح للنشر

### تحليل جودة الفيديو

يحلل جودة الفيديو ويقدم اقتراحات:

- **الجودة التقنية**: دقة، معدل الإطارات، معدل البت
- **جودة المحتوى**: مدة مناسبة، نسبة العرض للارتفاع
- **توقع الأداء**: مشاهدات متوقعة، معدل تفاعل
- **اقتراحات التحسين**: نصائح لتحسين الجودة

## 🔧 حل المشاكل

### مشاكل شائعة وحلولها

#### لا يمكن فتح الفيديو
**الأسباب المحتملة:**
- تنسيق الفيديو غير مدعوم
- الملف تالف
- حجم الملف كبير جداً

**الحلول:**
1. تأكد من أن التنسيق مدعوم (.mp4, .avi, .mov, .mkv, .wmv, .flv, .webm)
2. جرب فتح الملف في مشغل فيديو آخر للتأكد من سلامته
3. قلل حجم الملف إذا كان أكبر من 500MB

#### FFmpeg غير متوفر
**الخطأ:** "FFmpeg غير متوفر - FFmpeg not available"

**الحل:**
1. تأكد من تثبيت FFmpeg
2. أضف FFmpeg إلى متغير البيئة PATH
3. أعد تشغيل التطبيق

#### بطء في المعالجة
**الأسباب:**
- ملف فيديو كبير
- موارد النظام محدودة
- عمليات متعددة تعمل في نفس الوقت

**الحلول:**
1. أغلق التطبيقات الأخرى
2. استخدم ملفات فيديو أصغر
3. قلل جودة المعالجة مؤقتاً

#### مشاكل الذكاء الاصطناعي
**الخطأ:** "خدمة الذكاء الاصطناعي غير متوفرة"

**الحلول:**
1. تحقق من اتصال الإنترنت
2. تأكد من صحة مفاتيح API
3. تحقق من حدود الاستخدام المجاني

### رسائل الخطأ الشائعة

| رسالة الخطأ | السبب | الحل |
|-------------|--------|------|
| "الملف غير موجود" | مسار الملف خاطئ | تأكد من وجود الملف |
| "تنسيق غير مدعوم" | تنسيق الفيديو غير مدعوم | استخدم تنسيق مدعوم |
| "مساحة تخزين غير كافية" | مساحة القرص ممتلئة | احذف ملفات غير ضرورية |
| "ذاكرة غير كافية" | الذاكرة ممتلئة | أغلق تطبيقات أخرى |

## ❓ الأسئلة الشائعة

### عام

**س: هل التطبيق مجاني حقاً؟**
ج: نعم، التطبيق مجاني بالكامل ومفتوح المصدر. ميزات الذكاء الاصطناعي تعتمد على خدمات مجانية مع حدود استخدام.

**س: ما هي التنسيقات المدعومة؟**
ج: يدعم التطبيق معظم تنسيقات الفيديو الشائعة: MP4, AVI, MOV, MKV, WMV, FLV, WebM.

**س: هل يمكن استخدامه بدون إنترنت؟**
ج: المميزات الأساسية تعمل بدون إنترنت. ميزات الذكاء الاصطناعي تحتاج اتصال إنترنت.

### الذكاء الاصطناعي

**س: كم مرة يمكنني استخدام ميزات الذكاء الاصطناعي؟**
ج: الحدود تعتمد على الخدمات المجانية:
- Hugging Face: 100 طلب يومياً
- Google Cloud: 1000 دقيقة شهرياً

**س: هل البيانات آمنة؟**
ج: نعم، الفيديوهات تُعالج محلياً. فقط الصوت يُرسل للخدمات الخارجية للتحليل.

### الأداء

**س: لماذا المعالجة بطيئة؟**
ج: سرعة المعالجة تعتمد على:
- حجم الفيديو
- قوة المعالج
- الذاكرة المتاحة
- نوع العملية

**س: كيف أحسن الأداء؟**
ج: 
- استخدم ملفات فيديو أصغر
- أغلق التطبيقات الأخرى
- استخدم SSD بدلاً من HDD
- زد الذاكرة إذا أمكن

### الدعم

**س: أين أجد المساعدة؟**
ج: يمكنك:
- قراءة هذا الدليل
- زيارة [صفحة المشاكل](https://github.com/your-username/video-editor-pro/issues)
- مراسلة الدعم

**س: كيف أبلغ عن مشكلة؟**
ج: افتح مشكلة جديدة في GitHub مع:
- وصف المشكلة
- خطوات إعادة الإنتاج
- رسائل الخطأ
- معلومات النظام

---

## 📞 الدعم والتواصل

- **GitHub**: [video-editor-pro](https://github.com/your-username/video-editor-pro)
- **الوثائق**: [docs/](https://github.com/your-username/video-editor-pro/docs)
- **المشاكل**: [Issues](https://github.com/your-username/video-editor-pro/issues)

---

*آخر تحديث: ديسمبر 2024*
