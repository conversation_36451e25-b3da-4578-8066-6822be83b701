"""
محمل YouTube للبثوث المباشرة والفيديوهات
YouTube downloader for livestreams and videos
"""

import logging
import os
import re
from pathlib import Path
from typing import Optional, Dict, Any, Callable
import subprocess
import json

logger = logging.getLogger(__name__)

# محاولة استيراد yt-dlp
try:
    import yt_dlp
    YT_DLP_AVAILABLE = True
except ImportError:
    YT_DLP_AVAILABLE = False
    logger.warning("مكتبة yt-dlp غير متوفرة. قم بتثبيتها: pip install yt-dlp")

class YouTubeDownloader:
    """محمل YouTube للبثوث المباشرة والفيديوهات"""
    
    def __init__(self, download_dir: Optional[Path] = None):
        """تهيئة محمل YouTube"""
        
        if not YT_DLP_AVAILABLE:
            raise ImportError("مكتبة yt-dlp غير متوفرة")
        
        self.download_dir = download_dir or Path("downloads")
        self.download_dir.mkdir(exist_ok=True)
        
        # إعدادات التحميل الافتراضية
        self.default_options = {
            'format': 'best[height<=1080]',  # أفضل جودة حتى 1080p
            'outtmpl': str(self.download_dir / '%(title)s.%(ext)s'),
            'writeinfojson': True,  # حفظ معلومات الفيديو
            'writesubtitles': False,  # عدم تحميل الترجمات (سنولدها بأنفسنا)
            'writeautomaticsub': False,
            'ignoreerrors': True,
            'no_warnings': False,
            'extractaudio': False,
            'audioformat': 'mp3',
            'audioquality': '192',
        }
        
        logger.info(f"تم تهيئة محمل YouTube - مجلد التحميل: {self.download_dir}")
    
    def is_youtube_url(self, url: str) -> bool:
        """التحقق من كون الرابط من YouTube"""
        youtube_patterns = [
            r'(?:https?://)?(?:www\.)?youtube\.com/watch\?v=[\w-]+',
            r'(?:https?://)?(?:www\.)?youtube\.com/live/[\w-]+',
            r'(?:https?://)?youtu\.be/[\w-]+',
            r'(?:https?://)?(?:www\.)?youtube\.com/embed/[\w-]+',
            r'(?:https?://)?(?:m\.)?youtube\.com/watch\?v=[\w-]+',
        ]
        
        return any(re.match(pattern, url) for pattern in youtube_patterns)
    
    def get_video_info(self, url: str) -> Optional[Dict[str, Any]]:
        """الحصول على معلومات الفيديو بدون تحميل"""
        
        if not self.is_youtube_url(url):
            logger.error("الرابط ليس من YouTube")
            return None
        
        try:
            logger.info(f"جلب معلومات الفيديو: {url}")
            
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'extract_flat': False,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                
                if not info:
                    logger.error("لا يمكن الحصول على معلومات الفيديو")
                    return None
                
                # استخراج المعلومات المهمة
                video_info = {
                    'id': info.get('id', ''),
                    'title': info.get('title', 'Unknown'),
                    'description': info.get('description', ''),
                    'duration': info.get('duration', 0),
                    'view_count': info.get('view_count', 0),
                    'like_count': info.get('like_count', 0),
                    'upload_date': info.get('upload_date', ''),
                    'uploader': info.get('uploader', ''),
                    'uploader_id': info.get('uploader_id', ''),
                    'is_live': info.get('is_live', False),
                    'was_live': info.get('was_live', False),
                    'live_status': info.get('live_status', 'not_live'),
                    'thumbnail': info.get('thumbnail', ''),
                    'webpage_url': info.get('webpage_url', url),
                    'formats': len(info.get('formats', [])),
                    'resolution': self._get_best_resolution(info.get('formats', [])),
                    'filesize_approx': info.get('filesize_approx', 0),
                }
                
                logger.info(f"تم جلب معلومات الفيديو: {video_info['title']}")
                return video_info
                
        except Exception as e:
            logger.error(f"خطأ في جلب معلومات الفيديو: {e}")
            return None
    
    def _get_best_resolution(self, formats: list) -> str:
        """الحصول على أفضل دقة متاحة"""
        if not formats:
            return "Unknown"
        
        resolutions = []
        for fmt in formats:
            if fmt.get('height'):
                resolutions.append(fmt['height'])
        
        if resolutions:
            max_res = max(resolutions)
            return f"{max_res}p"
        
        return "Unknown"
    
    def download_video(self, url: str, progress_callback: Optional[Callable] = None,
                      custom_filename: Optional[str] = None) -> Optional[str]:
        """تحميل فيديو من YouTube"""
        
        if not self.is_youtube_url(url):
            logger.error("الرابط ليس من YouTube")
            return None
        
        try:
            logger.info(f"بدء تحميل الفيديو: {url}")
            
            # إعداد خيارات التحميل
            ydl_opts = self.default_options.copy()
            
            # تخصيص اسم الملف إذا تم تحديده
            if custom_filename:
                safe_filename = self._sanitize_filename(custom_filename)
                ydl_opts['outtmpl'] = str(self.download_dir / f'{safe_filename}.%(ext)s')
            
            # إضافة callback للتقدم
            if progress_callback:
                ydl_opts['progress_hooks'] = [self._progress_hook_wrapper(progress_callback)]
            
            downloaded_file = None
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                # الحصول على معلومات الفيديو أولاً
                info = ydl.extract_info(url, download=False)
                
                if not info:
                    logger.error("لا يمكن الحصول على معلومات الفيديو")
                    return None
                
                # التحقق من حالة البث
                if info.get('is_live'):
                    logger.warning("هذا بث مباشر جاري. سيتم تحميل الجزء المتاح.")
                
                # تحديد اسم الملف المتوقع
                if custom_filename:
                    safe_filename = self._sanitize_filename(custom_filename)
                    expected_file = self.download_dir / f'{safe_filename}.mp4'
                else:
                    title = self._sanitize_filename(info.get('title', 'video'))
                    expected_file = self.download_dir / f'{title}.mp4'
                
                # بدء التحميل
                ydl.download([url])
                
                # البحث عن الملف المحمل
                downloaded_file = self._find_downloaded_file(expected_file, info.get('id', ''))
                
                if downloaded_file and Path(downloaded_file).exists():
                    file_size = Path(downloaded_file).stat().st_size / (1024 * 1024)  # MB
                    logger.info(f"تم تحميل الفيديو بنجاح: {downloaded_file} ({file_size:.1f}MB)")
                    return str(downloaded_file)
                else:
                    logger.error("لم يتم العثور على الملف المحمل")
                    return None
                    
        except Exception as e:
            logger.error(f"خطأ في تحميل الفيديو: {e}")
            return None
    
    def _sanitize_filename(self, filename: str) -> str:
        """تنظيف اسم الملف من الأحرف غير المسموحة"""
        # إزالة الأحرف غير المسموحة
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # تقصير الاسم إذا كان طويلاً
        if len(sanitized) > 100:
            sanitized = sanitized[:100]
        return sanitized.strip()
    
    def _find_downloaded_file(self, expected_file: Path, video_id: str) -> Optional[str]:
        """البحث عن الملف المحمل"""
        
        # البحث بالاسم المتوقع
        for ext in ['.mp4', '.mkv', '.webm', '.avi']:
            file_path = expected_file.with_suffix(ext)
            if file_path.exists():
                return str(file_path)
        
        # البحث بـ ID الفيديو
        if video_id:
            for file_path in self.download_dir.glob(f'*{video_id}*'):
                if file_path.is_file() and file_path.suffix in ['.mp4', '.mkv', '.webm', '.avi']:
                    return str(file_path)
        
        # البحث في آخر الملفات المحملة
        video_files = []
        for ext in ['.mp4', '.mkv', '.webm', '.avi']:
            video_files.extend(self.download_dir.glob(f'*{ext}'))
        
        if video_files:
            # ترتيب حسب وقت التعديل (الأحدث أولاً)
            latest_file = max(video_files, key=lambda x: x.stat().st_mtime)
            return str(latest_file)
        
        return None
    
    def _progress_hook_wrapper(self, callback: Callable):
        """تحويل progress hook من yt-dlp إلى تنسيق مناسب"""
        
        def progress_hook(d):
            if d['status'] == 'downloading':
                # حساب النسبة المئوية
                if 'total_bytes' in d:
                    percentage = (d['downloaded_bytes'] / d['total_bytes']) * 100
                elif 'total_bytes_estimate' in d:
                    percentage = (d['downloaded_bytes'] / d['total_bytes_estimate']) * 100
                else:
                    percentage = 0
                
                # معلومات التقدم
                progress_info = {
                    'percentage': min(percentage, 100),
                    'downloaded_bytes': d.get('downloaded_bytes', 0),
                    'total_bytes': d.get('total_bytes', d.get('total_bytes_estimate', 0)),
                    'speed': d.get('speed', 0),
                    'eta': d.get('eta', 0),
                    'status': 'downloading'
                }
                
                callback(progress_info)
                
            elif d['status'] == 'finished':
                callback({
                    'percentage': 100,
                    'status': 'finished',
                    'filename': d.get('filename', '')
                })
        
        return progress_hook
    
    def download_audio_only(self, url: str, progress_callback: Optional[Callable] = None) -> Optional[str]:
        """تحميل الصوت فقط من الفيديو"""
        
        try:
            logger.info(f"تحميل الصوت فقط: {url}")
            
            ydl_opts = {
                'format': 'bestaudio/best',
                'outtmpl': str(self.download_dir / '%(title)s_audio.%(ext)s'),
                'postprocessors': [{
                    'key': 'FFmpegExtractAudio',
                    'preferredcodec': 'mp3',
                    'preferredquality': '192',
                }],
                'writeinfojson': False,
                'ignoreerrors': True,
            }
            
            if progress_callback:
                ydl_opts['progress_hooks'] = [self._progress_hook_wrapper(progress_callback)]
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                if not info:
                    return None
                
                ydl.download([url])
                
                # البحث عن ملف الصوت
                title = self._sanitize_filename(info.get('title', 'audio'))
                audio_file = self.download_dir / f'{title}_audio.mp3'
                
                if audio_file.exists():
                    logger.info(f"تم تحميل الصوت: {audio_file}")
                    return str(audio_file)
                
                # البحث في الملفات الصوتية الحديثة
                audio_files = list(self.download_dir.glob('*.mp3'))
                if audio_files:
                    latest_audio = max(audio_files, key=lambda x: x.stat().st_mtime)
                    return str(latest_audio)
                
                return None
                
        except Exception as e:
            logger.error(f"خطأ في تحميل الصوت: {e}")
            return None
    
    def get_available_formats(self, url: str) -> Optional[list]:
        """الحصول على قائمة التنسيقات المتاحة"""
        
        try:
            ydl_opts = {'quiet': True, 'listformats': True}
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                
                if not info or 'formats' not in info:
                    return None
                
                formats = []
                for fmt in info['formats']:
                    format_info = {
                        'format_id': fmt.get('format_id', ''),
                        'ext': fmt.get('ext', ''),
                        'resolution': f"{fmt.get('width', 0)}x{fmt.get('height', 0)}" if fmt.get('width') else 'audio only',
                        'filesize': fmt.get('filesize', 0),
                        'fps': fmt.get('fps', 0),
                        'vcodec': fmt.get('vcodec', ''),
                        'acodec': fmt.get('acodec', ''),
                    }
                    formats.append(format_info)
                
                return formats
                
        except Exception as e:
            logger.error(f"خطأ في جلب التنسيقات: {e}")
            return None
    
    def cleanup_old_downloads(self, max_age_days: int = 7):
        """تنظيف التحميلات القديمة"""
        
        try:
            import time
            current_time = time.time()
            max_age_seconds = max_age_days * 24 * 3600
            
            cleaned_files = 0
            total_size_freed = 0
            
            for file_path in self.download_dir.rglob('*'):
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    
                    if file_age > max_age_seconds:
                        file_size = file_path.stat().st_size
                        file_path.unlink()
                        cleaned_files += 1
                        total_size_freed += file_size
            
            size_mb = total_size_freed / (1024 * 1024)
            logger.info(f"تم تنظيف {cleaned_files} ملف، تم توفير {size_mb:.1f}MB")
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف التحميلات: {e}")
