"""
عميل Google Gemini للترجمة وتوليد المحتوى
Google Gemini client for translation and content generation
"""

import logging
import requests
import json
import os
from typing import Optional, Dict, Any, List
from config.settings import AISettings
from ai.gemini_key_manager import gemini_key_manager

logger = logging.getLogger(__name__)

class GeminiClient:
    """عميل للتفاعل مع Google Gemini API"""
    
    def __init__(self, api_key: Optional[str] = None):
        """تهيئة عميل Gemini"""
        # استخدام مدير المفاتيح للحصول على مفتاح متاح
        self.key_manager = gemini_key_manager
        self.api_key = api_key or self.key_manager.get_current_key() or os.getenv('GOOGLE_GEMINI_API_KEY')
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models"

        if not self.api_key:
            logger.warning("مف<PERSON>ا<PERSON> Google Gemini API غير متوفر")
            logger.warning("Google Gemini API key not available")
        else:
            logger.info(f"تم تهيئة عميل Google Gemini بنجاح - المفتاح: {self.api_key[-10:]}")
            logger.info("Google Gemini client initialized successfully")
    
    def is_available(self) -> bool:
        """التحقق من توفر الخدمة"""
        return bool(self.api_key)

    def _make_request_with_retry(self, url: str, headers: Dict, data: Dict, params: Dict) -> Optional[Dict]:
        """إجراء طلب مع إعادة المحاولة والتبديل التلقائي للمفاتيح"""
        max_retries = 3

        for attempt in range(max_retries):
            try:
                # استخدام المفتاح الحالي
                current_key = self.key_manager.get_current_key()
                if not current_key:
                    logger.error("لا توجد مفاتيح Gemini متاحة")
                    return None

                # تحديث المفتاح في الطلب
                params["key"] = current_key

                response = requests.post(url, headers=headers, json=data, params=params, timeout=30)

                if response.status_code == 200:
                    # تسجيل الطلب الناجح
                    self.key_manager.record_successful_request(current_key)
                    return response.json()

                elif response.status_code == 429:
                    # تجاوز الحد اليومي
                    error_text = response.text
                    logger.warning(f"تجاوز الحد اليومي للمفتاح: {current_key[-10:]}")

                    # تمييز المفتاح كمحظور والتبديل للتالي
                    switched = self.key_manager.handle_api_error(current_key, error_text)

                    if switched and attempt < max_retries - 1:
                        logger.info("تم التبديل لمفتاح جديد، إعادة المحاولة...")
                        continue
                    else:
                        logger.error("لا توجد مفاتيح متاحة أخرى")
                        return None

                else:
                    logger.error(f"خطأ في API: {response.status_code} - {response.text}")
                    return None

            except Exception as e:
                logger.error(f"خطأ في الطلب (المحاولة {attempt + 1}): {e}")
                if attempt == max_retries - 1:
                    return None

        return None
    
    def translate_text(self, text: str, target_language: str = "ar", 
                      source_language: str = "en") -> Optional[str]:
        """ترجمة النص باستخدام Gemini"""
        if not self.is_available():
            logger.error("Gemini API غير متوفر للترجمة")
            return None
        
        try:
            # إعداد prompt للترجمة
            if target_language == "ar":
                prompt = f"""ترجم النص التالي إلى العربية بطريقة طبيعية ومفهومة:

النص: {text}

الترجمة:"""
            else:
                prompt = f"""Translate the following text to {target_language} naturally:

Text: {text}

Translation:"""
            
            # إرسال الطلب
            url = f"{self.base_url}/gemini-1.5-pro:generateContent"
            
            headers = {
                "Content-Type": "application/json",
            }
            
            data = {
                "contents": [{
                    "parts": [{
                        "text": prompt
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.3,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 1024,
                }
            }
            
            params = {}  # سيتم تعيين المفتاح في _make_request_with_retry

            result = self._make_request_with_retry(url, headers, data, params)

            if result and "candidates" in result and len(result["candidates"]) > 0:
                content = result["candidates"][0]["content"]["parts"][0]["text"]
                # تنظيف النص المترجم
                translated_text = content.strip()

                logger.info(f"تم ترجمة النص بنجاح: {text[:50]}...")
                return translated_text
            else:
                logger.error("لا توجد نتائج ترجمة في الاستجابة")
                return None
                
        except Exception as e:
            logger.error(f"خطأ في ترجمة النص باستخدام Gemini: {e}")
            return None
    
    def generate_video_title(self, description: str, moment_type: str = "exciting") -> Optional[str]:
        """توليد عنوان للفيديو"""
        if not self.is_available():
            return None
        
        try:
            # تحديد نوع العنوان حسب نوع اللحظة
            type_prompts = {
                "exciting": "مثير وجذاب",
                "funny": "مضحك ومسلي", 
                "shocking": "صادم ومفاجئ"
            }
            
            type_desc = type_prompts.get(moment_type, "مثير")
            
            prompt = f"""أنشئ عنوان {type_desc} لمقطع فيديو قصير (شورتس) بناءً على الوصف التالي:

الوصف: {description}

متطلبات العنوان:
- يجب أن يكون جذاب ويحفز على المشاهدة
- لا يزيد عن 60 حرف
- يحتوي على إيموجي مناسب
- مناسب لمنصات التواصل الاجتماعي

العنوان:"""
            
            url = f"{self.base_url}/gemini-1.5-pro:generateContent"
            
            headers = {"Content-Type": "application/json"}
            
            data = {
                "contents": [{"parts": [{"text": prompt}]}],
                "generationConfig": {
                    "temperature": 0.8,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 100,
                }
            }
            
            params = {}  # سيتم تعيين المفتاح في _make_request_with_retry

            result = self._make_request_with_retry(url, headers, data, params)

            if result and "candidates" in result and len(result["candidates"]) > 0:
                title = result["candidates"][0]["content"]["parts"][0]["text"].strip()
                logger.info(f"تم توليد عنوان: {title}")
                return title

            return None
            
        except Exception as e:
            logger.error(f"خطأ في توليد العنوان: {e}")
            return None
    
    def generate_video_description(self, title: str, moment_type: str = "exciting") -> Optional[str]:
        """توليد وصف للفيديو"""
        if not self.is_available():
            return None
        
        try:
            prompt = f"""أنشئ وصف جذاب لمقطع فيديو شورتس بناءً على العنوان التالي:

العنوان: {title}

متطلبات الوصف:
- يجب أن يكون مشوق ويحفز على المشاهدة
- يحتوي على دعوة للإعجاب والمشاركة
- لا يزيد عن 150 كلمة
- يحتوي على هاشتاغات مناسبة

الوصف:"""
            
            url = f"{self.base_url}/gemini-1.5-pro:generateContent"
            
            headers = {"Content-Type": "application/json"}
            
            data = {
                "contents": [{"parts": [{"text": prompt}]}],
                "generationConfig": {
                    "temperature": 0.7,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 300,
                }
            }
            
            params = {}  # سيتم تعيين المفتاح في _make_request_with_retry

            result = self._make_request_with_retry(url, headers, data, params)

            if result and "candidates" in result and len(result["candidates"]) > 0:
                description = result["candidates"][0]["content"]["parts"][0]["text"].strip()
                logger.info("تم توليد وصف للفيديو")
                return description

            return None
            
        except Exception as e:
            logger.error(f"خطأ في توليد الوصف: {e}")
            return None
    
    def generate_hashtags(self, content: str, moment_type: str = "exciting") -> List[str]:
        """توليد هاشتاغات للمحتوى"""
        if not self.is_available():
            return []
        
        try:
            prompt = f"""أنشئ قائمة بأفضل الهاشتاغات لمقطع فيديو شورتس بناءً على المحتوى التالي:

المحتوى: {content}
نوع اللحظة: {moment_type}

متطلبات الهاشتاغات:
- 10-15 هاشتاغ
- مزيج من العربية والإنجليزية
- مناسبة لمنصات التواصل الاجتماعي
- تحتوي على هاشتاغات شائعة مثل #shorts #viral #trending

الهاشتاغات (كل هاشتاغ في سطر منفصل):"""
            
            url = f"{self.base_url}/gemini-1.5-pro:generateContent"
            
            headers = {"Content-Type": "application/json"}
            
            data = {
                "contents": [{"parts": [{"text": prompt}]}],
                "generationConfig": {
                    "temperature": 0.6,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 200,
                }
            }
            
            params = {}  # سيتم تعيين المفتاح في _make_request_with_retry

            result = self._make_request_with_retry(url, headers, data, params)

            if result and "candidates" in result and len(result["candidates"]) > 0:
                hashtags_text = result["candidates"][0]["content"]["parts"][0]["text"].strip()

                # استخراج الهاشتاغات من النص
                hashtags = []
                for line in hashtags_text.split('\n'):
                    line = line.strip()
                    if line.startswith('#'):
                        hashtags.append(line)
                    elif line and not line.startswith('#'):
                        # إضافة # إذا لم تكن موجودة
                        hashtags.append(f"#{line}")

                logger.info(f"تم توليد {len(hashtags)} هاشتاغ")
                return hashtags[:15]  # حد أقصى 15 هاشتاغ

            return []
            
        except Exception as e:
            logger.error(f"خطأ في توليد الهاشتاغات: {e}")
            return []
    
    def analyze_sentiment(self, text: str) -> Optional[Dict[str, Any]]:
        """تحليل المشاعر في النص"""
        if not self.is_available():
            return None
        
        try:
            prompt = f"""حلل المشاعر في النص التالي وحدد:
1. نوع المشاعر (إيجابي، سلبي، محايد)
2. شدة المشاعر (1-10)
3. المشاعر المحددة (فرح، حزن، غضب، خوف، مفاجأة، إثارة)

النص: {text}

أجب بتنسيق JSON:
{{
  "sentiment": "positive/negative/neutral",
  "intensity": 1-10,
  "emotions": ["emotion1", "emotion2"],
  "confidence": 0.0-1.0
}}"""
            
            url = f"{self.base_url}/gemini-1.5-pro:generateContent"
            
            headers = {"Content-Type": "application/json"}
            
            data = {
                "contents": [{"parts": [{"text": prompt}]}],
                "generationConfig": {
                    "temperature": 0.1,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 200,
                }
            }
            
            params = {}  # سيتم تعيين المفتاح في _make_request_with_retry

            result = self._make_request_with_retry(url, headers, data, params)

            if result and "candidates" in result and len(result["candidates"]) > 0:
                response_text = result["candidates"][0]["content"]["parts"][0]["text"].strip()

                # محاولة استخراج JSON من الاستجابة
                try:
                    # البحث عن JSON في النص
                    start_idx = response_text.find('{')
                    end_idx = response_text.rfind('}') + 1

                    if start_idx != -1 and end_idx != -1:
                        json_str = response_text[start_idx:end_idx]
                        sentiment_data = json.loads(json_str)
                        logger.info("تم تحليل المشاعر بنجاح")
                        return sentiment_data
                except json.JSONDecodeError:
                    logger.warning("لا يمكن تحليل استجابة JSON لتحليل المشاعر")

            return None
            
        except Exception as e:
            logger.error(f"خطأ في تحليل المشاعر: {e}")
            return None
