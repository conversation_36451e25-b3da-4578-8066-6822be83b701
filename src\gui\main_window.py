"""
النافذة الرئيسية لأداة تحرير الفيديو
Main window for Video Editor Pro
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import logging
from pathlib import Path
from typing import Optional

from config.settings import AppSettings, UISettings
from utils.file_utils import FileManager
from utils.validators import VideoValidator

logger = logging.getLogger(__name__)

class VideoEditorApp:
    """التطبيق الرئيسي لتحرير الفيديو"""
    
    def __init__(self, root: tk.Tk):
        self.root = root
        self.file_manager = FileManager()
        self.video_validator = VideoValidator()
        
        # متغيرات التطبيق
        self.current_video_path: Optional[str] = None
        self.video_info: Optional[dict] = None
        
        # إعداد النافذة الرئيسية
        self.setup_main_window()
        
        # إنشاء واجهة المستخدم
        self.create_widgets()
        
        # ربط الأحداث
        self.bind_events()
        
        logger.info("تم تشغيل النافذة الرئيسية بنجاح")
    
    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        # عنوان النافذة
        self.root.title(f"{AppSettings.APP_NAME} v{AppSettings.APP_VERSION}")
        
        # حجم النافذة
        self.root.geometry(f"{AppSettings.WINDOW_WIDTH}x{AppSettings.WINDOW_HEIGHT}")
        self.root.minsize(AppSettings.MIN_WINDOW_WIDTH, AppSettings.MIN_WINDOW_HEIGHT)
        
        # توسيط النافذة
        self.center_window()
        
        # أيقونة النافذة (إذا كانت متوفرة)
        try:
            icon_path = AppSettings.ASSETS_DIR / "icons" / "app_icon.ico"
            if icon_path.exists():
                self.root.iconbitmap(str(icon_path))
        except Exception:
            pass  # تجاهل إذا لم تكن الأيقونة متوفرة
        
        # إعداد الألوان والخطوط
        self.setup_styles()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_styles(self):
        """إعداد الأنماط والألوان"""
        style = ttk.Style()
        
        # تكوين الألوان
        self.root.configure(bg=UISettings.BACKGROUND_COLOR)
        
        # أنماط الأزرار
        style.configure("Primary.TButton",
                       font=UISettings.BUTTON_FONT,
                       padding=(10, 5))
        
        style.configure("Secondary.TButton",
                       font=UISettings.BUTTON_FONT,
                       padding=(8, 4))
    
    def create_widgets(self):
        """إنشاء عناصر واجهة المستخدم"""
        # إنشاء الإطار الرئيسي
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تكوين الشبكة
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        self.main_frame.columnconfigure(1, weight=1)
        
        # إنشاء شريط القوائم
        self.create_menu_bar()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء منطقة العمل الرئيسية
        self.create_main_area()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
    
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="فتح فيديو...", command=self.open_video, accelerator="Ctrl+O")
        file_menu.add_separator()
        file_menu.add_command(label="حفظ المشروع...", command=self.save_project, accelerator="Ctrl+S")
        file_menu.add_command(label="فتح مشروع...", command=self.open_project)
        file_menu.add_separator()
        file_menu.add_command(label="تصدير الفيديو...", command=self.export_video, accelerator="Ctrl+E")
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.quit_app, accelerator="Ctrl+Q")
        
        # قائمة التحرير
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="تحرير", menu=edit_menu)
        edit_menu.add_command(label="تراجع", command=self.undo, accelerator="Ctrl+Z")
        edit_menu.add_command(label="إعادة", command=self.redo, accelerator="Ctrl+Y")
        edit_menu.add_separator()
        edit_menu.add_command(label="قص", command=self.cut, accelerator="Ctrl+X")
        edit_menu.add_command(label="نسخ", command=self.copy, accelerator="Ctrl+C")
        edit_menu.add_command(label="لصق", command=self.paste, accelerator="Ctrl+V")
        
        # قائمة الذكاء الاصطناعي
        ai_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ذكاء اصطناعي", menu=ai_menu)
        ai_menu.add_command(label="🎬 محلل البثوث المباشرة", command=self.open_livestream_analyzer)
        ai_menu.add_separator()
        ai_menu.add_command(label="قص تلقائي", command=self.auto_clip)
        ai_menu.add_command(label="تتبع الوجه", command=self.face_tracking)
        ai_menu.add_command(label="تعليقات توضيحية", command=self.auto_captions)
        ai_menu.add_command(label="ترجمة", command=self.translate_captions)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل المستخدم", command=self.show_help)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        self.toolbar_frame = ttk.Frame(self.main_frame)
        self.toolbar_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # أزرار الأدوات الرئيسية
        ttk.Button(self.toolbar_frame, text="فتح فيديو", 
                  command=self.open_video, style="Primary.TButton").pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(self.toolbar_frame, text="حفظ", 
                  command=self.save_project, style="Secondary.TButton").pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Separator(self.toolbar_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        ttk.Button(self.toolbar_frame, text="قص", 
                  command=self.trim_video, style="Secondary.TButton").pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(self.toolbar_frame, text="إضافة نص", 
                  command=self.add_text, style="Secondary.TButton").pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(self.toolbar_frame, text="تحويل", 
                  command=self.convert_video, style="Secondary.TButton").pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Separator(self.toolbar_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        ttk.Button(self.toolbar_frame, text="تصدير", 
                  command=self.export_video, style="Primary.TButton").pack(side=tk.LEFT, padx=(0, 5))
    
    def create_main_area(self):
        """إنشاء منطقة العمل الرئيسية"""
        # إطار جانبي للأدوات
        self.sidebar_frame = ttk.LabelFrame(self.main_frame, text="الأدوات", padding="10")
        self.sidebar_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # منطقة العمل الرئيسية
        self.workspace_frame = ttk.LabelFrame(self.main_frame, text="منطقة العمل", padding="10")
        self.workspace_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تكوين الشبكة
        self.main_frame.rowconfigure(1, weight=1)
        self.workspace_frame.columnconfigure(0, weight=1)
        self.workspace_frame.rowconfigure(0, weight=1)
        
        # إنشاء محتوى الشريط الجانبي
        self.create_sidebar_content()
        
        # إنشاء محتوى منطقة العمل
        self.create_workspace_content()
    
    def create_sidebar_content(self):
        """إنشاء محتوى الشريط الجانبي"""
        # معلومات الفيديو
        info_frame = ttk.LabelFrame(self.sidebar_frame, text="معلومات الفيديو")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.video_info_text = tk.Text(info_frame, height=8, width=30, state=tk.DISABLED)
        self.video_info_text.pack(fill=tk.BOTH, expand=True)
        
        # أدوات التحرير السريع
        tools_frame = ttk.LabelFrame(self.sidebar_frame, text="أدوات سريعة")
        tools_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(tools_frame, text="قص سريع", command=self.quick_trim).pack(fill=tk.X, pady=2)
        ttk.Button(tools_frame, text="تحويل للعمودي", command=self.convert_to_vertical).pack(fill=tk.X, pady=2)
        ttk.Button(tools_frame, text="إضافة ترجمة", command=self.add_subtitles).pack(fill=tk.X, pady=2)
        
        # إعدادات الذكاء الاصطناعي
        ai_frame = ttk.LabelFrame(self.sidebar_frame, text="ذكاء اصطناعي")
        ai_frame.pack(fill=tk.X)

        ttk.Button(ai_frame, text="🎬 محلل البثوث", command=self.open_livestream_analyzer).pack(fill=tk.X, pady=2)
        ttk.Button(ai_frame, text="📚 إدارة الفيديوهات", command=self.open_video_history).pack(fill=tk.X, pady=2)
        ttk.Button(ai_frame, text="قص تلقائي", command=self.auto_clip).pack(fill=tk.X, pady=2)
        ttk.Button(ai_frame, text="تتبع الوجه", command=self.face_tracking).pack(fill=tk.X, pady=2)
        ttk.Button(ai_frame, text="ترجمة تلقائية", command=self.auto_translate).pack(fill=tk.X, pady=2)
    
    def create_workspace_content(self):
        """إنشاء محتوى منطقة العمل"""
        # منطقة معاينة الفيديو
        preview_frame = ttk.LabelFrame(self.workspace_frame, text="معاينة")
        preview_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # إطار للمعاينة (سيتم تطويره لاحقاً)
        self.preview_canvas = tk.Canvas(preview_frame, bg='black', height=300)
        self.preview_canvas.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # منطقة الجدول الزمني
        timeline_frame = ttk.LabelFrame(self.workspace_frame, text="الجدول الزمني")
        timeline_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # شريط تمرير للجدول الزمني
        self.timeline_scale = ttk.Scale(timeline_frame, from_=0, to=100, orient=tk.HORIZONTAL)
        self.timeline_scale.pack(fill=tk.X, padx=10, pady=10)
        
        # أزرار التحكم
        controls_frame = ttk.Frame(timeline_frame)
        controls_frame.pack(pady=5)
        
        ttk.Button(controls_frame, text="⏮", width=3).pack(side=tk.LEFT, padx=2)
        ttk.Button(controls_frame, text="⏸", width=3).pack(side=tk.LEFT, padx=2)
        ttk.Button(controls_frame, text="▶", width=3).pack(side=tk.LEFT, padx=2)
        ttk.Button(controls_frame, text="⏭", width=3).pack(side=tk.LEFT, padx=2)
        
        # تكوين الشبكة
        self.workspace_frame.rowconfigure(0, weight=1)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.status_label = ttk.Label(self.status_frame, text="جاهز")
        self.status_label.pack(side=tk.LEFT)
        
        # شريط التقدم
        self.progress_bar = ttk.Progressbar(self.status_frame, mode='determinate')
        self.progress_bar.pack(side=tk.RIGHT, padx=(10, 0))
    
    def bind_events(self):
        """ربط الأحداث"""
        # اختصارات لوحة المفاتيح
        self.root.bind('<Control-o>', lambda e: self.open_video())
        self.root.bind('<Control-s>', lambda e: self.save_project())
        self.root.bind('<Control-e>', lambda e: self.export_video())
        self.root.bind('<Control-q>', lambda e: self.quit_app())
        
        # حدث إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.quit_app)

    # وظائف الملفات
    def open_video(self):
        """فتح ملف فيديو"""
        try:
            file_path = filedialog.askopenfilename(
                title="اختر ملف فيديو",
                filetypes=[
                    ("ملفات الفيديو", "*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm"),
                    ("جميع الملفات", "*.*")
                ]
            )

            if file_path:
                # التحقق من صحة الملف
                is_valid, message = self.video_validator.validate_video_file(file_path)
                if not is_valid:
                    messagebox.showerror("خطأ", f"ملف غير صحيح: {message}")
                    return

                self.current_video_path = file_path
                self.load_video_info()
                self.update_status(f"تم فتح: {Path(file_path).name}")
                logger.info(f"تم فتح الفيديو: {file_path}")

        except Exception as e:
            logger.error(f"خطأ في فتح الفيديو: {e}")
            messagebox.showerror("خطأ", f"لا يمكن فتح الفيديو: {e}")

    def load_video_info(self):
        """تحميل معلومات الفيديو"""
        if not self.current_video_path:
            return

        try:
            from core.video_processor import VideoProcessor
            processor = VideoProcessor()
            self.video_info = processor.get_video_info(self.current_video_path)

            # عرض المعلومات
            info_text = f"""الملف: {Path(self.current_video_path).name}
المدة: {self.video_info['duration']:.1f} ثانية
الحجم: {self.file_manager.get_file_size_human(self.current_video_path)}
الدقة: {self.video_info['video']['width']}x{self.video_info['video']['height']}
معدل الإطارات: {self.video_info['video']['fps']:.1f} fps
ترميز الفيديو: {self.video_info['video']['codec']}
ترميز الصوت: {self.video_info['audio']['codec']}"""

            self.video_info_text.config(state=tk.NORMAL)
            self.video_info_text.delete(1.0, tk.END)
            self.video_info_text.insert(1.0, info_text)
            self.video_info_text.config(state=tk.DISABLED)

            # تحديث الجدول الزمني
            self.timeline_scale.config(to=self.video_info['duration'])

        except Exception as e:
            logger.error(f"خطأ في تحميل معلومات الفيديو: {e}")
            messagebox.showerror("خطأ", f"لا يمكن قراءة معلومات الفيديو: {e}")

    def save_project(self):
        """حفظ المشروع"""
        messagebox.showinfo("قريباً", "ميزة حفظ المشروع ستكون متاحة قريباً")

    def open_project(self):
        """فتح مشروع"""
        messagebox.showinfo("قريباً", "ميزة فتح المشروع ستكون متاحة قريباً")

    def export_video(self):
        """تصدير الفيديو"""
        if not self.current_video_path:
            messagebox.showwarning("تحذير", "يرجى فتح ملف فيديو أولاً")
            return

        try:
            output_path = filedialog.asksaveasfilename(
                title="حفظ الفيديو",
                defaultextension=".mp4",
                filetypes=[
                    ("MP4", "*.mp4"),
                    ("AVI", "*.avi"),
                    ("MOV", "*.mov"),
                    ("جميع الملفات", "*.*")
                ]
            )

            if output_path:
                # هنا سيتم تطبيق جميع التعديلات وتصدير الفيديو
                self.update_status("جاري التصدير...")
                messagebox.showinfo("نجح", f"تم تصدير الفيديو إلى: {output_path}")
                self.update_status("تم التصدير بنجاح")

        except Exception as e:
            logger.error(f"خطأ في التصدير: {e}")
            messagebox.showerror("خطأ", f"فشل في التصدير: {e}")

    # وظائف التحرير
    def undo(self):
        """تراجع"""
        messagebox.showinfo("قريباً", "ميزة التراجع ستكون متاحة قريباً")

    def redo(self):
        """إعادة"""
        messagebox.showinfo("قريباً", "ميزة الإعادة ستكون متاحة قريباً")

    def cut(self):
        """قص"""
        messagebox.showinfo("قريباً", "ميزة القص ستكون متاحة قريباً")

    def copy(self):
        """نسخ"""
        messagebox.showinfo("قريباً", "ميزة النسخ ستكون متاحة قريباً")

    def paste(self):
        """لصق"""
        messagebox.showinfo("قريباً", "ميزة اللصق ستكون متاحة قريباً")

    # وظائف التحرير السريع
    def trim_video(self):
        """قص الفيديو"""
        if not self.current_video_path:
            messagebox.showwarning("تحذير", "يرجى فتح ملف فيديو أولاً")
            return

        # فتح نافذة قص الفيديو
        from gui.dialogs import TrimDialog
        dialog = TrimDialog(self.root, self.video_info['duration'])
        if dialog.result:
            start_time, end_time = dialog.result
            self.perform_trim(start_time, end_time)

    def perform_trim(self, start_time: float, end_time: float):
        """تنفيذ قص الفيديو"""
        try:
            from core.video_processor import VideoProcessor
            processor = VideoProcessor()

            output_path = self.file_manager.create_temp_file(suffix='.mp4')

            self.update_status("جاري القص...")
            success = processor.trim_video(
                self.current_video_path,
                str(output_path),
                start_time,
                end_time
            )

            if success:
                self.current_video_path = str(output_path)
                self.load_video_info()
                self.update_status("تم القص بنجاح")
                messagebox.showinfo("نجح", "تم قص الفيديو بنجاح")
            else:
                self.update_status("فشل في القص")
                messagebox.showerror("خطأ", "فشل في قص الفيديو")

        except Exception as e:
            logger.error(f"خطأ في قص الفيديو: {e}")
            messagebox.showerror("خطأ", f"خطأ في قص الفيديو: {e}")

    def add_text(self):
        """إضافة نص"""
        if not self.current_video_path:
            messagebox.showwarning("تحذير", "يرجى فتح ملف فيديو أولاً")
            return

        from gui.dialogs import TextDialog
        dialog = TextDialog(self.root)
        if dialog.result:
            text, position, font_size, color = dialog.result
            self.perform_add_text(text, position, font_size, color)

    def perform_add_text(self, text: str, position: str, font_size: int, color: str):
        """تنفيذ إضافة النص"""
        try:
            from core.video_processor import VideoProcessor
            processor = VideoProcessor()

            output_path = self.file_manager.create_temp_file(suffix='.mp4')

            self.update_status("جاري إضافة النص...")
            success = processor.add_text_overlay(
                self.current_video_path,
                str(output_path),
                text,
                position,
                font_size,
                color
            )

            if success:
                self.current_video_path = str(output_path)
                self.load_video_info()
                self.update_status("تم إضافة النص بنجاح")
                messagebox.showinfo("نجح", "تم إضافة النص بنجاح")
            else:
                self.update_status("فشل في إضافة النص")
                messagebox.showerror("خطأ", "فشل في إضافة النص")

        except Exception as e:
            logger.error(f"خطأ في إضافة النص: {e}")
            messagebox.showerror("خطأ", f"خطأ في إضافة النص: {e}")

    def convert_video(self):
        """تحويل الفيديو"""
        if not self.current_video_path:
            messagebox.showwarning("تحذير", "يرجى فتح ملف فيديو أولاً")
            return

        from gui.dialogs import ConvertDialog
        dialog = ConvertDialog(self.root, self.video_info)
        if dialog.result:
            width, height, quality = dialog.result
            self.perform_convert(width, height, quality)

    def perform_convert(self, width: int, height: int, quality: str):
        """تنفيذ تحويل الفيديو"""
        try:
            from core.video_processor import VideoProcessor
            processor = VideoProcessor()

            output_path = self.file_manager.create_temp_file(suffix='.mp4')

            self.update_status("جاري التحويل...")
            success = processor.resize_video(
                self.current_video_path,
                str(output_path),
                width,
                height,
                quality
            )

            if success:
                self.current_video_path = str(output_path)
                self.load_video_info()
                self.update_status("تم التحويل بنجاح")
                messagebox.showinfo("نجح", "تم تحويل الفيديو بنجاح")
            else:
                self.update_status("فشل في التحويل")
                messagebox.showerror("خطأ", "فشل في تحويل الفيديو")

        except Exception as e:
            logger.error(f"خطأ في تحويل الفيديو: {e}")
            messagebox.showerror("خطأ", f"خطأ في تحويل الفيديو: {e}")

    # وظائف سريعة
    def quick_trim(self):
        """قص سريع"""
        self.trim_video()

    def convert_to_vertical(self):
        """تحويل للعمودي (9:16)"""
        if not self.current_video_path:
            messagebox.showwarning("تحذير", "يرجى فتح ملف فيديو أولاً")
            return

        # تحويل إلى 1080x1920 (9:16)
        self.perform_convert(1080, 1920, 'medium')

    def add_subtitles(self):
        """إضافة ترجمة"""
        messagebox.showinfo("قريباً", "ميزة إضافة الترجمة ستكون متاحة قريباً")

    # وظائف الذكاء الاصطناعي
    def auto_clip(self):
        """قص تلقائي"""
        messagebox.showinfo("قريباً", "ميزة القص التلقائي ستكون متاحة قريباً")

    def face_tracking(self):
        """تتبع الوجه"""
        messagebox.showinfo("قريباً", "ميزة تتبع الوجه ستكون متاحة قريباً")

    def auto_captions(self):
        """تعليقات توضيحية تلقائية"""
        messagebox.showinfo("قريباً", "ميزة التعليقات التوضيحية التلقائية ستكون متاحة قريباً")

    def translate_captions(self):
        """ترجمة التعليقات"""
        messagebox.showinfo("قريباً", "ميزة ترجمة التعليقات ستكون متاحة قريباً")

    def auto_translate(self):
        """ترجمة تلقائية"""
        messagebox.showinfo("قريباً", "ميزة الترجمة التلقائية ستكون متاحة قريباً")

    def open_livestream_analyzer(self):
        """فتح محلل البثوث المباشرة"""
        try:
            from gui.livestream_window import LivestreamAnalysisWindow
            LivestreamAnalysisWindow(self.root)
        except Exception as e:
            logger.error(f"خطأ في فتح محلل البثوث المباشرة: {e}")
            messagebox.showerror("خطأ", f"لا يمكن فتح محلل البثوث المباشرة: {e}")

    def open_video_history(self):
        """فتح نافذة إدارة الفيديوهات"""
        try:
            from gui.video_history_window import VideoHistoryWindow
            VideoHistoryWindow(self.root)
        except Exception as e:
            logger.error(f"خطأ في فتح نافذة إدارة الفيديوهات: {e}")
            messagebox.showerror("خطأ", f"لا يمكن فتح نافذة إدارة الفيديوهات: {e}")

    # وظائف المساعدة
    def show_help(self):
        """عرض المساعدة"""
        messagebox.showinfo("المساعدة", "دليل المستخدم متاح في مجلد docs/")

    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = f"""{AppSettings.APP_NAME} v{AppSettings.APP_VERSION}

أداة تحرير فيديو احترافية ومجانية بالكامل
تعتمد على الذكاء الاصطناعي والأدوات مفتوحة المصدر

المطور: {AppSettings.APP_AUTHOR}
الترخيص: MIT License

للمزيد من المعلومات والدعم:
github.com/your-username/video-editor-pro"""

        messagebox.showinfo("حول البرنامج", about_text)

    def update_status(self, message: str):
        """تحديث شريط الحالة"""
        self.status_label.config(text=message)
        self.root.update_idletasks()

    def quit_app(self):
        """إغلاق التطبيق"""
        try:
            # تنظيف الملفات المؤقتة
            self.file_manager.cleanup_temp_files()

            # إغلاق التطبيق
            self.root.quit()
            self.root.destroy()

        except Exception as e:
            logger.error(f"خطأ في إغلاق التطبيق: {e}")
            self.root.destroy()
