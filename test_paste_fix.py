#!/usr/bin/env python3
"""
اختبار إصلاح ميزة لصق الرابط
Test the fixed paste URL feature
"""

import sys
import os
from pathlib import Path
import tkinter as tk
from tkinter import messagebox

# إضافة مجلد src إلى مسار Python
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_clipboard_access():
    """اختبار الوصول للحافظة"""
    
    print("📋 اختبار الوصول للحافظة")
    print("=" * 40)
    
    try:
        # إنشاء نافذة تجريبية
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # اختبار تعيين محتوى الحافظة
        test_content = "https://www.youtube.com/live/2GLBi1yhN8o?si=WrFbzhKsXdpJaHp_"
        root.clipboard_clear()
        root.clipboard_append(test_content)
        
        print("✅ تم تعيين محتوى الحافظة")
        
        # اختبار قراءة محتوى الحافظة
        clipboard_content = root.clipboard_get()
        
        if clipboard_content == test_content:
            print("✅ تم قراءة محتوى الحافظة بنجاح")
            print(f"المحتوى: {clipboard_content}")
            root.destroy()
            return True
        else:
            print(f"❌ محتوى الحافظة غير متطابق")
            print(f"متوقع: {test_content}")
            print(f"فعلي: {clipboard_content}")
            root.destroy()
            return False
            
    except tk.TclError as e:
        print(f"❌ خطأ في الوصول للحافظة: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_youtube_downloader_import():
    """اختبار استيراد محمل YouTube"""
    
    print("\n📥 اختبار استيراد محمل YouTube")
    print("=" * 40)
    
    try:
        from utils.youtube_downloader import YouTubeDownloader
        
        downloader = YouTubeDownloader()
        print("✅ تم استيراد وإنشاء محمل YouTube بنجاح")
        
        # اختبار وظيفة التحقق من الرابط
        test_url = "https://www.youtube.com/live/2GLBi1yhN8o?si=WrFbzhKsXdpJaHp_"
        is_valid = downloader.is_youtube_url(test_url)
        
        if is_valid:
            print("✅ وظيفة التحقق من الرابط تعمل")
            return True
        else:
            print("❌ وظيفة التحقق من الرابط لا تعمل")
            return False
            
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("تأكد من تثبيت مكتبة yt-dlp: pip install yt-dlp")
        return False
    except Exception as e:
        print(f"❌ خطأ في إنشاء المحمل: {e}")
        return False

def test_paste_function_simulation():
    """محاكاة وظيفة اللصق"""
    
    print("\n🔧 محاكاة وظيفة اللصق")
    print("=" * 40)
    
    try:
        # محاكاة الكلاس
        class MockLivestreamWindow:
            def __init__(self):
                self.root = tk.Tk()
                self.root.withdraw()
                
                # تهيئة المتغيرات
                self.video_path_var = tk.StringVar()
                
                # تهيئة محمل YouTube
                try:
                    from utils.youtube_downloader import YouTubeDownloader
                    self.youtube_downloader = YouTubeDownloader()
                except:
                    self.youtube_downloader = None
                
                # تعيين رابط تجريبي في الحافظة
                test_url = "https://www.youtube.com/live/2GLBi1yhN8o?si=WrFbzhKsXdpJaHp_"
                self.root.clipboard_clear()
                self.root.clipboard_append(test_url)
            
            def extract_video_id(self, url: str):
                """استخراج معرف الفيديو"""
                import re
                patterns = [
                    r'(?:youtube\.com/watch\?v=|youtu\.be/|youtube\.com/live/)([a-zA-Z0-9_-]{11})',
                    r'youtube\.com/embed/([a-zA-Z0-9_-]{11})',
                ]
                
                for pattern in patterns:
                    match = re.search(pattern, url)
                    if match:
                        return match.group(1)
                return None
            
            def update_status(self, message):
                """تحديث الحالة"""
                print(f"📊 حالة: {message}")
            
            def paste_url(self):
                """لصق رابط من الحافظة - النسخة المحدثة"""
                try:
                    # الحصول على محتوى الحافظة
                    clipboard_content = self.root.clipboard_get()
                    
                    if clipboard_content:
                        # تنظيف الرابط
                        url = clipboard_content.strip()
                        
                        # التحقق من صحة الرابط
                        if self.youtube_downloader and self.youtube_downloader.is_youtube_url(url):
                            self.video_path_var.set(url)
                            self.update_status("تم لصق رابط YouTube")
                            
                            # عرض معاينة سريعة للرابط
                            if "youtube.com" in url or "youtu.be" in url:
                                # استخراج معرف الفيديو للمعاينة
                                video_id = self.extract_video_id(url)
                                if video_id:
                                    self.update_status(f"تم لصق رابط فيديو: {video_id}")
                            
                            return True
                            
                        elif url.startswith(('http://', 'https://')):
                            print("⚠️ الرابط المنسوخ ليس من YouTube صحيح")
                            return False
                        else:
                            # إذا لم يكن رابط، قد يكون نص عادي
                            self.video_path_var.set(url)
                            print("⚠️ تأكد من أن هذا رابط YouTube صحيح")
                            return False
                    else:
                        print("ℹ️ الحافظة فارغة")
                        return False
                        
                except tk.TclError:
                    print("⚠️ لا يمكن الوصول للحافظة")
                    return False
                except Exception as e:
                    print(f"❌ خطأ في لصق الرابط: {e}")
                    return False
        
        # إنشاء نافذة وهمية واختبار اللصق
        mock_window = MockLivestreamWindow()
        
        print("اختبار وظيفة اللصق...")
        result = mock_window.paste_url()
        
        if result:
            pasted_url = mock_window.video_path_var.get()
            print(f"✅ تم لصق الرابط بنجاح: {pasted_url}")
        else:
            print("❌ فشل في لصق الرابط")
        
        mock_window.root.destroy()
        return result
        
    except Exception as e:
        print(f"❌ خطأ في محاكاة اللصق: {e}")
        return False

def test_keyboard_shortcuts():
    """اختبار اختصارات لوحة المفاتيح"""
    
    print("\n⌨️ اختبار اختصارات لوحة المفاتيح")
    print("=" * 40)
    
    try:
        root = tk.Tk()
        root.title("اختبار الاختصارات")
        root.geometry("400x200")
        
        # متغير للنص
        text_var = tk.StringVar()
        entry = tk.Entry(root, textvariable=text_var, width=50)
        entry.pack(pady=20)
        
        # متغير لتتبع الأحداث
        events_triggered = []
        
        # وظائف الاختبار
        def on_ctrl_v(event):
            events_triggered.append("Ctrl+V")
            try:
                clipboard_content = root.clipboard_get()
                text_var.set(clipboard_content)
                result_label.config(text=f"✅ Ctrl+V: {clipboard_content[:30]}...")
            except:
                result_label.config(text="❌ Ctrl+V: فشل في الوصول للحافظة")
            return "break"
        
        def on_enter(event):
            events_triggered.append("Enter")
            content = text_var.get()
            result_label.config(text=f"✅ Enter: {content[:30]}...")
            return "break"
        
        # ربط الأحداث
        entry.bind("<Control-v>", on_ctrl_v)
        entry.bind("<Return>", on_enter)
        
        # تسمية النتيجة
        result_label = tk.Label(root, text="اضغط Ctrl+V أو Enter لاختبار الاختصارات")
        result_label.pack(pady=10)
        
        # تعيين محتوى تجريبي في الحافظة
        test_url = "https://www.youtube.com/live/2GLBi1yhN8o"
        root.clipboard_clear()
        root.clipboard_append(test_url)
        
        # تعليمات
        instructions = tk.Label(root, 
                               text="1. اضغط Ctrl+V لاختبار اللصق\n2. اضغط Enter لاختبار الإدخال\n3. أغلق النافذة عند الانتهاء",
                               justify=tk.LEFT)
        instructions.pack(pady=10)
        
        print("✅ تم إعداد اختبار الاختصارات")
        print("نافذة الاختبار مفتوحة - اختبر الاختصارات وأغلق النافذة")
        
        # تشغيل النافذة
        root.mainloop()
        
        # فحص النتائج
        if events_triggered:
            print(f"✅ تم تشغيل الأحداث: {', '.join(events_triggered)}")
            return True
        else:
            print("⚠️ لم يتم تشغيل أي أحداث")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الاختصارات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🔧 اختبار إصلاح ميزة لصق الرابط")
    print("=" * 60)
    
    results = {}
    
    # اختبار الوصول للحافظة
    results["clipboard"] = test_clipboard_access()
    
    # اختبار استيراد محمل YouTube
    results["youtube_downloader"] = test_youtube_downloader_import()
    
    # اختبار محاكاة وظيفة اللصق
    results["paste_function"] = test_paste_function_simulation()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("🎯 النتيجة النهائية")
    print("=" * 60)
    
    working_features = sum(results.values())
    total_features = len(results)
    
    feature_names = {
        "clipboard": "الوصول للحافظة",
        "youtube_downloader": "محمل YouTube",
        "paste_function": "وظيفة اللصق"
    }
    
    for feature, status in results.items():
        status_icon = "✅" if status else "❌"
        name = feature_names.get(feature, feature)
        print(f"{status_icon} {name}: {'يعمل' if status else 'لا يعمل'}")
    
    print(f"\nالنتيجة: {working_features}/{total_features} ميزات تعمل")
    
    if working_features == total_features:
        print("\n🎉 تم إصلاح جميع مشاكل اللصق!")
        print("\nيمكنك الآن:")
        print("1. تشغيل محلل البثوث: python run_livestream_analyzer.py")
        print("2. اختيار 'رابط YouTube'")
        print("3. استخدام زر 'لصق' أو Ctrl+V بأمان")
        
        # اختبار الاختصارات (اختياري)
        response = input("\nهل تريد اختبار اختصارات لوحة المفاتيح؟ (y/n): ")
        if response.lower() in ['y', 'yes', 'نعم', 'ن']:
            test_keyboard_shortcuts()
        
        return 0
    else:
        print("\n⚠️ بعض المشاكل لا تزال موجودة")
        
        if not results["youtube_downloader"]:
            print("💡 نصيحة: قم بتثبيت yt-dlp: pip install yt-dlp")
        
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
