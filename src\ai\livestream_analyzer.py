"""
محلل البثوث المباشرة المتخصص
Specialized livestream analyzer for extracting viral moments
"""

import logging
from typing import Optional, Dict, List, Any, Tuple
from pathlib import Path
import json
import time
from datetime import datetime, timedelta

# استيراد اختياري للمكتبات المتقدمة
try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False

try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False

from ai.huggingface_client import HuggingFaceClient
from ai.google_cloud_client import GoogleCloudClient
from ai.gemini_client import GeminiClient
from ai.assemblyai_client import AssemblyAIClient
from core.video_processor import VideoProcessor
from utils.file_utils import FileManager
from ai.caption_generator import CaptionGenerator
from ai.advanced_highlight_detector import AdvancedHighlightDetector
from utils.video_history_manager import VideoHistoryManager

logger = logging.getLogger(__name__)

class LivestreamAnalyzer:
    """محلل البثوث المباشرة لاستخراج اللحظات الفيروسية"""
    
    def __init__(self):
        self.hf_client = HuggingFaceClient()
        self.gc_client = GoogleCloudClient()
        self.gemini_client = GeminiClient()
        self.assemblyai_client = AssemblyAIClient()
        self.video_processor = VideoProcessor()
        self.file_manager = FileManager()
        self.caption_generator = CaptionGenerator()

        # النظام المتقدم لاكتشاف اللقطات
        self.advanced_detector = AdvancedHighlightDetector()

        # مدير تاريخ الفيديوهات
        self.history_manager = VideoHistoryManager()
        
        # إعدادات تحليل البث
        self.chunk_duration = 300  # 5 دقائق لكل قطعة تحليل
        self.min_clip_duration = 15  # 15 ثانية كحد أدنى للمقطع
        self.max_clip_duration = 60  # 60 ثانية كحد أقصى للمقطع
        self.excitement_threshold = 0.7  # حد الإثارة
        
        # كلمات مفتاحية للحظات المثيرة
        self.excitement_keywords = [
            # إنجليزي
            "wow", "omg", "no way", "insane", "crazy", "unbelievable", 
            "what", "how", "amazing", "incredible", "sick", "fire",
            "let's go", "yooo", "bruh", "damn", "holy", "jesus",
            # عربي
            "واو", "لا يصدق", "مجنون", "رهيب", "جامد", "عجيب",
            "يا إلهي", "مستحيل", "خرافي", "أسطوري"
        ]
        
        # كلمات مفتاحية للحظات المضحكة
        self.funny_keywords = [
            "haha", "lol", "lmao", "funny", "hilarious", "joke",
            "laugh", "comedy", "meme", "troll", "rofl",
            "ضحك", "مضحك", "كوميدي", "نكتة", "مزحة", "ترول"
        ]
        
        # كلمات مفتاحية للحظات الصادمة
        self.shocking_keywords = [
            "shock", "surprised", "unexpected", "plot twist", "reveal",
            "exposed", "drama", "tea", "scandal", "controversy",
            "صدمة", "مفاجأة", "غير متوقع", "فضيحة", "دراما", "كشف"
        ]
        
        logger.info("تم تهيئة محلل البثوث المباشرة")

    def analyze_long_livestream_advanced(self, video_path: str,
                                        target_clips: int = 5) -> List[Dict[str, Any]]:
        """تحليل متقدم للبث المباشر الطويل باستخدام النظام الجديد"""
        try:
            logger.info(f"بدء التحليل المتقدم للبث المباشر: {video_path}")

            # استخدام النظام المتقدم لاكتشاف اللقطات
            highlights = self.advanced_detector.detect_highlights(video_path, target_clips)

            if not highlights:
                logger.warning("لم يتم العثور على لقطات بارزة باستخدام النظام المتقدم")
                # العودة للنظام القديم كبديل
                return self.analyze_long_livestream(video_path, target_clips)

            # تحويل النتائج إلى التنسيق المطلوب
            results = []
            for highlight in highlights:
                # إنشاء مقطع الشورتس
                clip_result = self._create_shorts_clip_from_highlight(video_path, highlight)
                if clip_result:
                    results.append(clip_result)

            logger.info(f"تم إنشاء {len(results)} مقطع شورتس باستخدام النظام المتقدم")
            return results

        except Exception as e:
            logger.error(f"خطأ في التحليل المتقدم: {e}")
            # العودة للنظام القديم في حالة الخطأ
            return self.analyze_long_livestream(video_path, target_clips)

    def _create_shorts_clip_from_highlight(self, video_path: str,
                                          highlight) -> Optional[Dict[str, Any]]:
        """إنشاء مقطع شورتس من لقطة بارزة"""
        try:
            # تحديد مدة المقطع (15-60 ثانية)
            duration = highlight.end_time - highlight.start_time
            if duration < 15:
                # توسيع المقطع إلى 15 ثانية على الأقل
                center_time = (highlight.start_time + highlight.end_time) / 2
                highlight.start_time = max(0, center_time - 7.5)
                highlight.end_time = highlight.start_time + 15
            elif duration > 60:
                # تقليص المقطع إلى 60 ثانية
                highlight.end_time = highlight.start_time + 60

            # إنشاء اسم الملف
            timestamp = int(highlight.start_time)
            filename = f"shorts_{highlight.highlight_type.value}_{timestamp}.mp4"
            output_path = self.file_manager.get_output_path(filename)

            # قص الفيديو
            success = self.video_processor.trim_video(
                video_path, str(output_path),
                highlight.start_time, highlight.end_time
            )

            if not success:
                logger.error(f"فشل في قص الفيديو للقطة: {highlight.start_time}-{highlight.end_time}")
                return None

            # إنشاء التعليقات التوضيحية
            captions = self._generate_captions_for_highlight(highlight)

            # إنشاء عنوان جذاب
            title = self._generate_engaging_title(highlight)

            # إنشاء هاشتاجات
            hashtags = self._generate_hashtags(highlight)

            return {
                'file_path': str(output_path),
                'start_time': highlight.start_time,
                'end_time': highlight.end_time,
                'duration': highlight.end_time - highlight.start_time,
                'type': highlight.highlight_type.value,
                'confidence': highlight.confidence,
                'title': title,
                'description': highlight.description,
                'hashtags': hashtags,
                'captions': captions,
                'text': highlight.text,
                'sentiment_score': highlight.sentiment_score,
                'audio_features': highlight.audio_features,
                'visual_features': highlight.visual_features
            }

        except Exception as e:
            logger.error(f"خطأ في إنشاء مقطع الشورتس: {e}")
            return None

    def _generate_captions_for_highlight(self, highlight) -> List[Dict[str, Any]]:
        """إنشاء تعليقات توضيحية للقطة البارزة"""
        try:
            if not highlight.text:
                return []

            # تقسيم النص إلى جمل قصيرة للتعليقات
            sentences = highlight.text.split('.')
            duration = highlight.end_time - highlight.start_time
            sentence_duration = duration / max(len(sentences), 1)

            captions = []
            for i, sentence in enumerate(sentences):
                sentence = sentence.strip()
                if len(sentence) > 3:
                    caption = {
                        'start_time': i * sentence_duration,
                        'end_time': (i + 1) * sentence_duration,
                        'text': sentence,
                        'style': 'highlight'  # نمط خاص للقطات البارزة
                    }
                    captions.append(caption)

            return captions

        except Exception as e:
            logger.error(f"خطأ في إنشاء التعليقات: {e}")
            return []

    def _generate_engaging_title(self, highlight) -> str:
        """إنشاء عنوان جذاب للقطة"""
        try:
            # قوالب العناوين حسب نوع اللقطة
            title_templates = {
                'exciting': [
                    "🔥 لحظة لا تصدق!",
                    "⚡ إثارة خالصة!",
                    "🚀 لحظة أسطورية!",
                    "💥 طاقة جنونية!"
                ],
                'funny': [
                    "😂 ضحك حتى البكاء!",
                    "🤣 كوميديا خالصة!",
                    "😆 لحظة مضحكة جداً!",
                    "😄 ضحك لا يتوقف!"
                ],
                'shocking': [
                    "😱 صدمة لا تصدق!",
                    "🤯 مفاجأة مذهلة!",
                    "😲 لم أتوقع هذا!",
                    "🫨 صدمة العمر!"
                ],
                'emotional': [
                    "💔 لحظة مؤثرة جداً",
                    "😢 عواطف جياشة",
                    "❤️ لحظة من القلب",
                    "🥺 مشاعر حقيقية"
                ],
                'epic': [
                    "👑 لحظة تاريخية!",
                    "🏆 إنجاز أسطوري!",
                    "⭐ لحظة لا تُنسى!",
                    "🎯 هدف مثالي!"
                ]
            }

            # اختيار عنوان عشوائي من القوالب
            templates = title_templates.get(highlight.highlight_type.value, ["🎬 لحظة بارزة!"])
            import random
            base_title = random.choice(templates)

            # إضافة معلومات إضافية إذا كان النص متوفراً
            if highlight.text and len(highlight.text) > 10:
                # استخراج كلمة مفتاحية من النص
                words = highlight.text.split()
                important_words = [w for w in words if len(w) > 4 and w.lower() not in ['this', 'that', 'with', 'from']]
                if important_words:
                    keyword = important_words[0].title()
                    base_title += f" | {keyword}"

            return base_title

        except Exception as e:
            logger.error(f"خطأ في إنشاء العنوان: {e}")
            return "🎬 لحظة بارزة!"

    def _generate_hashtags(self, highlight) -> List[str]:
        """إنشاء هاشتاجات للقطة"""
        try:
            hashtags = []

            # هاشتاجات أساسية
            hashtags.extend(['#shorts', '#viral', '#trending'])

            # هاشتاجات حسب نوع اللقطة
            type_hashtags = {
                'exciting': ['#exciting', '#amazing', '#wow', '#epic', '#insane'],
                'funny': ['#funny', '#comedy', '#lol', '#hilarious', '#meme'],
                'shocking': ['#shocking', '#surprise', '#unexpected', '#mindblown'],
                'emotional': ['#emotional', '#touching', '#feelings', '#heartwarming'],
                'epic': ['#epic', '#legendary', '#awesome', '#incredible', '#amazing']
            }

            specific_tags = type_hashtags.get(highlight.highlight_type.value, [])
            hashtags.extend(specific_tags[:3])  # أخذ أول 3 هاشتاجات

            # هاشتاجات من النص
            if highlight.text:
                words = highlight.text.lower().split()
                # البحث عن كلمات مناسبة للهاشتاجات
                for word in words:
                    word = word.strip('.,!?')
                    if (len(word) > 3 and
                        word not in ['this', 'that', 'with', 'from', 'they', 'have', 'will'] and
                        len(hashtags) < 10):
                        hashtags.append(f'#{word}')

            return hashtags[:10]  # حد أقصى 10 هاشتاجات

        except Exception as e:
            logger.error(f"خطأ في إنشاء الهاشتاجات: {e}")
            return ['#shorts', '#viral']

    def save_video_to_history(self, title: str, url: str, file_path: str,
                             duration: float, info_file_path: str = None) -> str:
        """حفظ الفيديو في تاريخ الفيديوهات"""
        try:
            video_id = self.history_manager.add_downloaded_video(
                title=title,
                url=url,
                file_path=file_path,
                duration=duration,
                info_file_path=info_file_path
            )
            logger.info(f"تم حفظ الفيديو في التاريخ: {title} (ID: {video_id})")
            return video_id
        except Exception as e:
            logger.error(f"خطأ في حفظ الفيديو في التاريخ: {e}")
            return None

    def analyze_long_livestream(self, video_path: str,
                               target_clips: int = 5) -> List[Dict[str, Any]]:
        """تحليل بث مباشر طويل واستخراج أفضل اللحظات"""
        try:
            logger.info(f"بدء تحليل البث المباشر: {video_path}")
            
            # الحصول على معلومات الفيديو
            video_info = self.video_processor.get_video_info(video_path)
            if not video_info:
                logger.error("لا يمكن الحصول على معلومات الفيديو")
                return []
            
            duration = video_info['duration']
            logger.info(f"مدة البث: {duration/3600:.1f} ساعة")
            
            if duration < 600:  # أقل من 10 دقائق
                logger.warning("البث قصير جداً للتحليل المتقدم")
                return self._analyze_short_video(video_path, target_clips)
            
            # تقسيم البث إلى قطع للتحليل
            chunks = self._create_analysis_chunks(duration)
            logger.info(f"تم تقسيم البث إلى {len(chunks)} قطعة للتحليل")
            
            # تحليل كل قطعة
            all_moments = []
            for i, (start_time, end_time) in enumerate(chunks):
                logger.info(f"تحليل القطعة {i+1}/{len(chunks)}: {start_time/60:.1f}-{end_time/60:.1f} دقيقة")
                
                chunk_moments = self._analyze_chunk(
                    video_path, start_time, end_time
                )
                all_moments.extend(chunk_moments)
                
                # استراحة قصيرة لتجنب إرهاق النظام
                time.sleep(1)
            
            # ترتيب وفلترة أفضل اللحظات
            best_moments = self._select_best_moments(all_moments, target_clips)
            
            # إنشاء مقاطع الشورتس
            shorts_clips = []
            for moment in best_moments:
                clip = self._create_shorts_clip(video_path, moment)
                if clip:
                    shorts_clips.append(clip)
            
            logger.info(f"تم إنشاء {len(shorts_clips)} مقطع شورتس")
            return shorts_clips
            
        except Exception as e:
            logger.error(f"خطأ في تحليل البث المباشر: {e}")
            return []
    
    def _create_analysis_chunks(self, total_duration: float) -> List[Tuple[float, float]]:
        """تقسيم البث إلى قطع للتحليل"""
        chunks = []
        current_time = 0
        
        while current_time < total_duration:
            end_time = min(current_time + self.chunk_duration, total_duration)
            chunks.append((current_time, end_time))
            current_time = end_time
        
        return chunks
    
    def _analyze_chunk(self, video_path: str, start_time: float, 
                      end_time: float) -> List[Dict[str, Any]]:
        """تحليل قطعة من البث"""
        try:
            # استخراج الصوت من القطعة
            audio_path = self.file_manager.create_temp_file(suffix='.wav')
            
            # قص الصوت من القطعة المحددة
            success = self._extract_chunk_audio(
                video_path, str(audio_path), start_time, end_time
            )
            
            if not success:
                return []
            
            # تحويل الكلام إلى نص
            transcript = self._transcribe_chunk_audio(str(audio_path))
            
            # تنظيف الملف المؤقت
            audio_path.unlink(missing_ok=True)
            
            if not transcript:
                return []
            
            # تحليل النص للعثور على اللحظات المثيرة
            moments = self._analyze_transcript_for_moments(
                transcript, start_time, end_time
            )
            
            # تحليل الصوت للعثور على ذروات الإثارة
            audio_moments = self._analyze_audio_excitement(
                video_path, start_time, end_time
            )
            
            # دمج النتائج
            combined_moments = self._combine_analysis_results(
                moments, audio_moments, start_time
            )
            
            return combined_moments
            
        except Exception as e:
            logger.error(f"خطأ في تحليل القطعة: {e}")
            return []
    
    def _extract_chunk_audio(self, video_path: str, audio_path: str,
                           start_time: float, end_time: float) -> bool:
        """استخراج الصوت من قطعة محددة"""
        try:
            import subprocess
            
            cmd = [
                self.video_processor.ffmpeg_path,
                '-i', video_path,
                '-ss', str(start_time),
                '-t', str(end_time - start_time),
                '-vn',  # بدون فيديو
                '-acodec', 'pcm_s16le',
                '-ar', '16000',  # 16kHz للتوافق مع APIs
                '-ac', '1',  # mono
                '-y',
                audio_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
            
        except Exception as e:
            logger.error(f"خطأ في استخراج الصوت: {e}")
            return False
    
    def _transcribe_chunk_audio(self, audio_path: str) -> Optional[str]:
        """تحويل صوت القطعة إلى نص"""
        try:
            # محاولة استخدام AssemblyAI أولاً (أفضل جودة)
            if self.assemblyai_client.is_available():
                transcript = self.assemblyai_client.transcribe_audio(audio_path, "en")
                if transcript:
                    logger.info("تم التحويل باستخدام AssemblyAI")
                    return transcript

            # محاولة استخدام Google Cloud كبديل
            if self.gc_client.is_available():
                transcript = self.gc_client.transcribe_audio(audio_path, "en-US")
                if transcript:
                    logger.info("تم التحويل باستخدام Google Cloud")
                    return transcript

            # محاولة استخدام Hugging Face كبديل أخير
            if self.hf_client.api_key:
                transcript = self.hf_client.speech_to_text(audio_path, "en")
                if transcript:
                    logger.info("تم التحويل باستخدام Hugging Face")
                    return transcript

            logger.warning("لا توجد خدمة متاحة لتحويل الكلام إلى نص")
            return None

        except Exception as e:
            logger.error(f"خطأ في تحويل الكلام إلى نص: {e}")
            return None
    
    def _analyze_transcript_for_moments(self, transcript: str, 
                                      start_time: float, end_time: float) -> List[Dict[str, Any]]:
        """تحليل النص للعثور على اللحظات المثيرة"""
        moments = []
        
        try:
            # تقسيم النص إلى جمل
            sentences = transcript.split('.')
            sentence_duration = (end_time - start_time) / len(sentences)
            
            for i, sentence in enumerate(sentences):
                sentence = sentence.strip().lower()
                if len(sentence) < 5:
                    continue
                
                sentence_start = start_time + (i * sentence_duration)
                sentence_end = sentence_start + sentence_duration
                
                # تحليل نوع اللحظة
                moment_type, confidence = self._classify_moment_type(sentence)
                
                if confidence > self.excitement_threshold:
                    moment = {
                        'start_time': sentence_start,
                        'end_time': sentence_end,
                        'type': moment_type,
                        'confidence': confidence,
                        'text': sentence,
                        'source': 'transcript'
                    }
                    moments.append(moment)
            
            return moments
            
        except Exception as e:
            logger.error(f"خطأ في تحليل النص: {e}")
            return []
    
    def _classify_moment_type(self, text: str) -> Tuple[str, float]:
        """تصنيف نوع اللحظة وحساب الثقة"""
        excitement_score = 0
        funny_score = 0
        shocking_score = 0
        
        # حساب نقاط كل نوع
        for keyword in self.excitement_keywords:
            if keyword in text:
                excitement_score += 1
        
        for keyword in self.funny_keywords:
            if keyword in text:
                funny_score += 1
        
        for keyword in self.shocking_keywords:
            if keyword in text:
                shocking_score += 1
        
        # تحديد النوع الأعلى نقاطاً
        scores = {
            'exciting': excitement_score,
            'funny': funny_score,
            'shocking': shocking_score
        }
        
        best_type = max(scores, key=scores.get)
        max_score = scores[best_type]
        
        # تحويل النقاط إلى نسبة ثقة
        confidence = min(max_score / 3.0, 1.0)  # تطبيع إلى 0-1
        
        return best_type, confidence
    
    def _analyze_audio_excitement(self, video_path: str, start_time: float, 
                                end_time: float) -> List[Dict[str, Any]]:
        """تحليل الصوت للعثور على ذروات الإثارة"""
        try:
            # استخراج الصوت للتحليل
            audio_path = self.file_manager.create_temp_file(suffix='.wav')
            
            success = self._extract_chunk_audio(
                video_path, str(audio_path), start_time, end_time
            )
            
            if not success:
                return []
            
            # تحليل مستوى الصوت
            volume_peaks = self._detect_volume_peaks(str(audio_path))
            
            # تحويل ذروات الصوت إلى لحظات
            audio_moments = []
            for peak_time, volume_level in volume_peaks:
                if volume_level > 0.8:  # ذروة عالية
                    moment_start = start_time + peak_time - 5  # 5 ثواني قبل الذروة
                    moment_end = start_time + peak_time + 10   # 10 ثواني بعد الذروة
                    
                    moment = {
                        'start_time': max(moment_start, start_time),
                        'end_time': min(moment_end, end_time),
                        'type': 'exciting',
                        'confidence': volume_level,
                        'text': f'ذروة صوتية عند {peak_time:.1f}s',
                        'source': 'audio_analysis'
                    }
                    audio_moments.append(moment)
            
            # تنظيف الملف المؤقت
            audio_path.unlink(missing_ok=True)
            
            return audio_moments
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الصوت: {e}")
            return []
    
    def _detect_volume_peaks(self, audio_path: str) -> List[Tuple[float, float]]:
        """كشف ذروات مستوى الصوت"""
        try:
            # استخدام librosa إذا كان متوفراً، وإلا استخدام طريقة بديلة
            try:
                import librosa
                y, sr = librosa.load(audio_path)
                
                # حساب RMS (Root Mean Square) للطاقة
                hop_length = 512
                frame_length = 2048
                rms = librosa.feature.rms(y=y, frame_length=frame_length, 
                                        hop_length=hop_length)[0]
                
                # تحويل إلى وقت
                times = librosa.frames_to_time(range(len(rms)), sr=sr,
                                             hop_length=hop_length)

                # العثور على الذروات
                peaks = []
                if NUMPY_AVAILABLE:
                    import numpy as np
                    threshold = np.mean(rms) + 2 * np.std(rms)

                    for i, (time, energy) in enumerate(zip(times, rms)):
                        if energy > threshold:
                            # تطبيع الطاقة إلى 0-1
                            normalized_energy = min(energy / threshold, 1.0)
                            peaks.append((time, normalized_energy))
                else:
                    # حساب بسيط بدون numpy
                    mean_rms = sum(rms) / len(rms)
                    threshold = mean_rms * 1.5  # تقريب بسيط

                    for i, (time, energy) in enumerate(zip(times, rms)):
                        if energy > threshold:
                            normalized_energy = min(energy / threshold, 1.0)
                            peaks.append((time, normalized_energy))
                
                return peaks
                
            except ImportError:
                # طريقة بديلة باستخدام FFmpeg
                return self._detect_volume_peaks_ffmpeg(audio_path)
                
        except Exception as e:
            logger.error(f"خطأ في كشف ذروات الصوت: {e}")
            return []
    
    def _detect_volume_peaks_ffmpeg(self, audio_path: str) -> List[Tuple[float, float]]:
        """كشف ذروات الصوت باستخدام FFmpeg"""
        try:
            import subprocess
            
            # استخدام FFmpeg لتحليل مستوى الصوت
            cmd = [
                self.video_processor.ffmpeg_path,
                '-i', audio_path,
                '-af', 'volumedetect',
                '-f', 'null',
                '-'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            # تحليل بسيط - في التطبيق الحقيقي نحتاج تحليل أكثر تفصيلاً
            peaks = []
            if 'max_volume' in result.stderr:
                # إضافة ذروة واحدة في منتصف المقطع كمثال
                peaks.append((15.0, 0.9))  # ذروة عند 15 ثانية بقوة 0.9
            
            return peaks
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الصوت بـ FFmpeg: {e}")
            return []
    
    def _combine_analysis_results(self, transcript_moments: List[Dict], 
                                audio_moments: List[Dict], 
                                chunk_start: float) -> List[Dict[str, Any]]:
        """دمج نتائج تحليل النص والصوت"""
        combined = []
        
        # إضافة لحظات النص
        combined.extend(transcript_moments)
        
        # إضافة لحظات الصوت مع تجنب التداخل
        for audio_moment in audio_moments:
            # التحقق من عدم التداخل مع لحظات النص
            overlaps = False
            for text_moment in transcript_moments:
                if (audio_moment['start_time'] < text_moment['end_time'] and
                    audio_moment['end_time'] > text_moment['start_time']):
                    overlaps = True
                    break
            
            if not overlaps:
                combined.append(audio_moment)
        
        return combined
    
    def _select_best_moments(self, all_moments: List[Dict], 
                           target_clips: int) -> List[Dict[str, Any]]:
        """اختيار أفضل اللحظات"""
        if not all_moments:
            return []
        
        # ترتيب حسب الثقة
        sorted_moments = sorted(all_moments, key=lambda x: x['confidence'], reverse=True)
        
        # فلترة اللحظات المتداخلة
        filtered_moments = []
        for moment in sorted_moments:
            # التحقق من عدم التداخل مع اللحظات المختارة
            overlaps = False
            for selected in filtered_moments:
                if (moment['start_time'] < selected['end_time'] and
                    moment['end_time'] > selected['start_time']):
                    overlaps = True
                    break
            
            if not overlaps:
                filtered_moments.append(moment)
                
                if len(filtered_moments) >= target_clips:
                    break
        
        return filtered_moments
    
    def _analyze_short_video(self, video_path: str, target_clips: int) -> List[Dict[str, Any]]:
        """تحليل فيديو قصير (أقل من 10 دقائق)"""
        try:
            # للفيديوهات القصيرة، نحلل الفيديو كاملاً
            video_info = self.video_processor.get_video_info(video_path)
            duration = video_info['duration']

            moments = self._analyze_chunk(video_path, 0, duration)
            best_moments = self._select_best_moments(moments, target_clips)

            # إنشاء مقاطع الشورتس
            shorts_clips = []
            for moment in best_moments:
                clip = self._create_shorts_clip(video_path, moment)
                if clip:
                    shorts_clips.append(clip)

            logger.info(f"تم إنشاء {len(shorts_clips)} مقطع شورتس من الفيديو القصير")
            return shorts_clips

        except Exception as e:
            logger.error(f"خطأ في تحليل الفيديو القصير: {e}")
            return []

    def _create_shorts_clip(self, video_path: str, moment: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """إنشاء مقطع شورتس من اللحظة المحددة"""
        try:
            # تحديد مدة المقطع
            moment_duration = moment['end_time'] - moment['start_time']

            # تعديل المدة لتناسب الشورتس
            if moment_duration < self.min_clip_duration:
                # توسيع المقطع
                extension = (self.min_clip_duration - moment_duration) / 2
                start_time = max(0, moment['start_time'] - extension)
                end_time = moment['end_time'] + extension
            elif moment_duration > self.max_clip_duration:
                # تقصير المقطع للتركيز على الذروة
                start_time = moment['start_time']
                end_time = start_time + self.max_clip_duration
            else:
                start_time = moment['start_time']
                end_time = moment['end_time']

            # إنشاء اسم ملف فريد
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            clip_filename = f"shorts_{moment['type']}_{timestamp}.mp4"
            clip_path = self.file_manager.output_dir / clip_filename

            # قص الفيديو
            success = self.video_processor.trim_video(
                video_path, str(clip_path), start_time, end_time
            )

            if not success:
                logger.error("فشل في قص المقطع")
                return None

            # تحويل إلى تنسيق عمودي (9:16) للشورتس
            vertical_clip_path = self.file_manager.output_dir / f"vertical_{clip_filename}"
            success = self._convert_to_vertical(str(clip_path), str(vertical_clip_path))

            if not success:
                logger.warning("فشل في التحويل العمودي، سيتم استخدام المقطع الأصلي")
                vertical_clip_path = clip_path

            # إنشاء التعليقات التوضيحية
            captions = self._generate_clip_captions(
                str(vertical_clip_path), start_time, end_time
            )

            # إضافة النصوص على الفيديو
            final_clip_path = self._add_captions_to_video(
                str(vertical_clip_path), captions, moment
            )

            # إنشاء معلومات المقطع
            clip_info = {
                'file_path': str(final_clip_path),
                'original_start_time': start_time,
                'original_end_time': end_time,
                'duration': end_time - start_time,
                'type': moment['type'],
                'confidence': moment['confidence'],
                'description': self._generate_clip_description(moment),
                'captions': captions,
                'suggested_title': self._generate_clip_title(moment),
                'hashtags': self._generate_clip_hashtags(moment),
                'thumbnail_time': (start_time + end_time) / 2  # منتصف المقطع للصورة المصغرة
            }

            logger.info(f"تم إنشاء مقطع شورتس: {clip_filename}")
            return clip_info

        except Exception as e:
            logger.error(f"خطأ في إنشاء مقطع الشورتس: {e}")
            return None

    def _convert_to_vertical(self, input_path: str, output_path: str) -> bool:
        """تحويل الفيديو إلى تنسيق عمودي (9:16)"""
        try:
            # الحصول على معلومات الفيديو الأصلي
            video_info = self.video_processor.get_video_info(input_path)
            original_width = video_info['video']['width']
            original_height = video_info['video']['height']

            # حساب الأبعاد الجديدة (9:16)
            target_width = 1080
            target_height = 1920

            # تحديد طريقة التحويل بناءً على النسبة الأصلية
            if original_width > original_height:
                # فيديو أفقي - نحتاج لقص أو تكبير
                scale_factor = target_height / original_height
                scaled_width = int(original_width * scale_factor)

                if scaled_width >= target_width:
                    # قص من الجانبين
                    crop_x = (scaled_width - target_width) // 2
                    filter_complex = f"scale={scaled_width}:{target_height},crop={target_width}:{target_height}:{crop_x}:0"
                else:
                    # إضافة حدود سوداء
                    filter_complex = f"scale={scaled_width}:{target_height},pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2:black"
            else:
                # فيديو عمودي أو مربع
                success = self.video_processor.resize_video(
                    input_path, output_path, target_width, target_height, 'medium'
                )
                return success

            # تطبيق الفلتر
            import subprocess
            cmd = [
                self.video_processor.ffmpeg_path,
                '-i', input_path,
                '-vf', filter_complex,
                '-c:a', 'copy',
                '-y',
                output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0

        except Exception as e:
            logger.error(f"خطأ في التحويل العمودي: {e}")
            return False

    def _generate_clip_captions(self, video_path: str, start_time: float,
                              end_time: float) -> List[Dict[str, Any]]:
        """إنشاء تعليقات توضيحية للمقطع"""
        try:
            # استخراج الصوت من المقطع
            audio_path = self.file_manager.create_temp_file(suffix='.wav')

            success = self.video_processor.extract_audio(video_path, str(audio_path))
            if not success:
                return []

            # تحويل الكلام إلى نص
            transcript = None

            # محاولة استخدام Google Cloud أولاً
            if self.gc_client.is_available():
                transcript = self.gc_client.transcribe_audio(str(audio_path), "en-US")

            # محاولة استخدام Hugging Face كبديل
            if not transcript and self.hf_client.api_key:
                transcript = self.hf_client.speech_to_text(str(audio_path), "en")

            # تنظيف الملف المؤقت
            audio_path.unlink(missing_ok=True)

            if not transcript:
                return []

            # تقسيم النص إلى تعليقات مؤقتة
            captions = self.caption_generator._create_timed_captions(transcript, video_path)

            # ترجمة إلى العربية إذا كان النص بالإنجليزية
            if self._is_english_text(transcript):
                arabic_captions = []
                for caption in captions:
                    arabic_text = self._translate_to_arabic(caption['text'])
                    if arabic_text:
                        arabic_caption = caption.copy()
                        arabic_caption['text'] = arabic_text
                        arabic_caption['original_text'] = caption['text']
                        arabic_captions.append(arabic_caption)

                return arabic_captions

            return captions

        except Exception as e:
            logger.error(f"خطأ في إنشاء التعليقات التوضيحية: {e}")
            return []

    def _is_english_text(self, text: str) -> bool:
        """التحقق من كون النص بالإنجليزية"""
        # فحص بسيط - إذا كان معظم النص أحرف لاتينية
        latin_chars = sum(1 for c in text if c.isascii() and c.isalpha())
        total_chars = sum(1 for c in text if c.isalpha())

        if total_chars == 0:
            return False

        return (latin_chars / total_chars) > 0.7

    def _translate_to_arabic(self, text: str) -> Optional[str]:
        """ترجمة النص إلى العربية"""
        try:
            # محاولة استخدام Gemini أولاً (أفضل جودة)
            if self.gemini_client.is_available():
                translated = self.gemini_client.translate_text(text, "ar", "en")
                if translated:
                    logger.info("تم الترجمة باستخدام Gemini")
                    return translated

            # محاولة استخدام Google Cloud كبديل
            if self.gc_client.is_available():
                translated = self.gc_client.translate_text_google(text, "ar")
                if translated:
                    logger.info("تم الترجمة باستخدام Google Cloud")
                    return translated

            # محاولة استخدام Hugging Face كبديل أخير
            if self.hf_client.api_key:
                translated = self.hf_client.translate_text(text, "en", "ar")
                if translated:
                    logger.info("تم الترجمة باستخدام Hugging Face")
                    return translated

            logger.warning("لا توجد خدمة متاحة للترجمة")
            return None

        except Exception as e:
            logger.error(f"خطأ في الترجمة: {e}")
            return None

    def _add_captions_to_video(self, video_path: str, captions: List[Dict],
                             moment: Dict[str, Any]) -> str:
        """إضافة التعليقات التوضيحية على الفيديو"""
        try:
            if not captions:
                return video_path

            # إنشاء مسار الملف النهائي
            final_path = video_path.replace('.mp4', '_with_captions.mp4')

            # إنشاء فلتر النصوص
            text_filters = []
            for i, caption in enumerate(captions):
                # تنظيف النص
                clean_text = caption['text'].replace("'", "\\'").replace('"', '\\"')

                # تحديد موقع النص (أسفل الشاشة للشورتس)
                text_filter = (
                    f"drawtext=text='{clean_text}'"
                    f":fontsize=32"
                    f":fontcolor=white"
                    f":x=(w-text_w)/2"
                    f":y=h-text_h-50"
                    f":enable='between(t,{caption['start_time']},{caption['end_time']})'"
                    f":box=1:boxcolor=black@0.5:boxborderw=5"
                )
                text_filters.append(text_filter)

            # دمج جميع فلاتر النصوص
            if text_filters:
                combined_filter = ','.join(text_filters)

                import subprocess
                cmd = [
                    self.video_processor.ffmpeg_path,
                    '-i', video_path,
                    '-vf', combined_filter,
                    '-c:a', 'copy',
                    '-y',
                    final_path
                ]

                result = subprocess.run(cmd, capture_output=True, text=True)

                if result.returncode == 0:
                    return final_path
                else:
                    logger.error(f"خطأ في إضافة النصوص: {result.stderr}")
                    return video_path

            return video_path

        except Exception as e:
            logger.error(f"خطأ في إضافة التعليقات على الفيديو: {e}")
            return video_path

    def _generate_clip_description(self, moment: Dict[str, Any]) -> str:
        """توليد وصف للمقطع"""
        type_descriptions = {
            'exciting': 'لحظة مثيرة ومليئة بالإثارة',
            'funny': 'لحظة مضحكة ومسلية',
            'shocking': 'لحظة صادمة وغير متوقعة'
        }

        base_description = type_descriptions.get(moment['type'], 'لحظة مميزة')

        if moment.get('text'):
            return f"{base_description} - {moment['text'][:100]}..."

        return base_description

    def _generate_clip_title(self, moment: Dict[str, Any]) -> str:
        """توليد عنوان للمقطع"""
        try:
            # محاولة استخدام Gemini لتوليد عنوان ذكي
            if self.gemini_client.is_available():
                description = moment.get('text', moment.get('description', ''))
                moment_type = moment.get('type', 'exciting')

                smart_title = self.gemini_client.generate_video_title(description, moment_type)
                if smart_title:
                    logger.info("تم توليد عنوان ذكي باستخدام Gemini")
                    return smart_title

            # العناوين الافتراضية كبديل
            type_titles = {
                'exciting': [
                    "لحظة لا تُصدق! 🔥",
                    "هذا مجنون! 😱",
                    "لن تصدق ما حدث! ⚡",
                    "لحظة أسطورية! 🚀"
                ],
                'funny': [
                    "ضحك حتى البكاء! 😂",
                    "أضحك مقطع اليوم! 🤣",
                    "كوميديا خالصة! 😆",
                    "مقطع مضحك جداً! 😄"
                ],
                'shocking': [
                    "صدمة! لم أتوقع هذا! 😲",
                    "مفاجأة غير متوقعة! 🤯",
                    "لحظة صادمة! ⚡",
                    "لن تصدق ما حدث! 😱"
                ]
            }

            titles = type_titles.get(moment['type'], ["لحظة مميزة! ✨"])

            # اختيار عنوان عشوائي
            import random
            return random.choice(titles)

        except Exception as e:
            logger.error(f"خطأ في توليد العنوان: {e}")
            return "لحظة مميزة! ✨"

    def _generate_clip_hashtags(self, moment: Dict[str, Any]) -> List[str]:
        """توليد هاشتاغات للمقطع"""
        try:
            # محاولة استخدام Gemini لتوليد هاشتاغات ذكية
            if self.gemini_client.is_available():
                content = moment.get('text', moment.get('description', ''))
                moment_type = moment.get('type', 'exciting')

                smart_hashtags = self.gemini_client.generate_hashtags(content, moment_type)
                if smart_hashtags:
                    logger.info("تم توليد هاشتاغات ذكية باستخدام Gemini")
                    return smart_hashtags[:15]  # حد أقصى 15 هاشتاغ

            # الهاشتاغات الافتراضية كبديل
            base_hashtags = ["#shorts", "#viral", "#trending", "#gaming"]

            type_hashtags = {
                'exciting': ["#exciting", "#amazing", "#incredible", "#مثير", "#رهيب"],
                'funny': ["#funny", "#comedy", "#laugh", "#مضحك", "#كوميدي"],
                'shocking': ["#shocking", "#unexpected", "#surprise", "#صادم", "#مفاجأة"]
            }

            moment_hashtags = type_hashtags.get(moment['type'], [])

            # إضافة هاشتاغات خاصة بالستريمر (يمكن تخصيصها)
            streamer_hashtags = ["#ishowspeed", "#speed", "#streamer", "#live"]

            all_hashtags = base_hashtags + moment_hashtags + streamer_hashtags

            return all_hashtags[:15]  # حد أقصى 15 هاشتاغ

        except Exception as e:
            logger.error(f"خطأ في توليد الهاشتاغات: {e}")
            return ["#shorts", "#viral", "#trending"]
