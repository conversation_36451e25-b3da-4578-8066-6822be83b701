#!/usr/bin/env python3
"""
اختبار نظام كشف ردات الفعل المتقدم
Test Advanced Reaction Detection System
"""

import sys
import os
import logging
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_reaction_detection.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def test_reaction_detector_basic():
    """اختبار أساسي لكاشف ردات الفعل"""
    try:
        print("🧪 اختبار نظام كشف ردات الفعل المتقدم")
        print("=" * 50)
        
        # محاولة استيراد النظام
        try:
            from ai.reaction_detector import ReactionDetector, ReactionMoment
            print("✅ تم استيراد كاشف ردات الفعل بنجاح")
        except ImportError as e:
            print(f"❌ فشل في استيراد كاشف ردات الفعل: {e}")
            print("💡 تأكد من تثبيت MediaPipe: pip install mediapipe==0.10.7")
            return False
        
        # إنشاء كاشف ردات الفعل
        try:
            detector = ReactionDetector()
            print("✅ تم إنشاء كاشف ردات الفعل بنجاح")
        except Exception as e:
            print(f"❌ فشل في إنشاء كاشف ردات الفعل: {e}")
            return False
        
        # اختبار الدوال الأساسية
        print("\n🔧 اختبار الدوال الأساسية...")
        
        # اختبار حساب انفتاح العين
        import numpy as np
        test_eye = np.array([[0, 0], [5, 2], [10, 0], [15, 2], [10, 5], [5, 3]])
        eye_openness = detector._calculate_eye_openness(test_eye)
        print(f"✅ اختبار انفتاح العين: {eye_openness:.3f}")
        
        # اختبار حساب انفتاح الفم
        test_mouth = np.array([[0, 0], [5, 3], [10, 0], [15, 3], [10, 8], [5, 5]])
        mouth_openness = detector._calculate_mouth_openness(test_mouth)
        print(f"✅ اختبار انفتاح الفم: {mouth_openness:.3f}")
        
        # اختبار توسيع اللقطة
        from ai.reaction_detector import ReactionMoment
        test_reaction = ReactionMoment(
            start_time=10.0,
            peak_time=12.0,
            end_time=15.0,
            intensity=0.8,
            emotion_type='surprise',
            confidence=0.9,
            face_changes={'surprise': 0.8},
            body_movement=0.6,
            audio_changes={}
        )
        
        new_start, new_end = detector.extend_highlight_with_reaction(5.0, 8.0, [test_reaction])
        print(f"✅ اختبار توسيع اللقطة: {new_start:.1f}s - {new_end:.1f}s")
        
        print(f"\n✅ جميع الاختبارات الأساسية نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار الأساسي: {e}")
        logger.error(f"خطأ في اختبار كاشف ردات الفعل: {e}")
        return False

def test_with_video_file():
    """اختبار مع ملف فيديو إذا كان متوفراً"""
    try:
        print(f"\n🎬 اختبار مع ملف فيديو")
        print("=" * 30)
        
        # البحث عن ملفات فيديو
        downloads_dir = Path("downloads")
        if not downloads_dir.exists():
            print("❌ مجلد downloads غير موجود")
            return False
        
        video_files = list(downloads_dir.glob("*.mp4"))
        if not video_files:
            print("❌ لا توجد ملفات فيديو في مجلد downloads")
            return False
        
        video_file = video_files[0]
        print(f"📁 استخدام الملف: {video_file.name}")
        
        # استيراد النظام
        from ai.reaction_detector import ReactionDetector
        detector = ReactionDetector()
        
        # تحديد لحظات محفزة للاختبار
        trigger_moments = [(30.0, 35.0), (60.0, 65.0)]  # لحظات افتراضية
        print(f"🎯 البحث عن ردات فعل بعد اللحظات: {trigger_moments}")
        
        # كشف ردات الفعل
        reactions = detector.detect_reactions_in_video(str(video_file), trigger_moments)
        
        if reactions:
            print(f"✅ تم العثور على {len(reactions)} ردة فعل:")
            for i, reaction in enumerate(reactions, 1):
                print(f"   {i}. {reaction.start_time:.1f}s-{reaction.end_time:.1f}s")
                print(f"      النوع: {reaction.emotion_type}")
                print(f"      الشدة: {reaction.intensity:.2f}")
                print(f"      الثقة: {reaction.confidence:.2f}")

                # عرض تفاصيل ردة الفعل
                if 'crowd_' in reaction.emotion_type:
                    print(f"      🎭 ردة فعل جماعية!")
                elif 'gesture_' in reaction.emotion_type:
                    gesture_name = reaction.emotion_type.replace('gesture_', '')
                    gesture_names = {
                        'applause': 'تصفيق',
                        'arms_raised': 'رفع الذراعين',
                        'jumping': 'قفز',
                        'pointing': 'إشارة'
                    }
                    print(f"      🤲 إيماءة: {gesture_names.get(gesture_name, gesture_name)}")
                elif 'phone_recording' in reaction.emotion_type:
                    print(f"      📱 تسجيل بالهواتف!")
                else:
                    print(f"      😊 ردة فعل فردية")
        else:
            print("❌ لم يتم العثور على ردات فعل واضحة")
            print("💡 هذا طبيعي إذا لم تكن هناك وجوه واضحة أو ردات فعل قوية في الفيديو")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الفيديو: {e}")
        logger.error(f"خطأ في اختبار الفيديو: {e}")
        return False

def test_integration_with_advanced_detector():
    """اختبار التكامل مع النظام المتقدم"""
    try:
        print(f"\n🔗 اختبار التكامل مع النظام المتقدم")
        print("=" * 40)
        
        # استيراد النظام المتقدم
        try:
            from ai.advanced_highlight_detector import AdvancedHighlightDetector
            print("✅ تم استيراد النظام المتقدم بنجاح")
        except ImportError as e:
            print(f"❌ فشل في استيراد النظام المتقدم: {e}")
            return False
        
        # إنشاء النظام
        try:
            detector = AdvancedHighlightDetector()
            print("✅ تم إنشاء النظام المتقدم مع كاشف ردات الفعل")
        except Exception as e:
            print(f"❌ فشل في إنشاء النظام المتقدم: {e}")
            return False
        
        # التحقق من وجود كاشف ردات الفعل
        if hasattr(detector, 'reaction_detector'):
            print("✅ كاشف ردات الفعل متكامل بنجاح")
        else:
            print("❌ كاشف ردات الفعل غير متكامل")
            return False
        
        print("✅ التكامل مكتمل وجاهز للاستخدام")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    try:
        print("🚀 بدء اختبار نظام كشف ردات الفعل المتقدم")
        print("=" * 60)
        
        # الاختبار الأساسي
        basic_test = test_reaction_detector_basic()
        
        # اختبار الفيديو (اختياري)
        video_test = test_with_video_file() if basic_test else False
        
        # اختبار التكامل
        integration_test = test_integration_with_advanced_detector() if basic_test else False
        
        print(f"\n📋 ملخص النتائج:")
        print(f"   الاختبار الأساسي: {'✅ نجح' if basic_test else '❌ فشل'}")
        print(f"   اختبار الفيديو: {'✅ نجح' if video_test else '❌ فشل أو لم يتم'}")
        print(f"   اختبار التكامل: {'✅ نجح' if integration_test else '❌ فشل'}")
        
        if basic_test and integration_test:
            print(f"\n🎉 نظام كشف ردات الفعل جاهز للاستخدام!")
            print(f"💡 الآن سيتم:")
            print(f"   • كشف ردات فعل جميع الأشخاص في الفيديو")
            print(f"   • تحليل ردات الفعل الفردية والجماعية")
            print(f"   • كشف الإيماءات والحركات الخاصة")
            print(f"   • توسيع اللقطات لتشمل ردات الفعل الكاملة")
            print(f"   • تحسين جودة المقاطع المستخرجة")

            print(f"\n🎯 أمثلة ردات الفعل المكتشفة:")
            print(f"   🎪 شخص يقوم بشقلبة:")
            print(f"      1. النظام يكتشف الشقلبة (اللحظة المحفزة)")
            print(f"      2. يبحث عن ردات فعل الجمهور")
            print(f"      3. يكتشف: تصفيق جماعي + وجوه مندهشة + تسجيل بالهواتف")
            print(f"      4. ينتج مقطع كامل: الشقلبة + ردات الفعل")

            print(f"\n   ⚽ IShowSpeed يرى صورة رونالدو:")
            print(f"      1. النظام يكتشف ظهور الصورة")
            print(f"      2. يكتشف صدمة IShowSpeed: عيون واسعة + فم مفتوح")
            print(f"      3. يكتشف حركات اليدين والجسم")
            print(f"      4. ينتج مقطع كامل مع ردة الفعل")

            print(f"\n   🎭 ردة فعل جماعية:")
            print(f"      1. حدث مفاجئ في الفيديو")
            print(f"      2. النظام يكتشف عدة وجوه مندهشة")
            print(f"      3. يكتشف تزامن في المشاعر (مفاجأة جماعية)")
            print(f"      4. يكتشف إيماءات: رفع الذراعين + إشارات")
            print(f"      5. ينتج مقطع يشمل الحدث + ردات الفعل الجماعية")
        else:
            print(f"\n⚠️ هناك مشاكل في النظام تحتاج إلى إصلاح")
            if not basic_test:
                print(f"💡 تأكد من تثبيت MediaPipe: pip install mediapipe==0.10.7")
        
        return 0 if (basic_test and integration_test) else 1
        
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {e}")
        logger.error(f"خطأ عام في الاختبار: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
