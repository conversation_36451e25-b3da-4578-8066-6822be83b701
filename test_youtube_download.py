#!/usr/bin/env python3
"""
اختبار تحميل فيديو YouTube المحدد
Test downloading the specific YouTube video
"""

import sys
import os
from pathlib import Path

# إضافة مجلد src إلى مسار Python
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_youtube_url():
    """اختبار الرابط المحدد"""
    
    # الرابط المطلوب اختباره
    test_url = "https://www.youtube.com/live/2GLBi1yhN8o?si=WrFbzhKsXdpJaHp_"
    
    print("🔗 اختبار رابط YouTube المحدد")
    print("=" * 50)
    print(f"الرابط: {test_url}")
    print()
    
    try:
        from utils.youtube_downloader import YouTubeDownloader
        
        # إنشاء محمل YouTube
        downloader = YouTubeDownloader()
        print("✅ تم إنشاء محمل YouTube")
        
        # التحقق من صحة الرابط
        if downloader.is_youtube_url(test_url):
            print("✅ الرابط صحيح ومن YouTube")
        else:
            print("❌ الرابط غير صحيح")
            return False
        
        # الحصول على معلومات الفيديو
        print("\n📋 جلب معلومات الفيديو...")
        video_info = downloader.get_video_info(test_url)
        
        if not video_info:
            print("❌ لا يمكن الحصول على معلومات الفيديو")
            return False
        
        # عرض معلومات الفيديو
        print("✅ تم جلب معلومات الفيديو بنجاح!")
        print("\n📊 معلومات الفيديو:")
        print("-" * 30)
        
        info_items = [
            ("العنوان", video_info.get('title', 'غير معروف')),
            ("القناة", video_info.get('uploader', 'غير معروف')),
            ("المدة", format_duration(video_info.get('duration', 0))),
            ("المشاهدات", f"{video_info.get('view_count', 0):,}"),
            ("الإعجابات", f"{video_info.get('like_count', 0):,}"),
            ("الدقة", video_info.get('resolution', 'غير معروف')),
            ("حالة البث", 'مباشر' if video_info.get('is_live') else 'مسجل'),
            ("تاريخ الرفع", video_info.get('upload_date', 'غير معروف')),
            ("عدد التنسيقات", video_info.get('formats', 0)),
        ]
        
        for label, value in info_items:
            print(f"{label}: {value}")
        
        # فحص حالة البث
        if video_info.get('is_live'):
            print("\n🔴 هذا بث مباشر جاري!")
            print("⚠️  سيتم تحميل الجزء المتاح من البث")
        elif video_info.get('was_live'):
            print("\n📹 هذا بث مباشر سابق (مسجل)")
            print("✅ يمكن تحميله بالكامل")
        else:
            print("\n🎬 هذا فيديو عادي")
            print("✅ يمكن تحميله بالكامل")
        
        # فحص التنسيقات المتاحة
        print("\n🎯 فحص التنسيقات المتاحة...")
        formats = downloader.get_available_formats(test_url)
        
        if formats:
            print(f"✅ متوفر {len(formats)} تنسيق")
            
            # عرض أفضل 5 تنسيقات
            print("\n📋 أفضل التنسيقات:")
            video_formats = [f for f in formats if f.get('vcodec') != 'none' and f.get('height')]
            video_formats.sort(key=lambda x: x.get('height', 0), reverse=True)
            
            for i, fmt in enumerate(video_formats[:5]):
                resolution = f"{fmt.get('width', 0)}x{fmt.get('height', 0)}" if fmt.get('width') else 'غير معروف'
                filesize = fmt.get('filesize', 0)
                size_mb = f"{filesize / (1024*1024):.1f}MB" if filesize else "غير معروف"
                
                print(f"  {i+1}. {resolution} - {fmt.get('ext', 'mp4')} - {size_mb}")
        else:
            print("⚠️  لا يمكن الحصول على قائمة التنسيقات")
        
        # اختبار تحميل تجريبي (صوت فقط للاختبار السريع)
        print("\n🎵 اختبار تحميل الصوت فقط (للاختبار السريع)...")
        
        def progress_callback(progress_info):
            percentage = progress_info.get('percentage', 0)
            status = progress_info.get('status', 'downloading')
            
            if status == 'downloading':
                print(f"\r📥 التحميل: {percentage:.1f}%", end='', flush=True)
            elif status == 'finished':
                print(f"\r✅ تم التحميل بنجاح!     ")
        
        audio_file = downloader.download_audio_only(test_url, progress_callback)
        
        if audio_file:
            file_size = Path(audio_file).stat().st_size / (1024 * 1024)
            print(f"✅ تم تحميل الصوت: {audio_file}")
            print(f"📊 حجم الملف: {file_size:.1f}MB")
            
            # حذف الملف التجريبي
            try:
                Path(audio_file).unlink()
                print("🗑️ تم حذف الملف التجريبي")
            except:
                pass
            
            return True
        else:
            print("❌ فشل في تحميل الصوت")
            return False
            
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        print("قم بتثبيت المكتبات المطلوبة: pip install yt-dlp")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def format_duration(seconds: int) -> str:
    """تنسيق المدة بالثواني إلى نص مقروء"""
    if seconds <= 0:
        return "غير معروف"
    
    hours = seconds // 3600
    minutes = (seconds % 3600) // 60
    seconds = seconds % 60
    
    if hours > 0:
        return f"{hours}:{minutes:02d}:{seconds:02d}"
    else:
        return f"{minutes}:{seconds:02d}"

def test_livestream_analyzer_with_url():
    """اختبار محلل البثوث المباشرة مع الرابط"""
    
    print("\n🎬 اختبار محلل البثوث المباشرة مع الرابط")
    print("=" * 50)
    
    try:
        from ai.livestream_analyzer import LivestreamAnalyzer
        
        analyzer = LivestreamAnalyzer()
        print("✅ تم إنشاء محلل البثوث المباشرة")
        
        # اختبار تصنيف اللحظات مع نصوص متعلقة بـ IShowSpeed
        test_cases = [
            ("YOOO THAT'S INSANE BRO NO WAY", "exciting"),
            ("CHAT CHAT LOOK AT THIS", "exciting"),
            ("HAHAHA THAT'S SO FUNNY", "funny"),
            ("WHAT THE HELL JUST HAPPENED", "shocking"),
            ("OH MY GOD OH MY GOD", "exciting"),
            ("SPEED SPEED SPEED", "exciting"),
            ("THIS IS CRAZY GUYS", "exciting"),
        ]
        
        print("\n🔍 اختبار تصنيف اللحظات (نمط IShowSpeed):")
        for text, expected in test_cases:
            moment_type, confidence = analyzer._classify_moment_type(text)
            status = "✅" if confidence > 0.5 else "⚠️"
            print(f"{status} '{text}' -> {moment_type} ({confidence:.2f})")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المحلل: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🧪 اختبار شامل للرابط المحدد")
    print("=" * 60)
    
    # اختبار الرابط
    url_test = test_youtube_url()
    
    # اختبار المحلل
    analyzer_test = test_livestream_analyzer_with_url()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("🎯 النتيجة النهائية")
    print("=" * 60)
    
    if url_test and analyzer_test:
        print("🎉 جميع الاختبارات نجحت!")
        print("\nالرابط جاهز للاستخدام في محلل البثوث المباشرة:")
        print("1. افتح محلل البثوث: python run_livestream_analyzer.py")
        print("2. اختر 'رابط YouTube'")
        print("3. الصق الرابط: https://www.youtube.com/live/2GLBi1yhN8o?si=WrFbzhKsXdpJaHp_")
        print("4. اضغط 'تحميل' وابدأ التحليل")
        return 0
    else:
        print("⚠️  بعض الاختبارات فشلت")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
