"""
نوافذ الحوار للتفاعل مع المستخدم
Dialog windows for user interaction
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, Tuple, Any
import logging

from config.settings import AppSettings, UISettings
from utils.validators import VideoValidator, TextValidator, ParameterValidator

logger = logging.getLogger(__name__)

class BaseDialog:
    """فئة أساسية لنوافذ الحوار"""
    
    def __init__(self, parent: tk.Tk, title: str, width: int = 400, height: int = 300):
        self.parent = parent
        self.result: Optional[Any] = None
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry(f"{width}x{height}")
        self.dialog.resizable(False, False)
        
        # جعل النافذة modal
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.center_dialog()
        
        # إنشاء المحتوى
        self.create_widgets()
        
        # ربط الأحداث
        self.bind_events()
        
        # انتظار إغلاق النافذة
        self.dialog.wait_window()
    
    def center_dialog(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_reqwidth() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_reqheight() // 2)
        self.dialog.geometry(f"+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر واجهة المستخدم - يجب تنفيذها في الفئات المشتقة"""
        pass
    
    def bind_events(self):
        """ربط الأحداث"""
        self.dialog.bind('<Return>', lambda e: self.ok_clicked())
        self.dialog.bind('<Escape>', lambda e: self.cancel_clicked())
    
    def ok_clicked(self):
        """عند النقر على موافق"""
        if self.validate_input():
            self.result = self.get_result()
            self.dialog.destroy()
    
    def cancel_clicked(self):
        """عند النقر على إلغاء"""
        self.result = None
        self.dialog.destroy()
    
    def validate_input(self) -> bool:
        """التحقق من صحة المدخلات - يجب تنفيذها في الفئات المشتقة"""
        return True
    
    def get_result(self) -> Any:
        """الحصول على النتيجة - يجب تنفيذها في الفئات المشتقة"""
        return None

class TrimDialog(BaseDialog):
    """نافذة حوار قص الفيديو"""
    
    def __init__(self, parent: tk.Tk, video_duration: float):
        self.video_duration = video_duration
        self.validator = VideoValidator()
        super().__init__(parent, "قص الفيديو", 450, 200)
    
    def create_widgets(self):
        """إنشاء عناصر واجهة المستخدم"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # معلومات الفيديو
        info_label = ttk.Label(main_frame, text=f"مدة الفيديو: {self.video_duration:.1f} ثانية")
        info_label.pack(pady=(0, 20))
        
        # وقت البداية
        start_frame = ttk.Frame(main_frame)
        start_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(start_frame, text="وقت البداية (ثانية):").pack(side=tk.LEFT)
        self.start_var = tk.DoubleVar(value=0.0)
        self.start_entry = ttk.Entry(start_frame, textvariable=self.start_var, width=10)
        self.start_entry.pack(side=tk.RIGHT)
        
        # وقت النهاية
        end_frame = ttk.Frame(main_frame)
        end_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(end_frame, text="وقت النهاية (ثانية):").pack(side=tk.LEFT)
        self.end_var = tk.DoubleVar(value=self.video_duration)
        self.end_entry = ttk.Entry(end_frame, textvariable=self.end_var, width=10)
        self.end_entry.pack(side=tk.RIGHT)
        
        # أزرار
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Button(button_frame, text="إلغاء", command=self.cancel_clicked).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="موافق", command=self.ok_clicked).pack(side=tk.RIGHT)
    
    def validate_input(self) -> bool:
        """التحقق من صحة المدخلات"""
        try:
            start_time = self.start_var.get()
            end_time = self.end_var.get()
            
            is_valid, message = self.validator.validate_time_range(
                start_time, end_time, self.video_duration
            )
            
            if not is_valid:
                messagebox.showerror("خطأ", message)
                return False
            
            return True
            
        except tk.TclError:
            messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة")
            return False
    
    def get_result(self) -> Tuple[float, float]:
        """الحصول على النتيجة"""
        return (self.start_var.get(), self.end_var.get())

class TextDialog(BaseDialog):
    """نافذة حوار إضافة النص"""
    
    def __init__(self, parent: tk.Tk):
        self.validator = TextValidator()
        self.param_validator = ParameterValidator()
        super().__init__(parent, "إضافة نص", 500, 350)
    
    def create_widgets(self):
        """إنشاء عناصر واجهة المستخدم"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # النص
        text_frame = ttk.LabelFrame(main_frame, text="النص", padding="10")
        text_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.text_var = tk.StringVar()
        text_entry = ttk.Entry(text_frame, textvariable=self.text_var, width=50)
        text_entry.pack(fill=tk.X)
        
        # الموقع
        position_frame = ttk.LabelFrame(main_frame, text="الموقع", padding="10")
        position_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.position_var = tk.StringVar(value="center")
        positions = [
            ("أعلى يسار", "top-left"),
            ("أعلى وسط", "top-center"),
            ("أعلى يمين", "top-right"),
            ("وسط يسار", "center-left"),
            ("وسط", "center"),
            ("وسط يمين", "center-right"),
            ("أسفل يسار", "bottom-left"),
            ("أسفل وسط", "bottom-center"),
            ("أسفل يمين", "bottom-right")
        ]
        
        position_combo = ttk.Combobox(position_frame, textvariable=self.position_var,
                                     values=[pos[1] for pos in positions], state="readonly")
        position_combo.pack(fill=tk.X)
        
        # إعدادات التنسيق
        format_frame = ttk.LabelFrame(main_frame, text="التنسيق", padding="10")
        format_frame.pack(fill=tk.X, pady=(0, 10))
        
        # حجم الخط
        font_size_frame = ttk.Frame(format_frame)
        font_size_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(font_size_frame, text="حجم الخط:").pack(side=tk.LEFT)
        self.font_size_var = tk.IntVar(value=24)
        font_size_spin = ttk.Spinbox(font_size_frame, from_=8, to=200, 
                                    textvariable=self.font_size_var, width=10)
        font_size_spin.pack(side=tk.RIGHT)
        
        # اللون
        color_frame = ttk.Frame(format_frame)
        color_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(color_frame, text="اللون:").pack(side=tk.LEFT)
        self.color_var = tk.StringVar(value="white")
        colors = ["white", "black", "red", "green", "blue", "yellow", "cyan", "magenta"]
        color_combo = ttk.Combobox(color_frame, textvariable=self.color_var,
                                  values=colors, width=10)
        color_combo.pack(side=tk.RIGHT)
        
        # أزرار
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Button(button_frame, text="إلغاء", command=self.cancel_clicked).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="موافق", command=self.ok_clicked).pack(side=tk.RIGHT)
    
    def validate_input(self) -> bool:
        """التحقق من صحة المدخلات"""
        # التحقق من النص
        text = self.text_var.get()
        is_valid, message = self.validator.validate_text_overlay(text)
        if not is_valid:
            messagebox.showerror("خطأ", message)
            return False
        
        # التحقق من حجم الخط
        font_size = self.font_size_var.get()
        is_valid, message = self.param_validator.validate_font_size(font_size)
        if not is_valid:
            messagebox.showerror("خطأ", message)
            return False
        
        # التحقق من اللون
        color = self.color_var.get()
        is_valid, message = self.param_validator.validate_color(color)
        if not is_valid:
            messagebox.showerror("خطأ", message)
            return False
        
        return True
    
    def get_result(self) -> Tuple[str, str, int, str]:
        """الحصول على النتيجة"""
        return (
            self.validator.sanitize_text(self.text_var.get()),
            self.position_var.get(),
            self.font_size_var.get(),
            self.color_var.get()
        )

class ConvertDialog(BaseDialog):
    """نافذة حوار تحويل الفيديو"""
    
    def __init__(self, parent: tk.Tk, video_info: dict):
        self.video_info = video_info
        self.validator = ParameterValidator()
        super().__init__(parent, "تحويل الفيديو", 450, 300)
    
    def create_widgets(self):
        """إنشاء عناصر واجهة المستخدم"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # معلومات الفيديو الحالي
        current_info = ttk.LabelFrame(main_frame, text="الدقة الحالية", padding="10")
        current_info.pack(fill=tk.X, pady=(0, 10))
        
        current_text = f"{self.video_info['video']['width']} x {self.video_info['video']['height']}"
        ttk.Label(current_info, text=current_text).pack()
        
        # الدقة الجديدة
        resolution_frame = ttk.LabelFrame(main_frame, text="الدقة الجديدة", padding="10")
        resolution_frame.pack(fill=tk.X, pady=(0, 10))
        
        # العرض
        width_frame = ttk.Frame(resolution_frame)
        width_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(width_frame, text="العرض:").pack(side=tk.LEFT)
        self.width_var = tk.IntVar(value=self.video_info['video']['width'])
        width_entry = ttk.Entry(width_frame, textvariable=self.width_var, width=10)
        width_entry.pack(side=tk.RIGHT)
        
        # الارتفاع
        height_frame = ttk.Frame(resolution_frame)
        height_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(height_frame, text="الارتفاع:").pack(side=tk.LEFT)
        self.height_var = tk.IntVar(value=self.video_info['video']['height'])
        height_entry = ttk.Entry(height_frame, textvariable=self.height_var, width=10)
        height_entry.pack(side=tk.RIGHT)
        
        # إعدادات مسبقة
        presets_frame = ttk.LabelFrame(main_frame, text="إعدادات مسبقة", padding="10")
        presets_frame.pack(fill=tk.X, pady=(0, 10))
        
        preset_buttons = [
            ("HD (1280x720)", 1280, 720),
            ("Full HD (1920x1080)", 1920, 1080),
            ("عمودي (1080x1920)", 1080, 1920),
            ("مربع (1080x1080)", 1080, 1080)
        ]
        
        for i, (text, w, h) in enumerate(preset_buttons):
            if i % 2 == 0:
                row_frame = ttk.Frame(presets_frame)
                row_frame.pack(fill=tk.X, pady=2)
            
            ttk.Button(row_frame, text=text, 
                      command=lambda w=w, h=h: self.set_resolution(w, h)).pack(side=tk.LEFT, padx=2)
        
        # الجودة
        quality_frame = ttk.LabelFrame(main_frame, text="الجودة", padding="10")
        quality_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.quality_var = tk.StringVar(value="medium")
        qualities = list(AppSettings.VIDEO_QUALITY_PRESETS.keys())
        quality_combo = ttk.Combobox(quality_frame, textvariable=self.quality_var,
                                    values=qualities, state="readonly")
        quality_combo.pack(fill=tk.X)
        
        # أزرار
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Button(button_frame, text="إلغاء", command=self.cancel_clicked).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="موافق", command=self.ok_clicked).pack(side=tk.RIGHT)
    
    def set_resolution(self, width: int, height: int):
        """تعيين الدقة"""
        self.width_var.set(width)
        self.height_var.set(height)
    
    def validate_input(self) -> bool:
        """التحقق من صحة المدخلات"""
        try:
            width = self.width_var.get()
            height = self.height_var.get()
            quality = self.quality_var.get()
            
            # التحقق من الدقة
            is_valid, message = self.validator.validate_resolution(width, height)
            if not is_valid:
                messagebox.showerror("خطأ", message)
                return False
            
            # التحقق من الجودة
            is_valid, message = self.validator.validate_quality_setting(quality)
            if not is_valid:
                messagebox.showerror("خطأ", message)
                return False
            
            return True
            
        except tk.TclError:
            messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة للدقة")
            return False
    
    def get_result(self) -> Tuple[int, int, str]:
        """الحصول على النتيجة"""
        return (self.width_var.get(), self.height_var.get(), self.quality_var.get())
