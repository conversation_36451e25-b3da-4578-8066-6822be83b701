"""
تشغيل جميع الاختبارات
Run all tests script
"""

import unittest
import sys
import os
from pathlib import Path
import time
import argparse

# إضافة مجلد src إلى مسار Python
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def discover_and_run_tests(test_dir="tests", pattern="test_*.py", verbosity=2):
    """اكتشاف وتشغيل جميع الاختبارات"""
    
    # إنشاء test suite
    loader = unittest.TestLoader()
    suite = loader.discover(test_dir, pattern=pattern)
    
    # إنشاء test runner
    runner = unittest.TextTestRunner(
        verbosity=verbosity,
        stream=sys.stdout,
        buffer=True,
        failfast=False
    )
    
    print("=" * 70)
    print("تشغيل اختبارات Video Editor Pro")
    print("Running Video Editor Pro Tests")
    print("=" * 70)
    
    start_time = time.time()
    
    # تشغيل الاختبارات
    result = runner.run(suite)
    
    end_time = time.time()
    duration = end_time - start_time
    
    # طباعة النتائج
    print("\n" + "=" * 70)
    print("ملخص النتائج - Test Results Summary")
    print("=" * 70)
    
    print(f"إجمالي الاختبارات - Total Tests: {result.testsRun}")
    print(f"نجح - Passed: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"فشل - Failed: {len(result.failures)}")
    print(f"أخطاء - Errors: {len(result.errors)}")
    print(f"تم تخطيها - Skipped: {len(result.skipped)}")
    print(f"الوقت المستغرق - Duration: {duration:.2f} seconds")
    
    # طباعة تفاصيل الفشل
    if result.failures:
        print("\n" + "-" * 50)
        print("الاختبارات الفاشلة - Failed Tests:")
        print("-" * 50)
        for test, traceback in result.failures:
            print(f"❌ {test}")
            print(f"   {traceback.split('AssertionError:')[-1].strip()}")
    
    # طباعة تفاصيل الأخطاء
    if result.errors:
        print("\n" + "-" * 50)
        print("أخطاء الاختبارات - Test Errors:")
        print("-" * 50)
        for test, traceback in result.errors:
            print(f"💥 {test}")
            print(f"   {traceback.split('Exception:')[-1].strip()}")
    
    # طباعة الاختبارات المتخطاة
    if result.skipped:
        print("\n" + "-" * 50)
        print("الاختبارات المتخطاة - Skipped Tests:")
        print("-" * 50)
        for test, reason in result.skipped:
            print(f"⏭️  {test}")
            print(f"   السبب - Reason: {reason}")
    
    print("\n" + "=" * 70)
    
    # تحديد حالة الخروج
    if result.wasSuccessful():
        print("✅ جميع الاختبارات نجحت! - All tests passed!")
        return 0
    else:
        print("❌ بعض الاختبارات فشلت - Some tests failed")
        return 1

def run_specific_test_module(module_name, verbosity=2):
    """تشغيل وحدة اختبار محددة"""
    
    print(f"تشغيل اختبارات الوحدة: {module_name}")
    print(f"Running tests for module: {module_name}")
    print("-" * 50)
    
    try:
        # استيراد الوحدة
        module = __import__(module_name)
        
        # إنشاء test suite
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromModule(module)
        
        # تشغيل الاختبارات
        runner = unittest.TextTestRunner(verbosity=verbosity)
        result = runner.run(suite)
        
        return 0 if result.wasSuccessful() else 1
        
    except ImportError as e:
        print(f"خطأ في استيراد الوحدة: {e}")
        print(f"Module import error: {e}")
        return 1

def run_performance_tests():
    """تشغيل اختبارات الأداء"""
    
    print("تشغيل اختبارات الأداء")
    print("Running Performance Tests")
    print("=" * 50)
    
    # تعيين متغير البيئة لتفعيل اختبارات الأداء
    os.environ['PERFORMANCE_TESTS'] = '1'
    
    try:
        # تشغيل اختبارات الأداء
        return discover_and_run_tests(pattern="test_*performance*.py")
    finally:
        # إزالة متغير البيئة
        if 'PERFORMANCE_TESTS' in os.environ:
            del os.environ['PERFORMANCE_TESTS']

def run_integration_tests():
    """تشغيل اختبارات التكامل"""
    
    print("تشغيل اختبارات التكامل")
    print("Running Integration Tests")
    print("=" * 50)
    
    # التحقق من توفر ملف فيديو للاختبار
    test_video = os.getenv('TEST_VIDEO_PATH')
    if not test_video or not os.path.exists(test_video):
        print("⚠️  تحذير: ملف فيديو الاختبار غير متوفر")
        print("⚠️  Warning: Test video file not available")
        print("   يرجى تعيين متغير البيئة TEST_VIDEO_PATH")
        print("   Please set TEST_VIDEO_PATH environment variable")
        return 1
    
    # تعيين متغير البيئة لتفعيل اختبارات التكامل
    os.environ['INTEGRATION_TESTS'] = '1'
    
    try:
        # تشغيل اختبارات التكامل
        return discover_and_run_tests(pattern="test_*integration*.py")
    finally:
        # إزالة متغير البيئة
        if 'INTEGRATION_TESTS' in os.environ:
            del os.environ['INTEGRATION_TESTS']

def check_test_environment():
    """فحص بيئة الاختبار"""
    
    print("فحص بيئة الاختبار")
    print("Checking Test Environment")
    print("-" * 30)
    
    issues = []
    
    # فحص Python
    python_version = sys.version_info
    if python_version < (3, 8):
        issues.append(f"إصدار Python قديم: {python_version}")
    else:
        print(f"✅ Python: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # فحص المكتبات المطلوبة
    required_modules = [
        'tkinter', 'unittest', 'pathlib', 'json', 'sqlite3'
    ]
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}: متوفر")
        except ImportError:
            issues.append(f"المكتبة {module} غير متوفرة")
            print(f"❌ {module}: غير متوفر")
    
    # فحص المكتبات الاختيارية
    optional_modules = [
        ('cv2', 'OpenCV'),
        ('numpy', 'NumPy'),
        ('PIL', 'Pillow'),
        ('requests', 'Requests')
    ]
    
    for module, name in optional_modules:
        try:
            __import__(module)
            print(f"✅ {name}: متوفر")
        except ImportError:
            print(f"⚠️  {name}: غير متوفر (اختياري)")
    
    # فحص FFmpeg
    try:
        import subprocess
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ FFmpeg: متوفر")
        else:
            issues.append("FFmpeg غير متوفر أو لا يعمل بشكل صحيح")
            print("❌ FFmpeg: غير متوفر")
    except FileNotFoundError:
        issues.append("FFmpeg غير مثبت")
        print("❌ FFmpeg: غير مثبت")
    
    print("-" * 30)
    
    if issues:
        print("⚠️  مشاكل في البيئة:")
        for issue in issues:
            print(f"   - {issue}")
        return False
    else:
        print("✅ بيئة الاختبار جاهزة!")
        return True

def main():
    """الدالة الرئيسية"""
    
    parser = argparse.ArgumentParser(description='تشغيل اختبارات Video Editor Pro')
    parser.add_argument('--module', '-m', help='تشغيل وحدة اختبار محددة')
    parser.add_argument('--performance', '-p', action='store_true', 
                       help='تشغيل اختبارات الأداء')
    parser.add_argument('--integration', '-i', action='store_true',
                       help='تشغيل اختبارات التكامل')
    parser.add_argument('--check-env', '-c', action='store_true',
                       help='فحص بيئة الاختبار')
    parser.add_argument('--verbose', '-v', type=int, default=2,
                       help='مستوى التفصيل (0-2)')
    
    args = parser.parse_args()
    
    # فحص البيئة إذا طُلب
    if args.check_env:
        env_ok = check_test_environment()
        return 0 if env_ok else 1
    
    # تشغيل اختبارات الأداء
    if args.performance:
        return run_performance_tests()
    
    # تشغيل اختبارات التكامل
    if args.integration:
        return run_integration_tests()
    
    # تشغيل وحدة محددة
    if args.module:
        return run_specific_test_module(args.module, args.verbose)
    
    # تشغيل جميع الاختبارات الأساسية
    return discover_and_run_tests(verbosity=args.verbose)

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
