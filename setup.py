"""
إعداد التثبيت لأداة تحرير الفيديو
Setup configuration for Video Editor Pro
"""

from setuptools import setup, find_packages
from pathlib import Path

# قراءة ملف README
this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text(encoding='utf-8')

# قراءة المتطلبات
requirements = []
with open('requirements.txt', 'r', encoding='utf-8') as f:
    requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]

setup(
    name="video-editor-pro",
    version="1.0.0",
    author="Video Editor Pro Team",
    author_email="<EMAIL>",
    description="أداة تحرير فيديو احترافية ومجانية بالكامل",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/video-editor-pro",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Multimedia :: Video",
        "Topic :: Multimedia :: Video :: Non-Linear Editor",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0",
            "pytest-asyncio>=0.21.0",
            "black>=23.0",
            "flake8>=6.0",
            "mypy>=1.0",
        ],
        "gpu": [
            "torch[cuda]",
            "tensorflow-gpu",
        ],
    },
    entry_points={
        "console_scripts": [
            "video-editor-pro=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.txt", "*.md", "*.yml", "*.yaml", "*.json"],
        "assets": ["*"],
        "docs": ["*"],
    },
    zip_safe=False,
    keywords="video editor, video processing, AI, free, open source",
    project_urls={
        "Bug Reports": "https://github.com/your-username/video-editor-pro/issues",
        "Source": "https://github.com/your-username/video-editor-pro",
        "Documentation": "https://github.com/your-username/video-editor-pro/docs",
    },
)
