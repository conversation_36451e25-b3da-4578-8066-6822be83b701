"""
إعدادات التطبيق الرئيسية
Application Settings Configuration
"""

import os
from pathlib import Path
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_env_path = Path(__file__).parent.parent.parent / '.env'
load_dotenv(load_env_path)

class AppSettings:
    """إعدادات التطبيق الأساسية"""
    
    # معلومات التطبيق
    APP_NAME = "Video Editor Pro"
    APP_VERSION = "1.0.0"
    APP_AUTHOR = "Video Editor Pro Team"
    
    # مسارات الملفات
    BASE_DIR = Path(__file__).parent.parent.parent
    SRC_DIR = BASE_DIR / "src"
    ASSETS_DIR = BASE_DIR / "assets"
    DOCS_DIR = BASE_DIR / "docs"
    TEMP_DIR = BASE_DIR / "temp"
    OUTPUT_DIR = BASE_DIR / "output"
    
    # إعدادات واجهة المستخدم
    WINDOW_WIDTH = 1200
    WINDOW_HEIGHT = 800
    MIN_WINDOW_WIDTH = 800
    MIN_WINDOW_HEIGHT = 600
    
    # إعدادات الفيديو الافتراضية
    DEFAULT_VIDEO_FORMAT = "mp4"
    DEFAULT_AUDIO_FORMAT = "mp3"
    DEFAULT_FRAME_RATE = 30
    DEFAULT_RESOLUTION = (1920, 1080)
    SUPPORTED_VIDEO_FORMATS = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
    SUPPORTED_AUDIO_FORMATS = ['.mp3', '.wav', '.aac', '.ogg', '.m4a']
    SUPPORTED_IMAGE_FORMATS = ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff']
    
    # إعدادات FFmpeg
    FFMPEG_PATH = os.getenv('FFMPEG_PATH', 'ffmpeg')
    FFPROBE_PATH = os.getenv('FFPROBE_PATH', 'ffprobe')
    
    # إعدادات الجودة
    VIDEO_QUALITY_PRESETS = {
        'low': {'crf': 28, 'preset': 'fast'},
        'medium': {'crf': 23, 'preset': 'medium'},
        'high': {'crf': 18, 'preset': 'slow'},
        'ultra': {'crf': 15, 'preset': 'veryslow'}
    }
    
    @classmethod
    def create_directories(cls):
        """إنشاء المجلدات المطلوبة"""
        directories = [cls.TEMP_DIR, cls.OUTPUT_DIR]
        for directory in directories:
            directory.mkdir(exist_ok=True)

class AISettings:
    """إعدادات الذكاء الاصطناعي والـ APIs"""
    
    # Hugging Face API
    HUGGINGFACE_API_KEY = os.getenv('HUGGINGFACE_API_KEY')
    HUGGINGFACE_API_URL = "https://api-inference.huggingface.co/models"
    
    # Google Cloud APIs
    GOOGLE_CLOUD_PROJECT_ID = os.getenv('GOOGLE_CLOUD_PROJECT_ID')
    GOOGLE_CLOUD_CREDENTIALS_PATH = os.getenv('GOOGLE_CLOUD_CREDENTIALS_PATH')
    
    # نماذج الذكاء الاصطناعي
    SPEECH_TO_TEXT_MODEL = "openai/whisper-large-v3"
    TRANSLATION_MODEL = "Helsinki-NLP/opus-mt-en-ar"
    SENTIMENT_ANALYSIS_MODEL = "cardiffnlp/twitter-roberta-base-sentiment-latest"
    TEXT_GENERATION_MODEL = "microsoft/DialoGPT-medium"
    
    # إعدادات المعالجة
    MAX_AUDIO_DURATION = 300  # 5 دقائق
    MAX_VIDEO_DURATION = 1800  # 30 دقيقة
    CHUNK_SIZE = 30  # ثواني لكل قطعة
    
    # حدود الاستخدام المجاني
    DAILY_API_LIMIT = 100
    MONTHLY_API_LIMIT = 1000

class UISettings:
    """إعدادات واجهة المستخدم"""
    
    # الألوان
    PRIMARY_COLOR = "#2196F3"
    SECONDARY_COLOR = "#FFC107"
    SUCCESS_COLOR = "#4CAF50"
    ERROR_COLOR = "#F44336"
    WARNING_COLOR = "#FF9800"
    BACKGROUND_COLOR = "#F5F5F5"
    TEXT_COLOR = "#212121"
    
    # الخطوط
    DEFAULT_FONT = ("Arial", 10)
    HEADER_FONT = ("Arial", 14, "bold")
    BUTTON_FONT = ("Arial", 9)
    
    # أيقونات
    ICON_SIZE = (24, 24)
    BUTTON_ICON_SIZE = (16, 16)
    
    # تخطيط
    PADDING = 10
    BUTTON_HEIGHT = 35
    ENTRY_HEIGHT = 30

class LoggingSettings:
    """إعدادات السجلات"""
    
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_FILE = AppSettings.BASE_DIR / 'logs' / 'app.log'
    MAX_LOG_SIZE = 10 * 1024 * 1024  # 10MB
    BACKUP_COUNT = 5

# إنشاء المجلدات المطلوبة عند استيراد الملف
AppSettings.create_directories()

# إنشاء مجلد السجلات
LoggingSettings.LOG_FILE.parent.mkdir(exist_ok=True)
