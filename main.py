#!/usr/bin/env python3
"""
Video Editor Pro - أداة تحرير الفيديو المتكاملة والمجانية
الملف الرئيسي لتشغيل التطبيق

Main entry point for Video Editor Pro application
"""

import sys
import os
import logging
from pathlib import Path

# إضافة مجلد src إلى مسار Python
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    import tkinter as tk
    from tkinter import messagebox
except ImportError:
    print("خطأ: لا يمكن استيراد tkinter. تأكد من تثبيت Python مع دعم tkinter.")
    print("Error: Cannot import tkinter. Make sure Python is installed with tkinter support.")
    sys.exit(1)

# استيراد الإعدادات
try:
    from config.settings import AppSettings, LoggingSettings
except ImportError as e:
    print(f"خطأ في استيراد الإعدادات: {e}")
    print(f"Settings import error: {e}")
    sys.exit(1)

def setup_logging():
    """إعداد نظام السجلات"""
    try:
        logging.basicConfig(
            level=getattr(logging, LoggingSettings.LOG_LEVEL),
            format=LoggingSettings.LOG_FORMAT,
            handlers=[
                logging.FileHandler(LoggingSettings.LOG_FILE, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        logger = logging.getLogger(__name__)
        logger.info("تم تشغيل نظام السجلات بنجاح")
        logger.info("Logging system initialized successfully")
        return logger
    except Exception as e:
        print(f"خطأ في إعداد السجلات: {e}")
        print(f"Logging setup error: {e}")
        return None

def check_dependencies():
    """فحص المتطلبات الأساسية"""
    missing_deps = []
    
    # فحص المكتبات المطلوبة
    required_modules = [
        'PIL', 'cv2', 'numpy', 'requests', 
        'moviepy', 'pydub', 'transformers'
    ]
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_deps.append(module)
    
    # فحص FFmpeg
    import subprocess
    try:
        subprocess.run([AppSettings.FFMPEG_PATH, '-version'], 
                      capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        missing_deps.append('FFmpeg')
    
    return missing_deps

def show_dependency_error(missing_deps):
    """عرض رسالة خطأ للمتطلبات المفقودة"""
    error_msg = "المتطلبات التالية مفقودة:\nMissing dependencies:\n\n"
    error_msg += "\n".join(f"- {dep}" for dep in missing_deps)
    error_msg += "\n\nيرجى تثبيتها باستخدام:\nPlease install them using:\n"
    error_msg += "pip install -r requirements.txt"
    
    if 'FFmpeg' in missing_deps:
        error_msg += "\n\nلتثبيت FFmpeg:\nTo install FFmpeg:\n"
        error_msg += "Windows: https://ffmpeg.org/download.html\n"
        error_msg += "macOS: brew install ffmpeg\n"
        error_msg += "Linux: sudo apt install ffmpeg"
    
    messagebox.showerror("خطأ في المتطلبات - Dependency Error", error_msg)

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    
    # إعداد السجلات
    logger = setup_logging()
    if logger:
        logger.info(f"بدء تشغيل {AppSettings.APP_NAME} v{AppSettings.APP_VERSION}")
        logger.info(f"Starting {AppSettings.APP_NAME} v{AppSettings.APP_VERSION}")
    
    # فحص المتطلبات
    missing_deps = check_dependencies()
    if missing_deps:
        show_dependency_error(missing_deps)
        return 1
    
    try:
        # استيراد واجهة المستخدم الرئيسية
        from gui.main_window import VideoEditorApp
        
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        app = VideoEditorApp(root)
        
        if logger:
            logger.info("تم تشغيل التطبيق بنجاح")
            logger.info("Application started successfully")
        
        # تشغيل التطبيق
        root.mainloop()
        
    except ImportError as e:
        error_msg = f"خطأ في استيراد واجهة المستخدم: {e}\nGUI import error: {e}"
        if logger:
            logger.error(error_msg)
        messagebox.showerror("خطأ - Error", error_msg)
        return 1
        
    except Exception as e:
        error_msg = f"خطأ غير متوقع: {e}\nUnexpected error: {e}"
        if logger:
            logger.error(error_msg, exc_info=True)
        messagebox.showerror("خطأ - Error", error_msg)
        return 1
    
    if logger:
        logger.info("تم إغلاق التطبيق")
        logger.info("Application closed")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
