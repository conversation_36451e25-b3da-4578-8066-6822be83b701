#!/usr/bin/env python3
"""
اختبار لوحة تقدم التحميل الجديدة
Test the new download progress window
"""

import sys
import os
from pathlib import Path
import tkinter as tk
from tkinter import ttk
import time
import threading

# إضافة مجلد src إلى مسار Python
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_progress_window_design():
    """اختبار تصميم نافذة التقدم"""
    
    print("🎨 اختبار تصميم نافذة التقدم")
    print("=" * 40)
    
    try:
        # إنشاء نافذة رئيسية
        root = tk.Tk()
        root.title("اختبار لوحة التقدم")
        root.geometry("300x200")
        
        # محاكاة الكلاس
        class MockWindow:
            def __init__(self, parent):
                self.window = parent
            
            def create_progress_window(self, title: str):
                """إنشاء نافذة شريط التقدم المحسنة"""
                
                window = tk.Toplevel(self.window)
                window.title(title)
                window.geometry("500x350")
                window.transient(self.window)
                window.grab_set()
                window.resizable(False, False)
                
                # توسيط النافذة
                window.update_idletasks()
                x = (window.winfo_screenwidth() // 2) - (500 // 2)
                y = (window.winfo_screenheight() // 2) - (350 // 2)
                window.geometry(f"500x350+{x}+{y}")
                
                # الإطار الرئيسي
                main_frame = ttk.Frame(window)
                main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
                
                # عنوان العملية
                title_label = ttk.Label(main_frame, text=title, font=("Arial", 12, "bold"))
                title_label.pack(pady=(0, 15))
                
                # معلومات الفيديو
                info_frame = ttk.LabelFrame(main_frame, text="معلومات الفيديو", padding="10")
                info_frame.pack(fill=tk.X, pady=(0, 15))
                
                video_title_label = ttk.Label(info_frame, text="العنوان: IShowSpeed Racing Video", wraplength=450)
                video_title_label.pack(anchor=tk.W, pady=(0, 5))
                
                video_info_label = ttk.Label(info_frame, text="القناة: IShowSpeed | المدة: 30:44 | الدقة: 1440p")
                video_info_label.pack(anchor=tk.W)
                
                # إطار التقدم
                progress_frame = ttk.LabelFrame(main_frame, text="تقدم التحميل", padding="10")
                progress_frame.pack(fill=tk.X, pady=(0, 15))
                
                # شريط التقدم الرئيسي
                progress_bar = ttk.Progressbar(progress_frame, mode='determinate', maximum=100)
                progress_bar.pack(fill=tk.X, pady=(0, 10))
                
                # تسمية النسبة المئوية
                percentage_label = ttk.Label(progress_frame, text="0%", font=("Arial", 10, "bold"))
                percentage_label.pack(pady=(0, 5))
                
                # تفاصيل التحميل
                details_frame = ttk.Frame(progress_frame)
                details_frame.pack(fill=tk.X)
                
                # السرعة والحجم
                speed_label = ttk.Label(details_frame, text="السرعة: --")
                speed_label.pack(side=tk.LEFT)
                
                size_label = ttk.Label(details_frame, text="الحجم: --")
                size_label.pack(side=tk.RIGHT)
                
                # الوقت المتبقي
                eta_label = ttk.Label(progress_frame, text="الوقت المتبقي: --")
                eta_label.pack(pady=(5, 0))
                
                # تسمية الحالة
                status_label = ttk.Label(main_frame, text="جاري التحضير...", font=("Arial", 9))
                status_label.pack(pady=(0, 10))
                
                # إطار الأزرار
                buttons_frame = ttk.Frame(main_frame)
                buttons_frame.pack(fill=tk.X)
                
                # زر الإلغاء
                cancel_button = ttk.Button(buttons_frame, text="إلغاء", state="normal")
                cancel_button.pack(side=tk.RIGHT)
                
                # زر إخفاء/إظهار التفاصيل
                details_button = ttk.Button(buttons_frame, text="إخفاء التفاصيل")
                details_button.pack(side=tk.LEFT)
                
                # متغير لتتبع حالة التفاصيل
                details_visible = tk.BooleanVar(value=True)
                
                def toggle_details():
                    if details_visible.get():
                        info_frame.pack_forget()
                        details_frame.pack_forget()
                        eta_label.pack_forget()
                        window.geometry("500x200")
                        details_button.config(text="إظهار التفاصيل")
                        details_visible.set(False)
                    else:
                        info_frame.pack(fill=tk.X, pady=(0, 15), before=progress_frame)
                        details_frame.pack(fill=tk.X, in_=progress_frame, after=percentage_label)
                        eta_label.pack(pady=(5, 0), in_=progress_frame)
                        window.geometry("500x350")
                        details_button.config(text="إخفاء التفاصيل")
                        details_visible.set(True)
                
                details_button.config(command=toggle_details)
                
                return {
                    'window': window,
                    'progress_bar': progress_bar,
                    'status_label': status_label,
                    'percentage_label': percentage_label,
                    'speed_label': speed_label,
                    'size_label': size_label,
                    'eta_label': eta_label,
                    'video_title_label': video_title_label,
                    'video_info_label': video_info_label,
                    'cancel_button': cancel_button,
                    'details_visible': details_visible
                }
        
        # إنشاء النافذة الوهمية
        mock_window = MockWindow(root)
        
        # زر لاختبار النافذة
        def show_progress():
            progress_window = mock_window.create_progress_window("تحميل فيديو IShowSpeed")
            
            # محاكاة التقدم
            def simulate_progress():
                for i in range(101):
                    if progress_window['window'].winfo_exists():
                        # تحديث شريط التقدم
                        progress_window['progress_bar'].config(value=i)
                        progress_window['percentage_label'].config(text=f"{i}%")
                        
                        # تحديث السرعة والحجم
                        speed = 2.5 + (i * 0.01)  # سرعة متغيرة
                        downloaded = (i * 50) / 100  # 50 MB إجمالي
                        
                        progress_window['speed_label'].config(text=f"السرعة: {speed:.1f} MB/s")
                        progress_window['size_label'].config(text=f"الحجم: {downloaded:.1f}/50.0 MB")
                        
                        # تحديث الوقت المتبقي
                        remaining_time = (100 - i) * 0.5  # ثانية
                        if remaining_time > 60:
                            eta_text = f"الوقت المتبقي: {remaining_time/60:.1f} دقيقة"
                        else:
                            eta_text = f"الوقت المتبقي: {remaining_time:.0f}s"
                        
                        progress_window['eta_label'].config(text=eta_text)
                        
                        # تحديث الحالة
                        if i < 100:
                            progress_window['status_label'].config(text=f"جاري التحميل... {i}%")
                        else:
                            progress_window['status_label'].config(text="تم التحميل بنجاح! ✅")
                            progress_window['speed_label'].config(text="السرعة: مكتمل")
                            progress_window['eta_label'].config(text="الوقت المتبقي: 0s")
                        
                        time.sleep(0.1)
                    else:
                        break
            
            # تشغيل المحاكاة في thread منفصل
            threading.Thread(target=simulate_progress, daemon=True).start()
        
        # واجهة الاختبار
        ttk.Label(root, text="اختبار لوحة تقدم التحميل", font=("Arial", 12, "bold")).pack(pady=20)
        
        ttk.Button(root, text="🎬 عرض لوحة التقدم", command=show_progress).pack(pady=10)
        
        ttk.Label(root, text="اضغط الزر لعرض لوحة التقدم المحسنة\nستظهر محاكاة لتحميل فيديو IShowSpeed", 
                 justify=tk.CENTER).pack(pady=10)
        
        print("✅ تم إنشاء نافذة الاختبار")
        print("اضغط 'عرض لوحة التقدم' لاختبار التصميم")
        
        # تشغيل النافذة
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التصميم: {e}")
        return False

def test_progress_features():
    """اختبار ميزات لوحة التقدم"""
    
    print("\n🔧 اختبار ميزات لوحة التقدم")
    print("=" * 40)
    
    features = [
        "شريط التقدم الرئيسي",
        "عرض النسبة المئوية",
        "عرض السرعة",
        "عرض الحجم المحمل/الإجمالي",
        "عرض الوقت المتبقي",
        "معلومات الفيديو",
        "زر الإلغاء",
        "زر إخفاء/إظهار التفاصيل",
        "توسيط النافذة",
        "منع تغيير الحجم"
    ]
    
    print("الميزات المتوفرة في لوحة التقدم الجديدة:")
    for i, feature in enumerate(features, 1):
        print(f"  {i}. ✅ {feature}")
    
    print(f"\nإجمالي الميزات: {len(features)}")
    
    return True

def main():
    """الدالة الرئيسية"""
    
    print("🧪 اختبار لوحة تقدم التحميل الجديدة")
    print("=" * 60)
    
    # اختبار الميزات
    features_test = test_progress_features()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("🎯 النتيجة النهائية")
    print("=" * 60)
    
    if features_test:
        print("🎉 جميع ميزات لوحة التقدم جاهزة!")
        print("\nالميزات الجديدة:")
        print("✅ واجهة محسنة وأكثر تفصيلاً")
        print("✅ عرض معلومات الفيديو")
        print("✅ تفاصيل التحميل (السرعة، الحجم، الوقت)")
        print("✅ زر إلغاء التحميل")
        print("✅ إمكانية إخفاء/إظهار التفاصيل")
        print("✅ تصميم احترافي ومتجاوب")
        
        # اختبار تفاعلي
        response = input("\nهل تريد اختبار التصميم تفاعلياً؟ (y/n): ")
        if response.lower() in ['y', 'yes', 'نعم', 'ن']:
            test_progress_window_design()
        
        print("\nيمكنك الآن:")
        print("1. تشغيل محلل البثوث: python run_livestream_analyzer.py")
        print("2. اختيار 'رابط YouTube'")
        print("3. لصق رابط IShowSpeed")
        print("4. الاستمتاع بلوحة التقدم الجديدة! 🎬")
        
        return 0
    else:
        print("⚠️ بعض المشاكل في لوحة التقدم")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
