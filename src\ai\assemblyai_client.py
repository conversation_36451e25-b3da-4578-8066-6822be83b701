"""
عميل AssemblyAI لتحويل الكلام إلى نص
AssemblyAI client for speech-to-text conversion
"""

import logging
import os
import time
from typing import Optional, Dict, Any, List
from pathlib import Path

logger = logging.getLogger(__name__)

# محاولة استيراد AssemblyAI
try:
    import assemblyai as aai
    ASSEMBLYAI_AVAILABLE = True
except ImportError:
    ASSEMBLYAI_AVAILABLE = False
    logger.warning("مكتبة AssemblyAI غير متوفرة. قم بتثبيتها: pip install assemblyai")

class AssemblyAIClient:
    """عميل للتفاعل مع AssemblyAI API"""
    
    def __init__(self, api_key: Optional[str] = None):
        """تهيئة عميل AssemblyAI"""
        self.api_key = api_key or os.getenv('ASSEMBLYAI_API_KEY')
        
        if not ASSEMBLYAI_AVAILABLE:
            logger.error("مكتبة AssemblyAI غير متوفرة")
            return
        
        if not self.api_key:
            logger.warning("مفتاح AssemblyAI API غير متوفر")
            logger.warning("AssemblyAI API key not available")
        else:
            # تعيين مفتاح API
            aai.settings.api_key = self.api_key
            logger.info("تم تهيئة عميل AssemblyAI بنجاح")
            logger.info("AssemblyAI client initialized successfully")
    
    def is_available(self) -> bool:
        """التحقق من توفر الخدمة"""
        return ASSEMBLYAI_AVAILABLE and bool(self.api_key)
    
    def transcribe_audio(self, audio_path: str, language: str = "en") -> Optional[str]:
        """تحويل الصوت إلى نص"""
        if not self.is_available():
            logger.error("AssemblyAI غير متوفر للتحويل")
            return None
        
        try:
            logger.info(f"بدء تحويل الصوت إلى نص: {audio_path}")
            
            # إعداد التكوين
            config = aai.TranscriptionConfig(
                speech_model=aai.SpeechModel.best,
                language_code=self._get_language_code(language),
                punctuate=True,
                format_text=True,
                speaker_labels=False,  # تعطيل تحديد المتحدثين لتوفير الوقت
                auto_highlights=True,  # تفعيل النقاط المهمة
                sentiment_analysis=True,  # تحليل المشاعر
                entity_detection=True,  # كشف الكيانات
            )
            
            # إنشاء المحول
            transcriber = aai.Transcriber(config=config)
            
            # تحويل الصوت
            transcript = transcriber.transcribe(audio_path)
            
            # التحقق من حالة التحويل
            if transcript.status == aai.TranscriptStatus.error:
                logger.error(f"فشل في تحويل الصوت: {transcript.error}")
                return None
            
            logger.info("تم تحويل الصوت إلى نص بنجاح")
            return transcript.text
            
        except Exception as e:
            logger.error(f"خطأ في تحويل الصوت إلى نص: {e}")
            return None
    
    def transcribe_with_details(self, audio_path: str, language: str = "en") -> Optional[Dict[str, Any]]:
        """تحويل الصوت إلى نص مع تفاصيل إضافية"""
        if not self.is_available():
            logger.error("AssemblyAI غير متوفر للتحويل")
            return None
        
        try:
            logger.info(f"بدء تحويل مفصل للصوت: {audio_path}")
            
            # إعداد التكوين المتقدم
            config = aai.TranscriptionConfig(
                speech_model=aai.SpeechModel.best,
                language_code=self._get_language_code(language),
                punctuate=True,
                format_text=True,
                speaker_labels=True,  # تحديد المتحدثين
                auto_highlights=True,  # النقاط المهمة
                sentiment_analysis=True,  # تحليل المشاعر
                entity_detection=True,  # كشف الكيانات
                auto_chapters=True,  # تقسيم تلقائي للفصول
                word_boost=["gaming", "stream", "live", "chat", "donation"],  # كلمات مهمة للبث
                boost_param="high"
            )
            
            # إنشاء المحول
            transcriber = aai.Transcriber(config=config)
            
            # تحويل الصوت
            transcript = transcriber.transcribe(audio_path)
            
            # التحقق من حالة التحويل
            if transcript.status == aai.TranscriptStatus.error:
                logger.error(f"فشل في تحويل الصوت: {transcript.error}")
                return None
            
            # جمع النتائج المفصلة
            result = {
                "text": transcript.text,
                "confidence": transcript.confidence,
                "words": [],
                "sentences": [],
                "highlights": [],
                "sentiment": None,
                "entities": [],
                "chapters": [],
                "speakers": []
            }
            
            # إضافة الكلمات مع التوقيتات
            if transcript.words:
                for word in transcript.words:
                    result["words"].append({
                        "text": word.text,
                        "start": word.start / 1000,  # تحويل إلى ثواني
                        "end": word.end / 1000,
                        "confidence": word.confidence
                    })
            
            # إضافة الجمل
            if hasattr(transcript, 'sentences') and transcript.sentences:
                for sentence in transcript.sentences:
                    result["sentences"].append({
                        "text": sentence.text,
                        "start": sentence.start / 1000,
                        "end": sentence.end / 1000,
                        "confidence": sentence.confidence
                    })
            
            # إضافة النقاط المهمة
            if transcript.auto_highlights:
                for highlight in transcript.auto_highlights.results:
                    result["highlights"].append({
                        "text": highlight.text,
                        "count": highlight.count,
                        "rank": highlight.rank,
                        "timestamps": [
                            {
                                "start": ts.start / 1000,
                                "end": ts.end / 1000
                            } for ts in highlight.timestamps
                        ]
                    })
            
            # إضافة تحليل المشاعر
            if transcript.sentiment_analysis_results:
                sentiments = []
                for sentiment in transcript.sentiment_analysis_results:
                    sentiments.append({
                        "text": sentiment.text,
                        "sentiment": sentiment.sentiment.value,
                        "confidence": sentiment.confidence,
                        "start": sentiment.start / 1000,
                        "end": sentiment.end / 1000
                    })
                result["sentiment"] = sentiments
            
            # إضافة الكيانات
            if transcript.entities:
                for entity in transcript.entities:
                    result["entities"].append({
                        "text": entity.text,
                        "entity_type": entity.entity_type.value,
                        "start": entity.start / 1000,
                        "end": entity.end / 1000
                    })
            
            # إضافة الفصول
            if transcript.chapters:
                for chapter in transcript.chapters:
                    result["chapters"].append({
                        "summary": chapter.summary,
                        "headline": chapter.headline,
                        "gist": chapter.gist,
                        "start": chapter.start / 1000,
                        "end": chapter.end / 1000
                    })
            
            logger.info("تم تحويل الصوت مع التفاصيل بنجاح")
            return result
            
        except Exception as e:
            logger.error(f"خطأ في التحويل المفصل: {e}")
            return None
    
    def find_exciting_moments(self, audio_path: str, language: str = "en") -> List[Dict[str, Any]]:
        """العثور على اللحظات المثيرة في الصوت"""
        if not self.is_available():
            return []
        
        try:
            # الحصول على التحويل المفصل
            transcript_data = self.transcribe_with_details(audio_path, language)
            
            if not transcript_data:
                return []
            
            exciting_moments = []
            
            # تحليل النقاط المهمة
            if transcript_data.get("highlights"):
                for highlight in transcript_data["highlights"]:
                    if highlight["rank"] >= 0.7:  # نقاط مهمة عالية
                        for timestamp in highlight["timestamps"]:
                            exciting_moments.append({
                                "type": "highlight",
                                "text": highlight["text"],
                                "start_time": timestamp["start"],
                                "end_time": timestamp["end"],
                                "confidence": highlight["rank"],
                                "source": "auto_highlights"
                            })
            
            # تحليل المشاعر للعثور على لحظات الإثارة
            if transcript_data.get("sentiment"):
                for sentiment in transcript_data["sentiment"]:
                    if sentiment["sentiment"] in ["POSITIVE"] and sentiment["confidence"] > 0.8:
                        exciting_moments.append({
                            "type": "positive_sentiment",
                            "text": sentiment["text"],
                            "start_time": sentiment["start"],
                            "end_time": sentiment["end"],
                            "confidence": sentiment["confidence"],
                            "source": "sentiment_analysis"
                        })
            
            # ترتيب حسب الثقة
            exciting_moments.sort(key=lambda x: x["confidence"], reverse=True)
            
            logger.info(f"تم العثور على {len(exciting_moments)} لحظة مثيرة")
            return exciting_moments
            
        except Exception as e:
            logger.error(f"خطأ في العثور على اللحظات المثيرة: {e}")
            return []
    
    def _get_language_code(self, language: str) -> str:
        """تحويل رمز اللغة إلى تنسيق AssemblyAI"""
        language_map = {
            "ar": "ar",
            "en": "en",
            "es": "es",
            "fr": "fr",
            "de": "de",
            "it": "it",
            "pt": "pt",
            "hi": "hi",
            "ja": "ja",
            "ko": "ko",
            "zh": "zh",
            "ru": "ru",
            "tr": "tr",
            "pl": "pl",
            "nl": "nl"
        }
        
        return language_map.get(language.lower(), "en")
    
    def get_transcription_cost(self, audio_duration_seconds: float) -> float:
        """حساب تكلفة التحويل التقريبية"""
        # AssemblyAI يحاسب بـ $0.00037 لكل ثانية
        cost_per_second = 0.00037
        return audio_duration_seconds * cost_per_second
    
    def check_quota(self) -> Optional[Dict[str, Any]]:
        """فحص الحصة المتبقية"""
        if not self.is_available():
            return None
        
        try:
            # AssemblyAI لا يوفر API لفحص الحصة مباشرة
            # لكن يمكن تتبع الاستخدام محلياً
            return {
                "status": "available",
                "message": "AssemblyAI متاح للاستخدام"
            }
            
        except Exception as e:
            logger.error(f"خطأ في فحص الحصة: {e}")
            return None
