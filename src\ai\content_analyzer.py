"""
محلل المحتوى والأداء
Content and performance analyzer
"""

import logging
import json
import sqlite3
from typing import Optional, Dict, List, Any, Tuple
from pathlib import Path
from datetime import datetime, timedelta
import statistics

from config.settings import AppSettings
from core.video_processor import VideoProcessor

logger = logging.getLogger(__name__)

class ContentAnalyzer:
    """محلل المحتوى والأداء"""
    
    def __init__(self):
        self.video_processor = VideoProcessor()
        self.db_path = AppSettings.BASE_DIR / "analytics.db"
        self._init_database()
        
        logger.info("تم تهيئة محلل المحتوى")
    
    def _init_database(self):
        """تهيئة قاعدة البيانات للتحليلات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # جدول الفيديوهات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS videos (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_path TEXT UNIQUE,
                    title TEXT,
                    duration REAL,
                    file_size INTEGER,
                    resolution TEXT,
                    fps REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    analysis_data TEXT
                )
            ''')
            
            # جدول التحليلات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS analytics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    video_id INTEGER,
                    analysis_type TEXT,
                    analysis_result TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (video_id) REFERENCES videos (id)
                )
            ''')
            
            # جدول الأداء المتوقع
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS performance_predictions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    video_id INTEGER,
                    predicted_views INTEGER,
                    predicted_engagement REAL,
                    confidence_score REAL,
                    factors TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (video_id) REFERENCES videos (id)
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info("تم تهيئة قاعدة البيانات للتحليلات")
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة قاعدة البيانات: {e}")
    
    def analyze_video_quality(self, video_path: str) -> Dict[str, Any]:
        """تحليل جودة الفيديو"""
        try:
            logger.info(f"بدء تحليل جودة الفيديو: {video_path}")
            
            # الحصول على معلومات الفيديو
            video_info = self.video_processor.get_video_info(video_path)
            if not video_info:
                return {}
            
            analysis = {
                "technical_quality": {},
                "content_quality": {},
                "optimization_suggestions": [],
                "overall_score": 0
            }
            
            # تحليل الجودة التقنية
            technical_score = self._analyze_technical_quality(video_info)
            analysis["technical_quality"] = technical_score
            
            # تحليل جودة المحتوى
            content_score = self._analyze_content_quality(video_path, video_info)
            analysis["content_quality"] = content_score
            
            # اقتراحات التحسين
            suggestions = self._generate_optimization_suggestions(
                technical_score, content_score
            )
            analysis["optimization_suggestions"] = suggestions
            
            # النتيجة الإجمالية
            overall_score = (
                technical_score.get("score", 0) * 0.4 +
                content_score.get("score", 0) * 0.6
            )
            analysis["overall_score"] = round(overall_score, 2)
            
            # حفظ التحليل
            self._save_video_analysis(video_path, analysis)
            
            logger.info(f"تم تحليل جودة الفيديو - النتيجة: {overall_score}")
            return analysis
            
        except Exception as e:
            logger.error(f"خطأ في تحليل جودة الفيديو: {e}")
            return {}
    
    def _analyze_technical_quality(self, video_info: Dict) -> Dict[str, Any]:
        """تحليل الجودة التقنية"""
        score_factors = []
        details = {}
        
        try:
            # تحليل الدقة
            width = video_info['video']['width']
            height = video_info['video']['height']
            resolution = f"{width}x{height}"
            
            if width >= 1920 and height >= 1080:
                resolution_score = 100
                details["resolution_quality"] = "ممتازة"
            elif width >= 1280 and height >= 720:
                resolution_score = 80
                details["resolution_quality"] = "جيدة"
            elif width >= 854 and height >= 480:
                resolution_score = 60
                details["resolution_quality"] = "متوسطة"
            else:
                resolution_score = 40
                details["resolution_quality"] = "منخفضة"
            
            score_factors.append(resolution_score)
            details["resolution"] = resolution
            
            # تحليل معدل الإطارات
            fps = video_info['video']['fps']
            if fps >= 60:
                fps_score = 100
                details["fps_quality"] = "ممتازة"
            elif fps >= 30:
                fps_score = 90
                details["fps_quality"] = "جيدة جداً"
            elif fps >= 24:
                fps_score = 80
                details["fps_quality"] = "جيدة"
            else:
                fps_score = 60
                details["fps_quality"] = "متوسطة"
            
            score_factors.append(fps_score)
            details["fps"] = fps
            
            # تحليل معدل البت
            bitrate = video_info['video']['bitrate']
            if bitrate > 0:
                # تقييم معدل البت بناءً على الدقة
                expected_bitrate = width * height * fps * 0.1  # تقدير تقريبي
                bitrate_ratio = bitrate / expected_bitrate if expected_bitrate > 0 else 0
                
                if bitrate_ratio >= 0.8:
                    bitrate_score = 100
                    details["bitrate_quality"] = "ممتازة"
                elif bitrate_ratio >= 0.6:
                    bitrate_score = 80
                    details["bitrate_quality"] = "جيدة"
                elif bitrate_ratio >= 0.4:
                    bitrate_score = 60
                    details["bitrate_quality"] = "متوسطة"
                else:
                    bitrate_score = 40
                    details["bitrate_quality"] = "منخفضة"
                
                score_factors.append(bitrate_score)
                details["bitrate"] = bitrate
            
            # تحليل الصوت
            audio_bitrate = video_info['audio']['bitrate']
            if audio_bitrate > 0:
                if audio_bitrate >= 128000:  # 128 kbps
                    audio_score = 100
                    details["audio_quality"] = "ممتازة"
                elif audio_bitrate >= 96000:  # 96 kbps
                    audio_score = 80
                    details["audio_quality"] = "جيدة"
                elif audio_bitrate >= 64000:  # 64 kbps
                    audio_score = 60
                    details["audio_quality"] = "متوسطة"
                else:
                    audio_score = 40
                    details["audio_quality"] = "منخفضة"
                
                score_factors.append(audio_score)
                details["audio_bitrate"] = audio_bitrate
            
            # حساب النتيجة الإجمالية
            overall_score = statistics.mean(score_factors) if score_factors else 0
            
            return {
                "score": round(overall_score, 2),
                "details": details,
                "factors_count": len(score_factors)
            }
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الجودة التقنية: {e}")
            return {"score": 0, "details": {}, "factors_count": 0}
    
    def _analyze_content_quality(self, video_path: str, video_info: Dict) -> Dict[str, Any]:
        """تحليل جودة المحتوى"""
        score_factors = []
        details = {}
        
        try:
            # تحليل المدة
            duration = video_info['duration']
            if 30 <= duration <= 300:  # 30 ثانية إلى 5 دقائق (مثالي للمحتوى القصير)
                duration_score = 100
                details["duration_quality"] = "مثالية للمحتوى القصير"
            elif 300 < duration <= 600:  # 5-10 دقائق
                duration_score = 90
                details["duration_quality"] = "جيدة للمحتوى المتوسط"
            elif 15 <= duration < 30:  # 15-30 ثانية
                duration_score = 80
                details["duration_quality"] = "قصيرة ولكن مقبولة"
            elif 600 < duration <= 1800:  # 10-30 دقيقة
                duration_score = 70
                details["duration_quality"] = "طويلة نسبياً"
            else:
                duration_score = 50
                details["duration_quality"] = "غير مثالية"
            
            score_factors.append(duration_score)
            details["duration"] = duration
            
            # تحليل نسبة العرض إلى الارتفاع
            width = video_info['video']['width']
            height = video_info['video']['height']
            aspect_ratio = width / height if height > 0 else 0
            
            if 0.5 <= aspect_ratio <= 0.6:  # عمودي (9:16 تقريباً)
                aspect_score = 100
                details["aspect_ratio_quality"] = "مثالية للمحتوى العمودي"
            elif 1.7 <= aspect_ratio <= 1.8:  # أفقي (16:9 تقريباً)
                aspect_score = 95
                details["aspect_ratio_quality"] = "مثالية للمحتوى الأفقي"
            elif aspect_ratio == 1.0:  # مربع
                aspect_score = 85
                details["aspect_ratio_quality"] = "جيدة للمحتوى المربع"
            else:
                aspect_score = 70
                details["aspect_ratio_quality"] = "نسبة غير شائعة"
            
            score_factors.append(aspect_score)
            details["aspect_ratio"] = round(aspect_ratio, 2)
            
            # تحليل حجم الملف (كفاءة الضغط)
            file_size = video_info['size']
            duration_minutes = duration / 60
            size_per_minute = file_size / duration_minutes if duration_minutes > 0 else 0
            
            # تقييم كفاءة الضغط (MB per minute)
            size_mb_per_min = size_per_minute / (1024 * 1024)
            
            if size_mb_per_min <= 10:  # أقل من 10 MB/min
                compression_score = 100
                details["compression_quality"] = "ممتازة"
            elif size_mb_per_min <= 20:  # 10-20 MB/min
                compression_score = 80
                details["compression_quality"] = "جيدة"
            elif size_mb_per_min <= 50:  # 20-50 MB/min
                compression_score = 60
                details["compression_quality"] = "متوسطة"
            else:
                compression_score = 40
                details["compression_quality"] = "ضعيفة"
            
            score_factors.append(compression_score)
            details["size_mb_per_minute"] = round(size_mb_per_min, 2)
            
            # حساب النتيجة الإجمالية
            overall_score = statistics.mean(score_factors) if score_factors else 0
            
            return {
                "score": round(overall_score, 2),
                "details": details,
                "factors_count": len(score_factors)
            }
            
        except Exception as e:
            logger.error(f"خطأ في تحليل جودة المحتوى: {e}")
            return {"score": 0, "details": {}, "factors_count": 0}
    
    def _generate_optimization_suggestions(self, technical_score: Dict, 
                                         content_score: Dict) -> List[str]:
        """توليد اقتراحات التحسين"""
        suggestions = []
        
        try:
            # اقتراحات تقنية
            tech_details = technical_score.get("details", {})
            
            if tech_details.get("resolution_quality") in ["منخفضة", "متوسطة"]:
                suggestions.append("فكر في تسجيل الفيديو بدقة أعلى (1080p أو أكثر)")
            
            if tech_details.get("fps_quality") in ["متوسطة"]:
                suggestions.append("استخدم معدل إطارات أعلى (30 fps على الأقل)")
            
            if tech_details.get("bitrate_quality") in ["منخفضة", "متوسطة"]:
                suggestions.append("اضبط معدل البت لتحسين جودة الصورة")
            
            if tech_details.get("audio_quality") in ["منخفضة", "متوسطة"]:
                suggestions.append("حسّن جودة الصوت (128 kbps على الأقل)")
            
            # اقتراحات المحتوى
            content_details = content_score.get("details", {})
            
            duration = content_details.get("duration", 0)
            if duration > 600:
                suggestions.append("فكر في تقصير الفيديو لزيادة المشاهدة")
            elif duration < 15:
                suggestions.append("فكر في إطالة الفيديو قليلاً لمحتوى أكثر")
            
            aspect_ratio = content_details.get("aspect_ratio", 0)
            if aspect_ratio and not (0.5 <= aspect_ratio <= 0.6 or 1.7 <= aspect_ratio <= 1.8):
                suggestions.append("استخدم نسبة عرض إلى ارتفاع شائعة (9:16 أو 16:9)")
            
            if content_details.get("compression_quality") == "ضعيفة":
                suggestions.append("حسّن إعدادات الضغط لتقليل حجم الملف")
            
            # اقتراحات عامة
            if technical_score.get("score", 0) < 70:
                suggestions.append("راجع الإعدادات التقنية لكاميرا التسجيل")
            
            if content_score.get("score", 0) < 70:
                suggestions.append("فكر في تحسين بنية وتنظيم المحتوى")
            
        except Exception as e:
            logger.error(f"خطأ في توليد الاقتراحات: {e}")
        
        return suggestions
    
    def predict_performance(self, video_analysis: Dict, 
                          historical_data: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """توقع أداء الفيديو"""
        try:
            logger.info("بدء توقع أداء الفيديو")
            
            prediction = {
                "predicted_views": 0,
                "predicted_engagement": 0.0,
                "confidence_score": 0.0,
                "performance_factors": [],
                "recommendations": []
            }
            
            # عوامل التوقع
            factors = []
            
            # الجودة التقنية
            tech_score = video_analysis.get("technical_quality", {}).get("score", 0)
            factors.append(("technical_quality", tech_score, 0.3))
            
            # جودة المحتوى
            content_score = video_analysis.get("content_quality", {}).get("score", 0)
            factors.append(("content_quality", content_score, 0.4))
            
            # النتيجة الإجمالية
            overall_score = video_analysis.get("overall_score", 0)
            factors.append(("overall_score", overall_score, 0.3))
            
            # حساب التوقعات
            weighted_score = sum(score * weight for _, score, weight in factors)
            
            # توقع المشاهدات (نموذج مبسط)
            base_views = 100  # حد أدنى
            max_views = 10000  # حد أقصى متوقع
            predicted_views = int(base_views + (weighted_score / 100) * (max_views - base_views))
            
            # توقع التفاعل
            base_engagement = 0.01  # 1%
            max_engagement = 0.15   # 15%
            predicted_engagement = base_engagement + (weighted_score / 100) * (max_engagement - base_engagement)
            
            # حساب الثقة
            confidence = min(0.8, weighted_score / 100)  # حد أقصى 80%
            
            prediction.update({
                "predicted_views": predicted_views,
                "predicted_engagement": round(predicted_engagement, 3),
                "confidence_score": round(confidence, 2),
                "performance_factors": factors
            })
            
            # توصيات لتحسين الأداء
            recommendations = self._generate_performance_recommendations(video_analysis)
            prediction["recommendations"] = recommendations
            
            logger.info(f"تم توقع الأداء - المشاهدات المتوقعة: {predicted_views}")
            return prediction
            
        except Exception as e:
            logger.error(f"خطأ في توقع الأداء: {e}")
            return {}
    
    def _generate_performance_recommendations(self, analysis: Dict) -> List[str]:
        """توليد توصيات لتحسين الأداء"""
        recommendations = []
        
        try:
            overall_score = analysis.get("overall_score", 0)
            
            if overall_score < 60:
                recommendations.append("حسّن الجودة التقنية والمحتوى قبل النشر")
            elif overall_score < 80:
                recommendations.append("راجع اقتراحات التحسين لزيادة الجودة")
            
            # توصيات محددة
            tech_details = analysis.get("technical_quality", {}).get("details", {})
            content_details = analysis.get("content_quality", {}).get("details", {})
            
            if content_details.get("duration", 0) > 300:
                recommendations.append("فكر في إنشاء مقاطع قصيرة إضافية من هذا الفيديو")
            
            if tech_details.get("resolution_quality") == "ممتازة":
                recommendations.append("جودة الفيديو ممتازة - مناسب للنشر على جميع المنصات")
            
            aspect_ratio = content_details.get("aspect_ratio", 0)
            if 0.5 <= aspect_ratio <= 0.6:
                recommendations.append("تنسيق عمودي مثالي لـ TikTok و Instagram Reels")
            elif 1.7 <= aspect_ratio <= 1.8:
                recommendations.append("تنسيق أفقي مثالي لـ YouTube و Facebook")
            
        except Exception as e:
            logger.error(f"خطأ في توليد توصيات الأداء: {e}")
        
        return recommendations
    
    def _save_video_analysis(self, video_path: str, analysis: Dict):
        """حفظ تحليل الفيديو في قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # إدراج أو تحديث الفيديو
            video_info = self.video_processor.get_video_info(video_path)
            
            cursor.execute('''
                INSERT OR REPLACE INTO videos 
                (file_path, duration, file_size, resolution, fps, analysis_data)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                video_path,
                video_info.get('duration', 0),
                video_info.get('size', 0),
                f"{video_info['video']['width']}x{video_info['video']['height']}",
                video_info['video']['fps'],
                json.dumps(analysis, ensure_ascii=False)
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"خطأ في حفظ التحليل: {e}")
    
    def get_analytics_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص التحليلات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # إحصائيات عامة
            cursor.execute('SELECT COUNT(*) FROM videos')
            total_videos = cursor.fetchone()[0]
            
            cursor.execute('SELECT AVG(duration) FROM videos')
            avg_duration = cursor.fetchone()[0] or 0
            
            cursor.execute('SELECT SUM(file_size) FROM videos')
            total_size = cursor.fetchone()[0] or 0
            
            # أحدث التحليلات
            cursor.execute('''
                SELECT analysis_data FROM videos 
                WHERE analysis_data IS NOT NULL 
                ORDER BY created_at DESC LIMIT 10
            ''')
            
            recent_analyses = []
            for row in cursor.fetchall():
                try:
                    analysis = json.loads(row[0])
                    recent_analyses.append(analysis.get("overall_score", 0))
                except:
                    continue
            
            avg_quality = statistics.mean(recent_analyses) if recent_analyses else 0
            
            conn.close()
            
            summary = {
                "total_videos": total_videos,
                "average_duration": round(avg_duration, 2),
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "average_quality_score": round(avg_quality, 2),
                "recent_analyses_count": len(recent_analyses)
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على ملخص التحليلات: {e}")
            return {}
