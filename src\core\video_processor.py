"""
وحدة معالجة الفيديو الأساسية
Core video processing module using FFmpeg
"""

import os
import subprocess
import logging
import tempfile
from pathlib import Path
from typing import Optional, Dict, List, Tuple, Union
import json

from config.settings import AppSettings

logger = logging.getLogger(__name__)

class VideoProcessor:
    """معالج الفيديو الأساسي باستخدام FFmpeg"""
    
    def __init__(self):
        self.ffmpeg_path = AppSettings.FFMPEG_PATH
        self.ffprobe_path = AppSettings.FFPROBE_PATH
        self.temp_dir = AppSettings.TEMP_DIR
        self.output_dir = AppSettings.OUTPUT_DIR
        
        # التحقق من وجود FFmpeg
        if not self._check_ffmpeg():
            raise RuntimeError("FFmpeg غير متوفر - FFmpeg not available")

        # إعدادات الجودة
        self.quality_presets = {
            'low': {'crf': 28, 'preset': 'fast'},
            'medium': {'crf': 23, 'preset': 'medium'},
            'high': {'crf': 18, 'preset': 'slow'},
            'ultra': {'crf': 15, 'preset': 'veryslow'}
        }
    
    def _check_ffmpeg(self) -> bool:
        """التحقق من توفر FFmpeg"""
        try:
            subprocess.run([self.ffmpeg_path, '-version'], 
                         capture_output=True, check=True)
            subprocess.run([self.ffprobe_path, '-version'], 
                         capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def get_video_info(self, video_path: str) -> Dict:
        """الحصول على معلومات الفيديو"""
        try:
            cmd = [
                self.ffprobe_path,
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                '-show_streams',
                video_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            info = json.loads(result.stdout)
            
            # استخراج معلومات الفيديو
            video_stream = next((s for s in info['streams'] if s['codec_type'] == 'video'), None)
            audio_stream = next((s for s in info['streams'] if s['codec_type'] == 'audio'), None)
            
            return {
                'duration': float(info['format'].get('duration', 0)),
                'size': int(info['format'].get('size', 0)),
                'bitrate': int(info['format'].get('bit_rate', 0)),
                'video': {
                    'codec': video_stream.get('codec_name') if video_stream else None,
                    'width': int(video_stream.get('width', 0)) if video_stream else 0,
                    'height': int(video_stream.get('height', 0)) if video_stream else 0,
                    'fps': eval(video_stream.get('r_frame_rate', '0/1')) if video_stream else 0,
                    'bitrate': int(video_stream.get('bit_rate', 0)) if video_stream else 0
                },
                'audio': {
                    'codec': audio_stream.get('codec_name') if audio_stream else None,
                    'sample_rate': int(audio_stream.get('sample_rate', 0)) if audio_stream else 0,
                    'channels': int(audio_stream.get('channels', 0)) if audio_stream else 0,
                    'bitrate': int(audio_stream.get('bit_rate', 0)) if audio_stream else 0
                }
            }
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات الفيديو: {e}")
            raise
    
    def trim_video(self, input_path: str, output_path: str, 
                   start_time: float, end_time: float) -> bool:
        """قص الفيديو"""
        try:
            duration = end_time - start_time
            
            cmd = [
                self.ffmpeg_path,
                '-i', input_path,
                '-ss', str(start_time),
                '-t', str(duration),
                '-c', 'copy',  # نسخ بدون إعادة ترميز للسرعة
                '-avoid_negative_ts', 'make_zero',
                '-y',  # الكتابة فوق الملف الموجود
                output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"تم قص الفيديو بنجاح: {output_path}")
                return True
            else:
                logger.error(f"خطأ في قص الفيديو: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في قص الفيديو: {e}")
            return False
    
    def resize_video(self, input_path: str, output_path: str, 
                     width: int, height: int, quality: str = 'medium') -> bool:
        """تغيير حجم الفيديو"""
        try:
            quality_settings = AppSettings.VIDEO_QUALITY_PRESETS.get(quality, 
                AppSettings.VIDEO_QUALITY_PRESETS['medium'])
            
            cmd = [
                self.ffmpeg_path,
                '-i', input_path,
                '-vf', f'scale={width}:{height}',
                '-c:v', 'libx264',
                '-crf', str(quality_settings['crf']),
                '-preset', quality_settings['preset'],
                '-c:a', 'aac',
                '-b:a', '128k',
                '-y',
                output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"تم تغيير حجم الفيديو بنجاح: {output_path}")
                return True
            else:
                logger.error(f"خطأ في تغيير حجم الفيديو: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في تغيير حجم الفيديو: {e}")
            return False
    
    def add_text_overlay(self, input_path: str, output_path: str,
                        text: str, position: str = 'center',
                        font_size: int = 24, font_color: str = 'white',
                        start_time: float = 0, duration: Optional[float] = None) -> bool:
        """إضافة نص على الفيديو"""
        try:
            # تحديد موقع النص
            position_map = {
                'top-left': 'x=10:y=10',
                'top-center': 'x=(w-text_w)/2:y=10',
                'top-right': 'x=w-text_w-10:y=10',
                'center-left': 'x=10:y=(h-text_h)/2',
                'center': 'x=(w-text_w)/2:y=(h-text_h)/2',
                'center-right': 'x=w-text_w-10:y=(h-text_h)/2',
                'bottom-left': 'x=10:y=h-text_h-10',
                'bottom-center': 'x=(w-text_w)/2:y=h-text_h-10',
                'bottom-right': 'x=w-text_w-10:y=h-text_h-10'
            }
            
            pos = position_map.get(position, position_map['center'])
            
            # إعداد فلتر النص
            text_filter = f"drawtext=text='{text}':fontsize={font_size}:fontcolor={font_color}:{pos}"
            
            if duration:
                text_filter += f":enable='between(t,{start_time},{start_time + duration})'"
            elif start_time > 0:
                text_filter += f":enable='gte(t,{start_time})'"
            
            cmd = [
                self.ffmpeg_path,
                '-i', input_path,
                '-vf', text_filter,
                '-c:a', 'copy',
                '-y',
                output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"تم إضافة النص بنجاح: {output_path}")
                return True
            else:
                logger.error(f"خطأ في إضافة النص: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في إضافة النص: {e}")
            return False
    
    def convert_format(self, input_path: str, output_path: str,
                      output_format: str = 'mp4', quality: str = 'medium') -> bool:
        """تحويل تنسيق الفيديو"""
        try:
            quality_settings = AppSettings.VIDEO_QUALITY_PRESETS.get(quality,
                AppSettings.VIDEO_QUALITY_PRESETS['medium'])
            
            cmd = [
                self.ffmpeg_path,
                '-i', input_path,
                '-c:v', 'libx264',
                '-crf', str(quality_settings['crf']),
                '-preset', quality_settings['preset'],
                '-c:a', 'aac',
                '-b:a', '128k',
                '-f', output_format,
                '-y',
                output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"تم تحويل التنسيق بنجاح: {output_path}")
                return True
            else:
                logger.error(f"خطأ في تحويل التنسيق: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في تحويل التنسيق: {e}")
            return False
    
    def extract_audio(self, input_path: str, output_path: str,
                     audio_format: str = 'mp3') -> bool:
        """استخراج الصوت من الفيديو"""
        try:
            cmd = [
                self.ffmpeg_path,
                '-i', input_path,
                '-vn',  # بدون فيديو
                '-acodec', 'mp3' if audio_format == 'mp3' else 'copy',
                '-ab', '192k',
                '-y',
                output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"تم استخراج الصوت بنجاح: {output_path}")
                return True
            else:
                logger.error(f"خطأ في استخراج الصوت: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في استخراج الصوت: {e}")
            return False

    def add_zoom_effect(self, input_path: str, output_path: str,
                       zoom_factor: float = 1.5, start_time: float = 0,
                       duration: float = 5.0) -> bool:
        """إضافة تأثير التكبير"""
        try:
            # حساب قيم التكبير
            zoom_filter = f"zoompan=z='if(between(t,{start_time},{start_time + duration}),min(zoom+0.0015,{zoom_factor}),1)':d=1:x=iw/2-(iw/zoom/2):y=ih/2-(ih/zoom/2)"

            cmd = [
                self.ffmpeg_path,
                '-i', input_path,
                '-vf', zoom_filter,
                '-c:a', 'copy',
                '-y',
                output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                logger.info(f"تم إضافة تأثير التكبير بنجاح: {output_path}")
                return True
            else:
                logger.error(f"خطأ في إضافة تأثير التكبير: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"خطأ في إضافة تأثير التكبير: {e}")
            return False

    def merge_videos(self, video_paths: List[str], output_path: str,
                    transition_duration: float = 1.0) -> bool:
        """دمج عدة فيديوهات مع انتقالات"""
        try:
            if len(video_paths) < 2:
                logger.error("يجب توفير فيديوهين على الأقل للدمج")
                return False

            # إنشاء ملف قائمة للدمج
            list_file = self.temp_dir / "merge_list.txt"
            with open(list_file, 'w', encoding='utf-8') as f:
                for video_path in video_paths:
                    f.write(f"file '{video_path}'\n")

            cmd = [
                self.ffmpeg_path,
                '-f', 'concat',
                '-safe', '0',
                '-i', str(list_file),
                '-c', 'copy',
                '-y',
                output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            # حذف الملف المؤقت
            if list_file.exists():
                list_file.unlink()

            if result.returncode == 0:
                logger.info(f"تم دمج الفيديوهات بنجاح: {output_path}")
                return True
            else:
                logger.error(f"خطأ في دمج الفيديوهات: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"خطأ في دمج الفيديوهات: {e}")
            return False

    def add_fade_transition(self, input_path: str, output_path: str,
                           fade_in_duration: float = 1.0,
                           fade_out_duration: float = 1.0) -> bool:
        """إضافة تأثير الظهور والاختفاء التدريجي"""
        try:
            video_info = self.get_video_info(input_path)
            duration = video_info['duration']

            fade_filter = f"fade=t=in:st=0:d={fade_in_duration},fade=t=out:st={duration - fade_out_duration}:d={fade_out_duration}"

            cmd = [
                self.ffmpeg_path,
                '-i', input_path,
                '-vf', fade_filter,
                '-c:a', 'copy',
                '-y',
                output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                logger.info(f"تم إضافة تأثير الانتقال بنجاح: {output_path}")
                return True
            else:
                logger.error(f"خطأ في إضافة تأثير الانتقال: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"خطأ في إضافة تأثير الانتقال: {e}")
            return False
