"""
نافذة إدارة تاريخ الفيديوهات والمقاطع المحملة
Video History Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any
import threading
import os

from utils.video_history_manager import VideoHistoryManager, VideoRecord
from ai.livestream_analyzer import LivestreamAnalyzer
from utils.file_utils import FileManager

logger = logging.getLogger(__name__)

class VideoHistoryWindow:
    """نافذة إدارة تاريخ الفيديوهات"""

    def __init__(self, parent: tk.Tk):
        self.parent = parent
        self.history_manager = VideoHistoryManager()
        self.analyzer = LivestreamAnalyzer()
        self.file_manager = FileManager()

        # متغيرات التطبيق
        self.selected_video: Optional[VideoRecord] = None
        self.is_processing = False

        # إنشاء النافذة
        self.create_window()

        logger.info("تم تشغيل نافذة إدارة تاريخ الفيديوهات")

    def create_window(self):
        """إنشاء النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("إدارة الفيديوهات المحملة - Video History Manager")
        self.window.geometry("1200x800")
        self.window.resizable(True, True)

        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()

        # إنشاء المحتوى
        self.create_widgets()

        # تحميل البيانات
        self.refresh_video_list()

    def create_widgets(self):
        """إنشاء عناصر واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان النافذة
        title_label = ttk.Label(main_frame, text="📚 إدارة الفيديوهات المحملة",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 10))

        # إطار الأدوات العلوية
        toolbar_frame = ttk.Frame(main_frame)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))

        # أزرار الأدوات
        ttk.Button(toolbar_frame, text="🔄 تحديث", command=self.refresh_video_list).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="📊 إحصائيات", command=self.show_statistics).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="🗑️ تنظيف", command=self.cleanup_missing_files).pack(side=tk.LEFT, padx=(0, 5))

        # شريط البحث
        search_frame = ttk.Frame(toolbar_frame)
        search_frame.pack(side=tk.RIGHT)

        ttk.Label(search_frame, text="🔍 بحث:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_changed)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=20)
        search_entry.pack(side=tk.LEFT)

        # إطار المحتوى الرئيسي
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # إطار قائمة الفيديوهات (يسار)
        videos_frame = ttk.LabelFrame(content_frame, text="📹 الفيديوهات المحملة", padding="5")
        videos_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # قائمة الفيديوهات
        self.create_videos_list(videos_frame)

        # إطار التفاصيل (يمين)
        details_frame = ttk.LabelFrame(content_frame, text="📋 تفاصيل الفيديو", padding="5")
        details_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=False, padx=(5, 0))
        details_frame.configure(width=400)

        # تفاصيل الفيديو
        self.create_details_panel(details_frame)

        # إطار الأزرار السفلية
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))

        # أزرار العمليات
        ttk.Button(buttons_frame, text="▶️ تشغيل", command=self.play_video).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="🔄 إعادة معالجة", command=self.reprocess_video).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="📁 فتح المجلد", command=self.open_video_folder).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="🗑️ حذف", command=self.delete_video).pack(side=tk.LEFT, padx=(0, 5))

        # زر الإغلاق
        ttk.Button(buttons_frame, text="❌ إغلاق", command=self.window.destroy).pack(side=tk.RIGHT)

        # إغلاق النافذة
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_videos_list(self, parent):
        """إنشاء قائمة الفيديوهات"""
        # إطار القائمة مع شريط التمرير
        list_frame = ttk.Frame(parent)
        list_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء Treeview
        columns = ('title', 'date', 'duration', 'size', 'status')
        self.videos_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        # تعريف الأعمدة
        self.videos_tree.heading('title', text='العنوان')
        self.videos_tree.heading('date', text='تاريخ التحميل')
        self.videos_tree.heading('duration', text='المدة')
        self.videos_tree.heading('size', text='الحجم')
        self.videos_tree.heading('status', text='الحالة')

        # تحديد عرض الأعمدة
        self.videos_tree.column('title', width=300)
        self.videos_tree.column('date', width=120)
        self.videos_tree.column('duration', width=80)
        self.videos_tree.column('size', width=80)
        self.videos_tree.column('status', width=100)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.videos_tree.yview)
        self.videos_tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط العناصر
        self.videos_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # ربط الأحداث
        self.videos_tree.bind('<<TreeviewSelect>>', self.on_video_selected)
        self.videos_tree.bind('<Double-1>', self.on_video_double_click)

    def create_details_panel(self, parent):
        """إنشاء لوحة التفاصيل"""
        # إطار قابل للتمرير
        canvas = tk.Canvas(parent, width=380)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # المعلومات الأساسية
        info_frame = ttk.LabelFrame(scrollable_frame, text="📋 معلومات أساسية", padding="5")
        info_frame.pack(fill=tk.X, pady=(0, 5))

        self.title_label = ttk.Label(info_frame, text="العنوان: غير محدد", wraplength=350)
        self.title_label.pack(anchor=tk.W, pady=2)

        self.url_label = ttk.Label(info_frame, text="الرابط: غير محدد", wraplength=350)
        self.url_label.pack(anchor=tk.W, pady=2)

        self.date_label = ttk.Label(info_frame, text="تاريخ التحميل: غير محدد")
        self.date_label.pack(anchor=tk.W, pady=2)

        self.size_label = ttk.Label(info_frame, text="الحجم: غير محدد")
        self.size_label.pack(anchor=tk.W, pady=2)

        self.duration_label = ttk.Label(info_frame, text="المدة: غير محدد")
        self.duration_label.pack(anchor=tk.W, pady=2)

        # حالة المعالجة
        processing_frame = ttk.LabelFrame(scrollable_frame, text="⚙️ حالة المعالجة", padding="5")
        processing_frame.pack(fill=tk.X, pady=(0, 5))

        self.processed_label = ttk.Label(processing_frame, text="المعالجة: لم تتم")
        self.processed_label.pack(anchor=tk.W, pady=2)

        self.highlights_label = ttk.Label(processing_frame, text="عدد اللقطات: 0")
        self.highlights_label.pack(anchor=tk.W, pady=2)

        self.processing_date_label = ttk.Label(processing_frame, text="تاريخ المعالجة: غير محدد")
        self.processing_date_label.pack(anchor=tk.W, pady=2)

        # العلامات والملاحظات
        tags_frame = ttk.LabelFrame(scrollable_frame, text="🏷️ العلامات والملاحظات", padding="5")
        tags_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(tags_frame, text="العلامات:").pack(anchor=tk.W)
        self.tags_var = tk.StringVar()
        self.tags_entry = ttk.Entry(tags_frame, textvariable=self.tags_var, width=45)
        self.tags_entry.pack(fill=tk.X, pady=2)

        ttk.Label(tags_frame, text="الملاحظات:").pack(anchor=tk.W, pady=(5, 0))
        self.notes_text = tk.Text(tags_frame, height=4, width=45, wrap=tk.WORD)
        self.notes_text.pack(fill=tk.X, pady=2)

        # زر الحفظ
        ttk.Button(tags_frame, text="💾 حفظ التغييرات", command=self.save_video_details).pack(pady=5)

        # تخطيط العناصر
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def refresh_video_list(self):
        """تحديث قائمة الفيديوهات"""
        try:
            # مسح القائمة الحالية
            for item in self.videos_tree.get_children():
                self.videos_tree.delete(item)

            # الحصول على الفيديوهات
            videos = self.history_manager.get_all_videos()

            # إضافة الفيديوهات إلى القائمة
            for video in videos:
                # تنسيق البيانات
                title = video.title[:50] + "..." if len(video.title) > 50 else video.title
                date = datetime.fromisoformat(video.download_date).strftime("%Y-%m-%d %H:%M")
                duration = self.format_duration(video.duration)
                size = self.format_file_size(video.file_size)
                status = "✅ معالج" if video.processed else "⏳ غير معالج"

                # إضافة إلى القائمة
                item = self.videos_tree.insert('', 'end', values=(title, date, duration, size, status))
                # حفظ معرف الفيديو مع العنصر
                self.videos_tree.set(item, 'video_id', video.id)

            logger.info(f"تم تحديث قائمة الفيديوهات - {len(videos)} فيديو")

        except Exception as e:
            logger.error(f"خطأ في تحديث قائمة الفيديوهات: {e}")
            messagebox.showerror("خطأ", f"خطأ في تحديث القائمة: {e}")

    def format_duration(self, seconds: float) -> str:
        """تنسيق مدة الفيديو"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = int(seconds % 60)

        if hours > 0:
            return f"{hours}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes}:{seconds:02d}"

    def format_file_size(self, size_bytes: int) -> str:
        """تنسيق حجم الملف"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

    def on_video_selected(self, event):
        """عند اختيار فيديو من القائمة"""
        try:
            selection = self.videos_tree.selection()
            if not selection:
                self.selected_video = None
                self.clear_details_panel()
                return

            # الحصول على معرف الفيديو
            item = selection[0]
            video_id = self.videos_tree.set(item, 'video_id')

            # الحصول على بيانات الفيديو
            self.selected_video = self.history_manager.get_video_by_id(video_id)

            if self.selected_video:
                self.update_details_panel()

        except Exception as e:
            logger.error(f"خطأ في اختيار الفيديو: {e}")

    def on_video_double_click(self, event):
        """عند النقر المزدوج على فيديو"""
        if self.selected_video:
            self.play_video()

    def update_details_panel(self):
        """تحديث لوحة التفاصيل"""
        if not self.selected_video:
            return

        try:
            video = self.selected_video

            # المعلومات الأساسية
            self.title_label.config(text=f"العنوان: {video.title}")
            self.url_label.config(text=f"الرابط: {video.url}")

            date = datetime.fromisoformat(video.download_date).strftime("%Y-%m-%d %H:%M")
            self.date_label.config(text=f"تاريخ التحميل: {date}")

            self.size_label.config(text=f"الحجم: {self.format_file_size(video.file_size)}")
            self.duration_label.config(text=f"المدة: {self.format_duration(video.duration)}")

            # حالة المعالجة
            if video.processed:
                self.processed_label.config(text="المعالجة: ✅ تمت")
                self.highlights_label.config(text=f"عدد اللقطات: {video.highlights_count}")
                if video.processing_date:
                    proc_date = datetime.fromisoformat(video.processing_date).strftime("%Y-%m-%d %H:%M")
                    self.processing_date_label.config(text=f"تاريخ المعالجة: {proc_date}")
            else:
                self.processed_label.config(text="المعالجة: ⏳ لم تتم")
                self.highlights_label.config(text="عدد اللقطات: 0")
                self.processing_date_label.config(text="تاريخ المعالجة: غير محدد")

            # العلامات والملاحظات
            self.tags_var.set(", ".join(video.tags))
            self.notes_text.delete(1.0, tk.END)
            self.notes_text.insert(1.0, video.notes)

        except Exception as e:
            logger.error(f"خطأ في تحديث لوحة التفاصيل: {e}")

    def clear_details_panel(self):
        """مسح لوحة التفاصيل"""
        self.title_label.config(text="العنوان: غير محدد")
        self.url_label.config(text="الرابط: غير محدد")
        self.date_label.config(text="تاريخ التحميل: غير محدد")
        self.size_label.config(text="الحجم: غير محدد")
        self.duration_label.config(text="المدة: غير محدد")
        self.processed_label.config(text="المعالجة: لم تتم")
        self.highlights_label.config(text="عدد اللقطات: 0")
        self.processing_date_label.config(text="تاريخ المعالجة: غير محدد")
        self.tags_var.set("")
        self.notes_text.delete(1.0, tk.END)

    def save_video_details(self):
        """حفظ تفاصيل الفيديو"""
        if not self.selected_video:
            messagebox.showwarning("تحذير", "لم يتم اختيار فيديو")
            return

        try:
            # حفظ العلامات
            tags_text = self.tags_var.get().strip()
            tags = [tag.strip() for tag in tags_text.split(",") if tag.strip()]
            self.history_manager.update_video_tags(self.selected_video.id, tags)

            # حفظ الملاحظات
            notes = self.notes_text.get(1.0, tk.END).strip()
            self.history_manager.update_video_notes(self.selected_video.id, notes)

            # تحديث البيانات المحلية
            self.selected_video.tags = tags
            self.selected_video.notes = notes

            messagebox.showinfo("نجح", "تم حفظ التغييرات بنجاح")

        except Exception as e:
            logger.error(f"خطأ في حفظ تفاصيل الفيديو: {e}")
            messagebox.showerror("خطأ", f"خطأ في حفظ التغييرات: {e}")

    def on_search_changed(self, *args):
        """عند تغيير نص البحث"""
        query = self.search_var.get().strip()

        if not query:
            self.refresh_video_list()
            return

        try:
            # البحث في الفيديوهات
            results = self.history_manager.search_videos(query)

            # مسح القائمة الحالية
            for item in self.videos_tree.get_children():
                self.videos_tree.delete(item)

            # إضافة النتائج
            for video in results:
                title = video.title[:50] + "..." if len(video.title) > 50 else video.title
                date = datetime.fromisoformat(video.download_date).strftime("%Y-%m-%d %H:%M")
                duration = self.format_duration(video.duration)
                size = self.format_file_size(video.file_size)
                status = "✅ معالج" if video.processed else "⏳ غير معالج"

                item = self.videos_tree.insert('', 'end', values=(title, date, duration, size, status))
                self.videos_tree.set(item, 'video_id', video.id)

        except Exception as e:
            logger.error(f"خطأ في البحث: {e}")

    def play_video(self):
        """تشغيل الفيديو المحدد"""
        if not self.selected_video:
            messagebox.showwarning("تحذير", "لم يتم اختيار فيديو")
            return

        try:
            video_path = Path(self.selected_video.file_path)
            if not video_path.exists():
                messagebox.showerror("خطأ", "ملف الفيديو غير موجود")
                return

            # فتح الفيديو بالبرنامج الافتراضي
            import subprocess
            import platform

            if platform.system() == "Windows":
                os.startfile(str(video_path))
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", str(video_path)])
            else:  # Linux
                subprocess.run(["xdg-open", str(video_path)])

        except Exception as e:
            logger.error(f"خطأ في تشغيل الفيديو: {e}")
            messagebox.showerror("خطأ", f"خطأ في تشغيل الفيديو: {e}")

    def open_video_folder(self):
        """فتح مجلد الفيديو"""
        if not self.selected_video:
            messagebox.showwarning("تحذير", "لم يتم اختيار فيديو")
            return

        try:
            video_path = Path(self.selected_video.file_path)
            folder_path = video_path.parent

            if not folder_path.exists():
                messagebox.showerror("خطأ", "مجلد الفيديو غير موجود")
                return

            # فتح المجلد
            import subprocess
            import platform

            if platform.system() == "Windows":
                subprocess.run(["explorer", str(folder_path)])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", str(folder_path)])
            else:  # Linux
                subprocess.run(["xdg-open", str(folder_path)])

        except Exception as e:
            logger.error(f"خطأ في فتح المجلد: {e}")
            messagebox.showerror("خطأ", f"خطأ في فتح المجلد: {e}")

    def reprocess_video(self):
        """إعادة معالجة الفيديو المحدد"""
        if not self.selected_video:
            messagebox.showwarning("تحذير", "لم يتم اختيار فيديو")
            return

        if self.is_processing:
            messagebox.showwarning("تحذير", "معالجة أخرى جارية")
            return

        try:
            video_path = Path(self.selected_video.file_path)
            if not video_path.exists():
                messagebox.showerror("خطأ", "ملف الفيديو غير موجود")
                return

            # تأكيد إعادة المعالجة
            if self.selected_video.processed:
                response = messagebox.askyesno(
                    "تأكيد إعادة المعالجة",
                    f"هذا الفيديو تم معالجته سابقاً ({self.selected_video.highlights_count} لقطة).\n"
                    "هل تريد إعادة معالجته؟"
                )
                if not response:
                    return

            # نافذة خيارات المعالجة
            self.show_processing_options_dialog()

        except Exception as e:
            logger.error(f"خطأ في إعادة معالجة الفيديو: {e}")
            messagebox.showerror("خطأ", f"خطأ في إعادة المعالجة: {e}")

    def show_processing_options_dialog(self):
        """عرض نافذة خيارات المعالجة"""
        dialog = tk.Toplevel(self.window)
        dialog.title("خيارات المعالجة")
        dialog.geometry("400x300")
        dialog.transient(self.window)
        dialog.grab_set()

        # توسيط النافذة
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (dialog.winfo_screenheight() // 2) - (300 // 2)
        dialog.geometry(f"400x300+{x}+{y}")

        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان
        ttk.Label(main_frame, text="⚙️ خيارات المعالجة", font=("Arial", 14, "bold")).pack(pady=(0, 20))

        # نوع المعالجة
        method_frame = ttk.LabelFrame(main_frame, text="نوع المعالجة", padding="10")
        method_frame.pack(fill=tk.X, pady=(0, 10))

        method_var = tk.StringVar(value="advanced")
        ttk.Radiobutton(method_frame, text="🧠 معالجة متقدمة (مستحسن)",
                       variable=method_var, value="advanced").pack(anchor=tk.W)
        ttk.Radiobutton(method_frame, text="⚡ معالجة أساسية (سريعة)",
                       variable=method_var, value="basic").pack(anchor=tk.W)

        # عدد المقاطع
        clips_frame = ttk.LabelFrame(main_frame, text="عدد المقاطع المطلوبة", padding="10")
        clips_frame.pack(fill=tk.X, pady=(0, 10))

        clips_var = tk.IntVar(value=5)
        clips_scale = ttk.Scale(clips_frame, from_=1, to=10, variable=clips_var, orient=tk.HORIZONTAL)
        clips_scale.pack(fill=tk.X)

        clips_label = ttk.Label(clips_frame, text="5 مقاطع")
        clips_label.pack()

        def update_clips_label(*args):
            clips_label.config(text=f"{clips_var.get()} مقاطع")
        clips_var.trace('w', update_clips_label)

        # أزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(20, 0))

        def start_processing():
            dialog.destroy()
            self.start_video_processing(method_var.get(), clips_var.get())

        ttk.Button(buttons_frame, text="🚀 بدء المعالجة", command=start_processing).pack(side=tk.LEFT)
        ttk.Button(buttons_frame, text="❌ إلغاء", command=dialog.destroy).pack(side=tk.RIGHT)

    def start_video_processing(self, method: str, target_clips: int):
        """بدء معالجة الفيديو"""
        if not self.selected_video:
            return

        self.is_processing = True

        # إنشاء نافذة التقدم
        progress_window = self.create_progress_window()

        def processing_thread():
            start_time = datetime.now()
            success = False
            error_message = None
            results_count = 0
            output_files = []

            try:
                # تحديث الحالة
                self.window.after(0, lambda: progress_window['status_label'].config(text="بدء المعالجة..."))

                # اختيار نوع المعالجة
                if method == "advanced":
                    results = self.analyzer.analyze_long_livestream_advanced(
                        self.selected_video.file_path, target_clips
                    )
                else:
                    results = self.analyzer.analyze_long_livestream(
                        self.selected_video.file_path, target_clips
                    )

                results_count = len(results)
                output_files = [result.get('file_path', '') for result in results if result.get('file_path')]

                if results_count > 0:
                    success = True
                    self.window.after(0, lambda: progress_window['status_label'].config(
                        text=f"تم إنشاء {results_count} مقطع بنجاح! ✅"
                    ))
                else:
                    error_message = "لم يتم العثور على لقطات مناسبة"
                    self.window.after(0, lambda: progress_window['status_label'].config(
                        text="لم يتم العثور على لقطات مناسبة ❌"
                    ))

            except Exception as e:
                error_message = str(e)
                self.window.after(0, lambda: progress_window['status_label'].config(
                    text=f"خطأ في المعالجة: {e}"
                ))
                logger.error(f"خطأ في معالجة الفيديو: {e}")

            finally:
                # حساب وقت المعالجة
                processing_time = (datetime.now() - start_time).total_seconds()

                # حفظ سجل المعالجة
                self.history_manager.add_processing_record(
                    video_id=self.selected_video.id,
                    method=method,
                    target_clips=target_clips,
                    results_count=results_count,
                    processing_time=processing_time,
                    success=success,
                    output_files=output_files,
                    error_message=error_message
                )

                # تحديث الواجهة
                self.window.after(0, lambda: self.processing_completed(progress_window, success, results_count))

        # بدء المعالجة في thread منفصل
        threading.Thread(target=processing_thread, daemon=True).start()

    def create_progress_window(self) -> Dict[str, Any]:
        """إنشاء نافذة التقدم"""
        window = tk.Toplevel(self.window)
        window.title("معالجة الفيديو")
        window.geometry("400x200")
        window.transient(self.window)
        window.grab_set()
        window.resizable(False, False)

        # توسيط النافذة
        window.update_idletasks()
        x = (window.winfo_screenwidth() // 2) - (400 // 2)
        y = (window.winfo_screenheight() // 2) - (200 // 2)
        window.geometry(f"400x200+{x}+{y}")

        main_frame = ttk.Frame(window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # أيقونة ونص
        ttk.Label(main_frame, text="⚙️", font=("Arial", 24)).pack(pady=(0, 10))
        ttk.Label(main_frame, text="جاري معالجة الفيديو...", font=("Arial", 12, "bold")).pack(pady=(0, 10))

        # شريط تقدم
        progress_bar = ttk.Progressbar(main_frame, mode='indeterminate')
        progress_bar.pack(fill=tk.X, pady=(0, 10))
        progress_bar.start(10)

        # نص الحالة
        status_label = ttk.Label(main_frame, text="بدء المعالجة...")
        status_label.pack(pady=(0, 10))

        return {
            'window': window,
            'progress_bar': progress_bar,
            'status_label': status_label
        }

    def processing_completed(self, progress_window: Dict[str, Any], success: bool, results_count: int):
        """انتهاء المعالجة"""
        self.is_processing = False

        # إغلاق نافذة التقدم
        progress_window['window'].destroy()

        # تحديث البيانات
        self.refresh_video_list()
        if self.selected_video:
            # إعادة تحميل بيانات الفيديو
            updated_video = self.history_manager.get_video_by_id(self.selected_video.id)
            if updated_video:
                self.selected_video = updated_video
                self.update_details_panel()

        # عرض النتيجة
        if success:
            messagebox.showinfo("نجح", f"تم إنشاء {results_count} مقطع بنجاح! ✅")
        else:
            messagebox.showwarning("تحذير", "لم يتم العثور على لقطات مناسبة في هذا الفيديو")

    def delete_video(self):
        """حذف الفيديو المحدد"""
        if not self.selected_video:
            messagebox.showwarning("تحذير", "لم يتم اختيار فيديو")
            return

        # تأكيد الحذف
        response = messagebox.askyesnocancel(
            "تأكيد الحذف",
            f"هل تريد حذف الفيديو '{self.selected_video.title}'؟\n\n"
            "اختر:\n"
            "• نعم: حذف السجل والملفات\n"
            "• لا: حذف السجل فقط\n"
            "• إلغاء: عدم الحذف"
        )

        if response is None:  # إلغاء
            return

        try:
            delete_files = response  # True = حذف الملفات، False = حذف السجل فقط

            success = self.history_manager.delete_video_record(
                self.selected_video.id, delete_files=delete_files
            )

            if success:
                self.selected_video = None
                self.clear_details_panel()
                self.refresh_video_list()

                action = "وملفاته" if delete_files else "فقط"
                messagebox.showinfo("نجح", f"تم حذف سجل الفيديو {action} بنجاح")
            else:
                messagebox.showerror("خطأ", "فشل في حذف الفيديو")

        except Exception as e:
            logger.error(f"خطأ في حذف الفيديو: {e}")
            messagebox.showerror("خطأ", f"خطأ في حذف الفيديو: {e}")

    def show_statistics(self):
        """عرض إحصائيات الفيديوهات"""
        try:
            stats = self.history_manager.get_statistics()

            # إنشاء نافذة الإحصائيات
            stats_window = tk.Toplevel(self.window)
            stats_window.title("📊 إحصائيات الفيديوهات")
            stats_window.geometry("500x400")
            stats_window.transient(self.window)
            stats_window.grab_set()

            # توسيط النافذة
            stats_window.update_idletasks()
            x = (stats_window.winfo_screenwidth() // 2) - (500 // 2)
            y = (stats_window.winfo_screenheight() // 2) - (400 // 2)
            stats_window.geometry(f"500x400+{x}+{y}")

            main_frame = ttk.Frame(stats_window, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # عنوان
            ttk.Label(main_frame, text="📊 إحصائيات الفيديوهات",
                     font=("Arial", 16, "bold")).pack(pady=(0, 20))

            # الإحصائيات
            stats_text = f"""
📹 إجمالي الفيديوهات: {stats['total_videos']}
✅ الفيديوهات المعالجة: {stats['processed_videos']}
⚙️ محاولات المعالجة: {stats['total_processing_attempts']}
🎯 المعالجة الناجحة: {stats['successful_processing']}
📊 معدل النجاح: {stats['success_rate']:.1f}%

💾 إجمالي الحجم: {stats['total_size_mb']:.1f} MB
⏱️ إجمالي المدة: {stats['total_duration_hours']:.1f} ساعة
            """.strip()

            stats_label = ttk.Label(main_frame, text=stats_text, font=("Arial", 11))
            stats_label.pack(pady=(0, 20))

            # زر الإغلاق
            ttk.Button(main_frame, text="❌ إغلاق", command=stats_window.destroy).pack()

        except Exception as e:
            logger.error(f"خطأ في عرض الإحصائيات: {e}")
            messagebox.showerror("خطأ", f"خطأ في عرض الإحصائيات: {e}")

    def cleanup_missing_files(self):
        """تنظيف الملفات المفقودة"""
        try:
            # البحث عن الملفات المفقودة
            missing_videos = []
            for video in self.history_manager.get_all_videos():
                if not Path(video.file_path).exists():
                    missing_videos.append(video)

            if not missing_videos:
                messagebox.showinfo("معلومات", "جميع الملفات موجودة ✅")
                return

            # تأكيد التنظيف
            response = messagebox.askyesno(
                "تنظيف الملفات المفقودة",
                f"تم العثور على {len(missing_videos)} فيديو بملفات مفقودة.\n"
                "هل تريد حذف سجلاتها من قاعدة البيانات؟"
            )

            if not response:
                return

            # حذف السجلات
            deleted_count = 0
            for video in missing_videos:
                if self.history_manager.delete_video_record(video.id, delete_files=False):
                    deleted_count += 1

            # تحديث القائمة
            self.refresh_video_list()
            self.clear_details_panel()

            messagebox.showinfo("نجح", f"تم حذف {deleted_count} سجل للملفات المفقودة")

        except Exception as e:
            logger.error(f"خطأ في تنظيف الملفات المفقودة: {e}")
            messagebox.showerror("خطأ", f"خطأ في التنظيف: {e}")

    def on_closing(self):
        """عند إغلاق النافذة"""
        if self.is_processing:
            response = messagebox.askokcancel(
                "تأكيد الإغلاق",
                "معالجة جارية. هل تريد إغلاق النافذة؟\n"
                "ستستمر المعالجة في الخلفية."
            )
            if not response:
                return

        self.window.destroy()