#!/usr/bin/env python3
"""
تشغيل محلل البثوث المباشرة
Standalone launcher for the livestream analyzer
"""

import sys
import os
from pathlib import Path

# إضافة مجلد src إلى مسار Python
sys.path.insert(0, str(Path(__file__).parent / "src"))

def main():
    """تشغيل محلل البثوث المباشرة"""
    
    print("🎬 محلل البثوث المباشرة - Video Editor Pro")
    print("=" * 50)
    
    try:
        # التحقق من توفر المتطلبات الأساسية
        print("فحص المتطلبات...")
        
        # فحص Python
        if sys.version_info < (3, 8):
            print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
            return 1
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
        
        # فحص FFmpeg
        import subprocess
        try:
            result = subprocess.run(['ffmpeg', '-version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ FFmpeg متوفر")
            else:
                print("⚠️  تحذير: FFmpeg قد لا يعمل بشكل صحيح")
        except FileNotFoundError:
            print("❌ خطأ: FFmpeg غير مثبت")
            print("يرجى تثبيت FFmpeg من: https://ffmpeg.org/download.html")
            return 1
        
        # فحص المكتبات المطلوبة
        required_modules = ['pathlib', 'json', 'logging', 'threading']
        for module in required_modules:
            try:
                __import__(module)
                print(f"✅ {module}")
            except ImportError:
                print(f"❌ {module} غير متوفر")
                return 1
        
        print("\n" + "=" * 50)
        print("جميع المتطلبات متوفرة!")
        print("=" * 50)
        
        # محاولة تشغيل واجهة المستخدم
        try:
            import tkinter as tk
            print("تشغيل واجهة المستخدم...")
            
            # إنشاء النافذة الرئيسية
            root = tk.Tk()
            root.withdraw()  # إخفاء النافذة الرئيسية مؤقتاً
            
            # تشغيل محلل البثوث المباشرة مباشرة
            from gui.livestream_window import LivestreamAnalysisWindow
            
            # إظهار النافذة الرئيسية
            root.deiconify()
            root.title("Video Editor Pro")
            root.geometry("800x600")
            
            # فتح محلل البثوث المباشرة
            analyzer_window = LivestreamAnalysisWindow(root)
            
            print("✅ تم تشغيل محلل البثوث المباشرة بنجاح!")
            print("\nاستخدم النافذة لتحليل البثوث المباشرة.")
            print("أغلق النافذة للخروج من البرنامج.")
            
            # تشغيل حلقة الأحداث
            root.mainloop()
            
            return 0
            
        except ImportError as e:
            print(f"❌ خطأ في استيراد Tkinter: {e}")
            print("يبدو أن Tkinter غير متوفر في هذه البيئة")
            
            # تشغيل نسخة سطر الأوامر
            return run_command_line_version()
            
        except Exception as e:
            print(f"❌ خطأ في تشغيل واجهة المستخدم: {e}")
            return run_command_line_version()
    
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return 1

def run_command_line_version():
    """تشغيل نسخة سطر الأوامر"""
    
    print("\n" + "=" * 50)
    print("تشغيل نسخة سطر الأوامر")
    print("=" * 50)
    
    try:
        from ai.livestream_analyzer import LivestreamAnalyzer
        
        analyzer = LivestreamAnalyzer()
        
        print("محلل البثوث المباشرة جاهز!")
        print("\nلاستخدام المحلل:")
        print("1. ضع ملف الفيديو في نفس مجلد البرنامج")
        print("2. قم بتشغيل الأمر التالي:")
        print("   python -c \"from ai.livestream_analyzer import LivestreamAnalyzer; analyzer = LivestreamAnalyzer(); results = analyzer.analyze_long_livestream('your_video.mp4', 5); print('تم العثور على', len(results), 'مقطع')\"")
        
        # مثال تفاعلي بسيط
        video_path = input("\nأدخل مسار ملف الفيديو (أو اضغط Enter للتخطي): ").strip()
        
        if video_path and os.path.exists(video_path):
            print(f"تحليل الفيديو: {video_path}")
            
            try:
                results = analyzer.analyze_long_livestream(video_path, 3)
                
                if results:
                    print(f"\n✅ تم العثور على {len(results)} مقطع:")
                    for i, result in enumerate(results, 1):
                        print(f"{i}. {result.get('type', 'غير محدد')} - "
                              f"{result.get('duration', 0):.1f}s - "
                              f"ثقة: {result.get('confidence', 0)*100:.0f}%")
                        print(f"   الملف: {result.get('file_path', 'غير محدد')}")
                else:
                    print("❌ لم يتم العثور على مقاطع مثيرة")
                    
            except Exception as e:
                print(f"❌ خطأ في التحليل: {e}")
        else:
            print("تم تخطي التحليل")
        
        return 0
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل نسخة سطر الأوامر: {e}")
        return 1

def show_help():
    """عرض المساعدة"""
    
    help_text = """
🎬 محلل البثوث المباشرة - Video Editor Pro

الاستخدام:
  python run_livestream_analyzer.py          # تشغيل عادي
  python run_livestream_analyzer.py --help   # عرض المساعدة

الوصف:
  أداة متخصصة لتحليل البثوث المباشرة الطويلة واستخراج اللحظات الفيروسية
  
الميزات:
  - تحليل بثوث تصل لـ 10+ ساعات
  - استخراج اللحظات المثيرة/المضحكة/الصادمة
  - إنشاء مقاطع شورتس جاهزة للنشر
  - ترجمة تلقائية للمقاطع
  - عناوين وهاشتاغات ذكية

المتطلبات:
  - Python 3.8+
  - FFmpeg
  - اتصال إنترنت (للميزات المتقدمة)

الدعم:
  - الوثائق: docs/user_guide.md
  - المشاكل: GitHub Issues
  
مثال:
  python run_livestream_analyzer.py
  # ثم اختر ملف الفيديو من الواجهة
"""
    
    print(help_text)

if __name__ == "__main__":
    # فحص المعاملات
    if len(sys.argv) > 1:
        if sys.argv[1] in ['--help', '-h', 'help']:
            show_help()
            sys.exit(0)
        elif sys.argv[1] in ['--version', '-v']:
            print("Video Editor Pro - محلل البثوث المباشرة v1.0.0")
            sys.exit(0)
    
    # تشغيل البرنامج
    exit_code = main()
    sys.exit(exit_code)
