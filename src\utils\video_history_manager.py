"""
مدير تاريخ الفيديوهات والمقاطع المحملة
Video History Manager for Downloaded and Processed Videos
"""

import json
import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import hashlib

logger = logging.getLogger(__name__)

@dataclass
class VideoRecord:
    """سجل فيديو محمل أو معالج"""
    id: str
    title: str
    url: str
    file_path: str
    download_date: str
    file_size: int
    duration: float
    thumbnail_path: Optional[str] = None
    info_file_path: Optional[str] = None
    processed: bool = False
    processing_date: Optional[str] = None
    highlights_count: int = 0
    highlights_data: Optional[Dict[str, Any]] = None
    tags: List[str] = None
    notes: str = ""

    def __post_init__(self):
        if self.tags is None:
            self.tags = []

@dataclass
class ProcessingRecord:
    """سجل معالجة فيديو"""
    video_id: str
    processing_date: str
    method: str  # 'basic' or 'advanced'
    target_clips: int
    results_count: int
    processing_time: float
    success: bool
    error_message: Optional[str] = None
    output_files: List[str] = None
    settings: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        if self.output_files is None:
            self.output_files = []

class VideoHistoryManager:
    """مدير تاريخ الفيديوهات والمعالجة"""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        # ملفات البيانات
        self.videos_db_file = self.data_dir / "videos_history.json"
        self.processing_db_file = self.data_dir / "processing_history.json"
        self.thumbnails_dir = self.data_dir / "thumbnails"
        self.thumbnails_dir.mkdir(exist_ok=True)
        
        # تحميل البيانات
        self.videos_db = self._load_videos_db()
        self.processing_db = self._load_processing_db()
        
        logger.info(f"تم تهيئة مدير تاريخ الفيديوهات - {len(self.videos_db)} فيديو محفوظ")

    def _load_videos_db(self) -> Dict[str, VideoRecord]:
        """تحميل قاعدة بيانات الفيديوهات"""
        try:
            if self.videos_db_file.exists():
                with open(self.videos_db_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return {
                        video_id: VideoRecord(**video_data) 
                        for video_id, video_data in data.items()
                    }
            return {}
        except Exception as e:
            logger.error(f"خطأ في تحميل قاعدة بيانات الفيديوهات: {e}")
            return {}

    def _load_processing_db(self) -> List[ProcessingRecord]:
        """تحميل قاعدة بيانات المعالجة"""
        try:
            if self.processing_db_file.exists():
                with open(self.processing_db_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return [ProcessingRecord(**record) for record in data]
            return []
        except Exception as e:
            logger.error(f"خطأ في تحميل قاعدة بيانات المعالجة: {e}")
            return []

    def _save_videos_db(self):
        """حفظ قاعدة بيانات الفيديوهات"""
        try:
            data = {
                video_id: asdict(video_record) 
                for video_id, video_record in self.videos_db.items()
            }
            with open(self.videos_db_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"خطأ في حفظ قاعدة بيانات الفيديوهات: {e}")

    def _save_processing_db(self):
        """حفظ قاعدة بيانات المعالجة"""
        try:
            data = [asdict(record) for record in self.processing_db]
            with open(self.processing_db_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"خطأ في حفظ قاعدة بيانات المعالجة: {e}")

    def _generate_video_id(self, url: str, title: str) -> str:
        """إنشاء معرف فريد للفيديو"""
        content = f"{url}_{title}_{datetime.now().strftime('%Y%m%d')}"
        return hashlib.md5(content.encode()).hexdigest()[:12]

    def add_downloaded_video(self, title: str, url: str, file_path: str, 
                           duration: float, info_file_path: Optional[str] = None) -> str:
        """إضافة فيديو محمل جديد"""
        try:
            # التحقق من وجود الملف
            if not Path(file_path).exists():
                raise FileNotFoundError(f"ملف الفيديو غير موجود: {file_path}")
            
            # إنشاء معرف الفيديو
            video_id = self._generate_video_id(url, title)
            
            # حساب حجم الملف
            file_size = Path(file_path).stat().st_size
            
            # إنشاء سجل الفيديو
            video_record = VideoRecord(
                id=video_id,
                title=title,
                url=url,
                file_path=file_path,
                download_date=datetime.now().isoformat(),
                file_size=file_size,
                duration=duration,
                info_file_path=info_file_path
            )
            
            # حفظ في قاعدة البيانات
            self.videos_db[video_id] = video_record
            self._save_videos_db()
            
            logger.info(f"تم إضافة فيديو جديد: {title} (ID: {video_id})")
            return video_id
            
        except Exception as e:
            logger.error(f"خطأ في إضافة الفيديو المحمل: {e}")
            raise

    def add_processing_record(self, video_id: str, method: str, target_clips: int,
                            results_count: int, processing_time: float, success: bool,
                            output_files: List[str] = None, error_message: str = None,
                            highlights_data: Dict[str, Any] = None) -> None:
        """إضافة سجل معالجة"""
        try:
            # إنشاء سجل المعالجة
            processing_record = ProcessingRecord(
                video_id=video_id,
                processing_date=datetime.now().isoformat(),
                method=method,
                target_clips=target_clips,
                results_count=results_count,
                processing_time=processing_time,
                success=success,
                output_files=output_files or [],
                error_message=error_message
            )
            
            # إضافة إلى قاعدة البيانات
            self.processing_db.append(processing_record)
            self._save_processing_db()
            
            # تحديث سجل الفيديو
            if video_id in self.videos_db:
                video_record = self.videos_db[video_id]
                video_record.processed = success
                video_record.processing_date = processing_record.processing_date
                video_record.highlights_count = results_count
                video_record.highlights_data = highlights_data
                self._save_videos_db()
            
            logger.info(f"تم إضافة سجل معالجة للفيديو {video_id}")
            
        except Exception as e:
            logger.error(f"خطأ في إضافة سجل المعالجة: {e}")

    def get_all_videos(self) -> List[VideoRecord]:
        """الحصول على جميع الفيديوهات المحفوظة"""
        return list(self.videos_db.values())

    def get_downloaded_videos(self) -> List[VideoRecord]:
        """الحصول على الفيديوهات المحملة فقط"""
        return [video for video in self.videos_db.values() if Path(video.file_path).exists()]

    def get_processed_videos(self) -> List[VideoRecord]:
        """الحصول على الفيديوهات المعالجة"""
        return [video for video in self.videos_db.values() if video.processed]

    def get_video_by_id(self, video_id: str) -> Optional[VideoRecord]:
        """الحصول على فيديو بالمعرف"""
        return self.videos_db.get(video_id)

    def get_video_processing_history(self, video_id: str) -> List[ProcessingRecord]:
        """الحصول على تاريخ معالجة فيديو معين"""
        return [record for record in self.processing_db if record.video_id == video_id]

    def search_videos(self, query: str) -> List[VideoRecord]:
        """البحث في الفيديوهات"""
        query_lower = query.lower()
        results = []
        
        for video in self.videos_db.values():
            if (query_lower in video.title.lower() or 
                query_lower in video.url.lower() or
                any(query_lower in tag.lower() for tag in video.tags)):
                results.append(video)
        
        return results

    def delete_video_record(self, video_id: str, delete_files: bool = False) -> bool:
        """حذف سجل فيديو"""
        try:
            if video_id not in self.videos_db:
                return False
            
            video_record = self.videos_db[video_id]
            
            # حذف الملفات إذا طُلب ذلك
            if delete_files:
                if Path(video_record.file_path).exists():
                    Path(video_record.file_path).unlink()
                if video_record.info_file_path and Path(video_record.info_file_path).exists():
                    Path(video_record.info_file_path).unlink()
                if video_record.thumbnail_path and Path(video_record.thumbnail_path).exists():
                    Path(video_record.thumbnail_path).unlink()
            
            # حذف من قاعدة البيانات
            del self.videos_db[video_id]
            self._save_videos_db()
            
            # حذف سجلات المعالجة
            self.processing_db = [record for record in self.processing_db if record.video_id != video_id]
            self._save_processing_db()
            
            logger.info(f"تم حذف سجل الفيديو {video_id}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في حذف سجل الفيديو: {e}")
            return False

    def update_video_tags(self, video_id: str, tags: List[str]) -> bool:
        """تحديث علامات الفيديو"""
        try:
            if video_id in self.videos_db:
                self.videos_db[video_id].tags = tags
                self._save_videos_db()
                return True
            return False
        except Exception as e:
            logger.error(f"خطأ في تحديث علامات الفيديو: {e}")
            return False

    def update_video_notes(self, video_id: str, notes: str) -> bool:
        """تحديث ملاحظات الفيديو"""
        try:
            if video_id in self.videos_db:
                self.videos_db[video_id].notes = notes
                self._save_videos_db()
                return True
            return False
        except Exception as e:
            logger.error(f"خطأ في تحديث ملاحظات الفيديو: {e}")
            return False

    def get_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات"""
        total_videos = len(self.videos_db)
        processed_videos = len(self.get_processed_videos())
        total_processing_attempts = len(self.processing_db)
        successful_processing = len([r for r in self.processing_db if r.success])
        
        total_size = sum(video.file_size for video in self.videos_db.values())
        total_duration = sum(video.duration for video in self.videos_db.values())
        
        return {
            'total_videos': total_videos,
            'processed_videos': processed_videos,
            'total_processing_attempts': total_processing_attempts,
            'successful_processing': successful_processing,
            'total_size_mb': total_size / (1024 * 1024),
            'total_duration_hours': total_duration / 3600,
            'success_rate': (successful_processing / max(total_processing_attempts, 1)) * 100
        }
