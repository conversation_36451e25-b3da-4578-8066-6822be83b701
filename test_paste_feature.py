#!/usr/bin/env python3
"""
اختبار ميزة لصق الرابط الجديدة
Test the new paste URL feature
"""

import sys
import os
from pathlib import Path
import tkinter as tk

# إضافة مجلد src إلى مسار Python
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_paste_feature():
    """اختبار ميزة لصق الرابط"""
    
    print("📋 اختبار ميزة لصق الرابط")
    print("=" * 40)
    
    # إنشاء نافذة تجريبية
    root = tk.Tk()
    root.title("اختبار ميزة اللصق")
    root.geometry("600x200")
    
    # متغير للنص
    text_var = tk.StringVar()
    
    # حقل النص
    entry = tk.Entry(root, textvariable=text_var, width=80)
    entry.pack(pady=20, padx=20, fill=tk.X)
    
    # تعيين رابط تجريبي في الحافظة
    test_url = "https://www.youtube.com/live/2GLBi1yhN8o?si=WrFbzhKsXdpJaHp_"
    root.clipboard_clear()
    root.clipboard_append(test_url)
    
    print(f"✅ تم وضع الرابط في الحافظة: {test_url}")
    
    # وظيفة لصق
    def paste_from_clipboard():
        try:
            clipboard_content = root.clipboard_get()
            text_var.set(clipboard_content)
            result_label.config(text=f"✅ تم اللصق: {clipboard_content[:50]}...")
            print(f"✅ تم لصق الرابط بنجاح")
        except Exception as e:
            result_label.config(text=f"❌ خطأ: {e}")
            print(f"❌ خطأ في اللصق: {e}")
    
    # زر اللصق
    paste_button = tk.Button(root, text="📋 لصق", command=paste_from_clipboard)
    paste_button.pack(pady=10)
    
    # تسمية النتيجة
    result_label = tk.Label(root, text="اضغط 'لصق' لاختبار الميزة", wraplength=500)
    result_label.pack(pady=10)
    
    # تعليمات
    instructions = tk.Label(root, 
                           text="التعليمات:\n1. اضغط 'لصق' لاختبار اللصق من الزر\n2. اضغط Ctrl+V في الحقل لاختبار الاختصار\n3. أغلق النافذة عند الانتهاء",
                           justify=tk.LEFT, wraplength=500)
    instructions.pack(pady=10)
    
    # ربط Ctrl+V
    def on_ctrl_v(event):
        paste_from_clipboard()
        return "break"
    
    entry.bind("<Control-v>", on_ctrl_v)
    
    print("\n📋 نافذة الاختبار مفتوحة:")
    print("1. اضغط 'لصق' لاختبار اللصق من الزر")
    print("2. اضغط Ctrl+V في الحقل لاختبار الاختصار")
    print("3. أغلق النافذة عند الانتهاء")
    
    # تشغيل النافذة
    root.mainloop()
    
    return True

def test_url_validation():
    """اختبار التحقق من صحة الروابط"""
    
    print("\n🔗 اختبار التحقق من صحة الروابط")
    print("=" * 40)
    
    try:
        from utils.youtube_downloader import YouTubeDownloader
        
        downloader = YouTubeDownloader()
        
        # روابط للاختبار
        test_urls = [
            ("https://www.youtube.com/live/2GLBi1yhN8o?si=WrFbzhKsXdpJaHp_", True),
            ("https://www.youtube.com/watch?v=dQw4w9WgXcQ", True),
            ("https://youtu.be/dQw4w9WgXcQ", True),
            ("https://www.youtube.com/embed/dQw4w9WgXcQ", True),
            ("https://m.youtube.com/watch?v=dQw4w9WgXcQ", True),
            ("https://www.google.com", False),
            ("not a url", False),
            ("", False),
        ]
        
        print("اختبار التحقق من صحة الروابط:")
        
        all_passed = True
        for url, expected in test_urls:
            result = downloader.is_youtube_url(url)
            status = "✅" if result == expected else "❌"
            
            if result != expected:
                all_passed = False
            
            url_display = url if len(url) <= 50 else url[:47] + "..."
            print(f"{status} {url_display} -> {result} (متوقع: {expected})")
        
        if all_passed:
            print("\n✅ جميع اختبارات التحقق نجحت!")
        else:
            print("\n❌ بعض اختبارات التحقق فشلت!")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التحقق: {e}")
        return False

def test_video_id_extraction():
    """اختبار استخراج معرف الفيديو"""
    
    print("\n🆔 اختبار استخراج معرف الفيديو")
    print("=" * 40)
    
    # محاكاة وظيفة استخراج معرف الفيديو
    import re
    
    def extract_video_id(url: str):
        patterns = [
            r'(?:youtube\.com/watch\?v=|youtu\.be/|youtube\.com/live/)([a-zA-Z0-9_-]{11})',
            r'youtube\.com/embed/([a-zA-Z0-9_-]{11})',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        return None
    
    # روابط للاختبار
    test_cases = [
        ("https://www.youtube.com/live/2GLBi1yhN8o?si=WrFbzhKsXdpJaHp_", "2GLBi1yhN8o"),
        ("https://www.youtube.com/watch?v=dQw4w9WgXcQ", "dQw4w9WgXcQ"),
        ("https://youtu.be/dQw4w9WgXcQ", "dQw4w9WgXcQ"),
        ("https://www.youtube.com/embed/dQw4w9WgXcQ", "dQw4w9WgXcQ"),
        ("https://www.google.com", None),
    ]
    
    print("اختبار استخراج معرف الفيديو:")
    
    all_passed = True
    for url, expected in test_cases:
        result = extract_video_id(url)
        status = "✅" if result == expected else "❌"
        
        if result != expected:
            all_passed = False
        
        url_display = url if len(url) <= 50 else url[:47] + "..."
        print(f"{status} {url_display} -> {result} (متوقع: {expected})")
    
    if all_passed:
        print("\n✅ جميع اختبارات استخراج المعرف نجحت!")
    else:
        print("\n❌ بعض اختبارات استخراج المعرف فشلت!")
    
    return all_passed

def main():
    """الدالة الرئيسية"""
    
    print("🧪 اختبار شامل لميزة لصق الرابط")
    print("=" * 60)
    
    # اختبار التحقق من الروابط
    validation_test = test_url_validation()
    
    # اختبار استخراج معرف الفيديو
    extraction_test = test_video_id_extraction()
    
    # اختبار ميزة اللصق (تفاعلي)
    print("\n" + "=" * 60)
    print("🎯 اختبار تفاعلي لميزة اللصق")
    print("=" * 60)
    
    response = input("هل تريد اختبار ميزة اللصق تفاعلياً؟ (y/n): ")
    
    paste_test = True
    if response.lower() in ['y', 'yes', 'نعم', 'ن']:
        paste_test = test_paste_feature()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("🎯 النتيجة النهائية")
    print("=" * 60)
    
    results = {
        "التحقق من الروابط": validation_test,
        "استخراج معرف الفيديو": extraction_test,
        "ميزة اللصق": paste_test
    }
    
    working_features = sum(results.values())
    total_features = len(results)
    
    for feature, status in results.items():
        status_icon = "✅" if status else "❌"
        print(f"{status_icon} {feature}: {'يعمل' if status else 'لا يعمل'}")
    
    print(f"\nالنتيجة: {working_features}/{total_features} ميزات تعمل")
    
    if working_features == total_features:
        print("\n🎉 جميع ميزات اللصق تعمل بنجاح!")
        print("\nالميزات الجديدة:")
        print("✅ زر لصق الرابط")
        print("✅ اختصار Ctrl+V للصق")
        print("✅ التحقق التلقائي من صحة الرابط")
        print("✅ استخراج معرف الفيديو للمعاينة")
        print("✅ تلميحات مفيدة للمستخدم")
        
        print("\nيمكنك الآن:")
        print("1. تشغيل محلل البثوث: python run_livestream_analyzer.py")
        print("2. اختيار 'رابط YouTube'")
        print("3. استخدام زر 'لصق' أو Ctrl+V")
        print("4. تحميل وتحليل الفيديو!")
        
        return 0
    else:
        print("\n⚠️  بعض الميزات تحتاج إصلاح")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
