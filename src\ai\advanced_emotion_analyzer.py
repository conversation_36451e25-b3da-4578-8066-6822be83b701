"""
محلل المشاعر المتقدم
Advanced Emotion Analyzer

يدمج تقنيات متقدمة من البرومت المقترح لتحليل أعمق للمشاعر وردات الفعل
"""

import cv2
import numpy as np
import logging
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
import librosa
import speech_recognition as sr
from pathlib import Path

# محاولة استيراد المكتبات المتقدمة
try:
    from deepface import DeepFace
    DEEPFACE_AVAILABLE = True
except ImportError:
    DEEPFACE_AVAILABLE = False
    logging.warning("DeepFace غير متوفر - سيتم استخدام التحليل الأساسي")

try:
    import feat
    PYFEAT_AVAILABLE = True
except ImportError:
    PYFEAT_AVAILABLE = False
    logging.warning("Py-Feat غير متوفر - سيتم استخدام التحليل الأساسي")

logger = logging.getLogger(__name__)

@dataclass
class AdvancedEmotionResult:
    """نتيجة تحليل المشاعر المتقدم"""
    timestamp: float
    
    # تحليل الوجه المتقدم
    basic_emotions: Dict[str, float]  # المشاعر الأساسية
    action_units: Dict[str, float]    # وحدات العمل
    facial_intensity: float           # شدة التعبير
    
    # تحليل صوتي متقدم
    vocal_emotions: Dict[str, float]  # المشاعر الصوتية
    pitch_changes: float              # تغيرات النبرة
    volume_changes: float             # تغيرات الصوت
    speech_rate: float                # معدل الكلام
    
    # تحليل متكامل
    overall_intensity: float          # الشدة الإجمالية
    dominant_emotion: str             # المشاعر السائدة
    confidence: float                 # مستوى الثقة

class AdvancedEmotionAnalyzer:
    """محلل المشاعر المتقدم"""
    
    def __init__(self):
        self.recognizer = sr.Recognizer()
        
        # إعداد Py-Feat إذا كان متوفراً
        if PYFEAT_AVAILABLE:
            try:
                self.feat_detector = feat.Detector(
                    face_model="retinaface",
                    landmark_model="mobilefacenet", 
                    au_model="xgb",
                    emotion_model="resmasknet",
                    facepose_model="img2pose"
                )
                logger.info("تم تهيئة Py-Feat بنجاح")
            except Exception as e:
                logger.warning(f"فشل في تهيئة Py-Feat: {e}")
                self.feat_detector = None
        else:
            self.feat_detector = None
        
        # قاموس وحدات العمل
        self.action_units = {
            'AU01': 'رفع الحاجب الداخلي',
            'AU02': 'رفع الحاجب الخارجي',
            'AU04': 'خفض الحاجب',
            'AU05': 'رفع الجفن العلوي',
            'AU06': 'رفع الخد',
            'AU07': 'شد الجفن',
            'AU09': 'تجعد الأنف',
            'AU10': 'رفع الشفة العلوية',
            'AU12': 'سحب زاوية الشفة',
            'AU14': 'تجويف الخد',
            'AU15': 'خفض زاوية الشفة',
            'AU17': 'رفع الذقن',
            'AU20': 'شد الشفة',
            'AU23': 'شد الشفة',
            'AU25': 'فصل الشفتين',
            'AU26': 'خفض الفك',
            'AU45': 'رمش العين'
        }
        
        # كلمات مفتاحية للمشاعر
        self.emotion_keywords = {
            'positive': ['ممتاز', 'رائع', 'أحسنت', 'جيد', 'برافو', 'واو', 'مذهل'],
            'negative': ['خطأ', 'لا', 'توقف', 'سيء', 'فظيع', 'كارثة'],
            'excitement': ['يالله', 'هيا', 'أسرع', 'قوي', 'أكثر', 'عظيم'],
            'surprise': ['ماذا', 'لا أصدق', 'مستحيل', 'يا إلهي', 'لا يمكن']
        }
        
        logger.info("تم تهيئة محلل المشاعر المتقدم")

    def analyze_frame_advanced(self, frame: np.ndarray, audio_segment: np.ndarray, 
                              timestamp: float) -> AdvancedEmotionResult:
        """تحليل متقدم لإطار واحد مع الصوت"""
        try:
            # تحليل الوجه المتقدم
            facial_analysis = self._analyze_face_advanced(frame)
            
            # تحليل صوتي متقدم
            audio_analysis = self._analyze_audio_advanced(audio_segment)
            
            # دمج النتائج
            result = self._combine_analysis(facial_analysis, audio_analysis, timestamp)
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في التحليل المتقدم: {e}")
            return self._create_empty_result(timestamp)

    def _analyze_face_advanced(self, frame: np.ndarray) -> Dict[str, Any]:
        """تحليل متقدم للوجه"""
        try:
            analysis = {
                'basic_emotions': {},
                'action_units': {},
                'facial_intensity': 0.0
            }
            
            # استخدام DeepFace إذا كان متوفراً
            if DEEPFACE_AVAILABLE:
                try:
                    deepface_result = DeepFace.analyze(
                        frame, 
                        actions=['emotion'],
                        enforce_detection=False,
                        silent=True
                    )
                    
                    if isinstance(deepface_result, list):
                        deepface_result = deepface_result[0]
                    
                    emotions = deepface_result.get('emotion', {})
                    # تحويل أسماء المشاعر للعربية
                    emotion_mapping = {
                        'angry': 'غضب',
                        'disgust': 'اشمئزاز', 
                        'fear': 'خوف',
                        'happy': 'سعادة',
                        'sad': 'حزن',
                        'surprise': 'مفاجأة',
                        'neutral': 'محايد'
                    }
                    
                    for eng_emotion, value in emotions.items():
                        ar_emotion = emotion_mapping.get(eng_emotion, eng_emotion)
                        analysis['basic_emotions'][ar_emotion] = value / 100.0
                        
                except Exception as e:
                    logger.warning(f"خطأ في DeepFace: {e}")
            
            # استخدام Py-Feat إذا كان متوفراً
            if PYFEAT_AVAILABLE and self.feat_detector:
                try:
                    feat_result = self.feat_detector.detect_image(frame)
                    
                    if not feat_result.empty:
                        # استخراج وحدات العمل
                        au_columns = [col for col in feat_result.columns if col.startswith('AU')]
                        for au_col in au_columns:
                            au_value = feat_result[au_col].iloc[0]
                            if not np.isnan(au_value):
                                analysis['action_units'][au_col] = float(au_value)
                        
                        # حساب شدة التعبير من وحدات العمل
                        if analysis['action_units']:
                            analysis['facial_intensity'] = np.mean(list(analysis['action_units'].values()))
                            
                except Exception as e:
                    logger.warning(f"خطأ في Py-Feat: {e}")
            
            return analysis
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الوجه المتقدم: {e}")
            return {'basic_emotions': {}, 'action_units': {}, 'facial_intensity': 0.0}

    def _analyze_audio_advanced(self, audio_segment: np.ndarray, sr_rate: int = 22050) -> Dict[str, Any]:
        """تحليل صوتي متقدم"""
        try:
            analysis = {
                'vocal_emotions': {},
                'pitch_changes': 0.0,
                'volume_changes': 0.0,
                'speech_rate': 0.0
            }
            
            if audio_segment is None or len(audio_segment) == 0:
                return analysis
            
            # تحليل النبرة
            try:
                pitches, magnitudes = librosa.piptrack(y=audio_segment, sr=sr_rate)
                pitch_values = []
                for t in range(pitches.shape[1]):
                    index = magnitudes[:, t].argmax()
                    pitch = pitches[index, t]
                    if pitch > 0:
                        pitch_values.append(pitch)
                
                if len(pitch_values) > 1:
                    analysis['pitch_changes'] = np.std(pitch_values) / np.mean(pitch_values)
                    
            except Exception as e:
                logger.warning(f"خطأ في تحليل النبرة: {e}")
            
            # تحليل مستوى الصوت
            try:
                rms = librosa.feature.rms(y=audio_segment)[0]
                if len(rms) > 1:
                    analysis['volume_changes'] = np.std(rms) / np.mean(rms)
                    
            except Exception as e:
                logger.warning(f"خطأ في تحليل الصوت: {e}")
            
            # تحليل معدل الكلام (تقريبي)
            try:
                tempo, _ = librosa.beat.beat_track(y=audio_segment, sr=sr_rate)
                analysis['speech_rate'] = float(tempo) / 120.0  # تطبيع
                
            except Exception as e:
                logger.warning(f"خطأ في تحليل معدل الكلام: {e}")
            
            # تحليل المشاعر الصوتية (بسيط)
            try:
                # استخدام ميزات صوتية لتقدير المشاعر
                spectral_centroid = librosa.feature.spectral_centroid(y=audio_segment, sr=sr_rate)[0]
                zero_crossing_rate = librosa.feature.zero_crossing_rate(audio_segment)[0]
                
                if len(spectral_centroid) > 0 and len(zero_crossing_rate) > 0:
                    # تقدير بسيط للمشاعر من الميزات الصوتية
                    energy = np.mean(rms) if len(rms) > 0 else 0
                    brightness = np.mean(spectral_centroid)
                    activity = np.mean(zero_crossing_rate)
                    
                    # تحويل إلى مشاعر تقريبية
                    if energy > 0.1 and brightness > 2000:
                        analysis['vocal_emotions']['إثارة'] = min(energy * 5, 1.0)
                    if activity > 0.1:
                        analysis['vocal_emotions']['نشاط'] = min(activity * 10, 1.0)
                    if energy < 0.05:
                        analysis['vocal_emotions']['هدوء'] = 1.0 - energy * 20
                        
            except Exception as e:
                logger.warning(f"خطأ في تحليل المشاعر الصوتية: {e}")
            
            return analysis
            
        except Exception as e:
            logger.error(f"خطأ في التحليل الصوتي المتقدم: {e}")
            return {'vocal_emotions': {}, 'pitch_changes': 0.0, 'volume_changes': 0.0, 'speech_rate': 0.0}

    def _combine_analysis(self, facial_analysis: Dict[str, Any], 
                         audio_analysis: Dict[str, Any], timestamp: float) -> AdvancedEmotionResult:
        """دمج نتائج التحليل"""
        try:
            # حساب الشدة الإجمالية
            facial_intensity = facial_analysis.get('facial_intensity', 0.0)
            audio_intensity = (
                audio_analysis.get('pitch_changes', 0.0) * 0.4 +
                audio_analysis.get('volume_changes', 0.0) * 0.4 +
                audio_analysis.get('speech_rate', 0.0) * 0.2
            )
            
            overall_intensity = (facial_intensity * 0.7 + audio_intensity * 0.3)
            
            # تحديد المشاعر السائدة
            all_emotions = {}
            all_emotions.update(facial_analysis.get('basic_emotions', {}))
            all_emotions.update(audio_analysis.get('vocal_emotions', {}))
            
            dominant_emotion = 'محايد'
            max_confidence = 0.0
            
            if all_emotions:
                dominant_emotion = max(all_emotions.items(), key=lambda x: x[1])[0]
                max_confidence = max(all_emotions.values())
            
            # حساب الثقة الإجمالية
            confidence = (max_confidence + overall_intensity) / 2.0
            
            return AdvancedEmotionResult(
                timestamp=timestamp,
                basic_emotions=facial_analysis.get('basic_emotions', {}),
                action_units=facial_analysis.get('action_units', {}),
                facial_intensity=facial_intensity,
                vocal_emotions=audio_analysis.get('vocal_emotions', {}),
                pitch_changes=audio_analysis.get('pitch_changes', 0.0),
                volume_changes=audio_analysis.get('volume_changes', 0.0),
                speech_rate=audio_analysis.get('speech_rate', 0.0),
                overall_intensity=overall_intensity,
                dominant_emotion=dominant_emotion,
                confidence=confidence
            )
            
        except Exception as e:
            logger.error(f"خطأ في دمج التحليل: {e}")
            return self._create_empty_result(timestamp)

    def _create_empty_result(self, timestamp: float) -> AdvancedEmotionResult:
        """إنشاء نتيجة فارغة"""
        return AdvancedEmotionResult(
            timestamp=timestamp,
            basic_emotions={},
            action_units={},
            facial_intensity=0.0,
            vocal_emotions={},
            pitch_changes=0.0,
            volume_changes=0.0,
            speech_rate=0.0,
            overall_intensity=0.0,
            dominant_emotion='محايد',
            confidence=0.0
        )

    def analyze_video_advanced(self, video_path: str, audio_path: str = None) -> List[AdvancedEmotionResult]:
        """تحليل متقدم لفيديو كامل"""
        try:
            results = []
            
            # فتح الفيديو
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            # تحميل الصوت إذا كان متوفراً
            audio_data = None
            if audio_path and Path(audio_path).exists():
                try:
                    audio_data, sr_rate = librosa.load(audio_path, sr=22050)
                except Exception as e:
                    logger.warning(f"فشل في تحميل الصوت: {e}")
            
            frame_count = 0
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                timestamp = frame_count / fps
                
                # استخراج قطعة صوتية مطابقة للإطار
                audio_segment = None
                if audio_data is not None:
                    start_sample = int(timestamp * sr_rate)
                    end_sample = int((timestamp + 1/fps) * sr_rate)
                    if start_sample < len(audio_data):
                        audio_segment = audio_data[start_sample:end_sample]
                
                # تحليل الإطار
                result = self.analyze_frame_advanced(frame, audio_segment, timestamp)
                results.append(result)
                
                frame_count += 1
                
                # معالجة كل 5 إطارات لتوفير الوقت
                if frame_count % 5 != 0:
                    continue
            
            cap.release()
            logger.info(f"تم تحليل {len(results)} إطار بالتحليل المتقدم")
            return results
            
        except Exception as e:
            logger.error(f"خطأ في التحليل المتقدم للفيديو: {e}")
            return []
