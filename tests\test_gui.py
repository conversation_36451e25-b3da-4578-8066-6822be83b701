"""
اختبارات واجهة المستخدم الرسومية
GUI unit tests
"""

import unittest
import tkinter as tk
from unittest.mock import Mock, patch, MagicMock
import sys
from pathlib import Path

# إضافة مجلد src إلى مسار Python
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from gui.main_window import VideoEditorApp
from gui.dialogs import TrimDialog, TextDialog, ConvertDialog
from utils.validators import VideoValidator, TextValidator, ParameterValidator

class TestVideoEditorApp(unittest.TestCase):
    """اختبارات التطبيق الرئيسي"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.root = tk.Tk()
        self.root.withdraw()  # إخفاء النافذة أثناء الاختبار
        
        with patch('gui.main_window.FileManager'), \
             patch('gui.main_window.VideoValidator'):
            self.app = VideoEditorApp(self.root)
    
    def tearDown(self):
        """تنظيف بعد الاختبارات"""
        self.root.destroy()
    
    def test_app_initialization(self):
        """اختبار تهيئة التطبيق"""
        self.assertIsNotNone(self.app)
        self.assertEqual(self.app.root, self.root)
        self.assertIsNone(self.app.current_video_path)
        self.assertIsNone(self.app.video_info)
    
    def test_window_setup(self):
        """اختبار إعداد النافذة"""
        # التحقق من عنوان النافذة
        title = self.root.title()
        self.assertIn("Video Editor Pro", title)
        
        # التحقق من الحد الأدنى لحجم النافذة
        min_width = self.root.minsize()[0]
        min_height = self.root.minsize()[1]
        self.assertGreater(min_width, 0)
        self.assertGreater(min_height, 0)
    
    def test_update_status(self):
        """اختبار تحديث شريط الحالة"""
        test_message = "رسالة اختبار"
        self.app.update_status(test_message)
        
        # التحقق من تحديث النص
        status_text = self.app.status_label.cget("text")
        self.assertEqual(status_text, test_message)
    
    @patch('tkinter.filedialog.askopenfilename')
    @patch('gui.main_window.VideoValidator')
    def test_open_video_success(self, mock_validator, mock_filedialog):
        """اختبار فتح فيديو بنجاح"""
        # محاكاة اختيار ملف
        mock_filedialog.return_value = "/path/to/video.mp4"
        
        # محاكاة التحقق الناجح
        mock_validator_instance = Mock()
        mock_validator_instance.validate_video_file.return_value = (True, "ملف صحيح")
        mock_validator.return_value = mock_validator_instance
        self.app.video_validator = mock_validator_instance
        
        # محاكاة تحميل معلومات الفيديو
        with patch.object(self.app, 'load_video_info') as mock_load_info:
            self.app.open_video()
            
            # التحقق من تعيين مسار الفيديو
            self.assertEqual(self.app.current_video_path, "/path/to/video.mp4")
            
            # التحقق من استدعاء تحميل المعلومات
            mock_load_info.assert_called_once()
    
    @patch('tkinter.filedialog.askopenfilename')
    @patch('tkinter.messagebox.showerror')
    def test_open_video_invalid_file(self, mock_messagebox, mock_filedialog):
        """اختبار فتح ملف فيديو غير صحيح"""
        # محاكاة اختيار ملف
        mock_filedialog.return_value = "/path/to/invalid.txt"
        
        # محاكاة التحقق الفاشل
        self.app.video_validator.validate_video_file.return_value = (False, "ملف غير صحيح")
        
        self.app.open_video()
        
        # التحقق من عدم تعيين مسار الفيديو
        self.assertIsNone(self.app.current_video_path)
        
        # التحقق من عرض رسالة خطأ
        mock_messagebox.assert_called_once()
    
    @patch('tkinter.filedialog.askopenfilename')
    def test_open_video_cancelled(self, mock_filedialog):
        """اختبار إلغاء فتح الفيديو"""
        # محاكاة إلغاء اختيار الملف
        mock_filedialog.return_value = ""
        
        self.app.open_video()
        
        # التحقق من عدم تغيير الحالة
        self.assertIsNone(self.app.current_video_path)
    
    @patch('tkinter.messagebox.showwarning')
    def test_trim_video_no_file(self, mock_messagebox):
        """اختبار قص الفيديو بدون ملف محمل"""
        self.app.trim_video()
        
        # التحقق من عرض رسالة تحذير
        mock_messagebox.assert_called_once()
    
    @patch('tkinter.messagebox.showwarning')
    def test_add_text_no_file(self, mock_messagebox):
        """اختبار إضافة نص بدون ملف محمل"""
        self.app.add_text()
        
        # التحقق من عرض رسالة تحذير
        mock_messagebox.assert_called_once()

class TestDialogs(unittest.TestCase):
    """اختبارات نوافذ الحوار"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.root = tk.Tk()
        self.root.withdraw()  # إخفاء النافذة أثناء الاختبار
    
    def tearDown(self):
        """تنظيف بعد الاختبارات"""
        self.root.destroy()
    
    def test_trim_dialog_initialization(self):
        """اختبار تهيئة نافذة قص الفيديو"""
        # محاكاة النافذة بدون عرضها فعلياً
        with patch('tkinter.Toplevel.wait_window'):
            dialog = TrimDialog.__new__(TrimDialog)
            dialog.parent = self.root
            dialog.result = None
            dialog.video_duration = 120.0
            
            # اختبار التحقق من صحة المدخلات
            dialog.start_var = tk.DoubleVar(value=10.0)
            dialog.end_var = tk.DoubleVar(value=20.0)
            dialog.validator = VideoValidator()
            
            # يجب أن يكون التحقق ناجحاً
            is_valid = dialog.validate_input()
            self.assertTrue(is_valid)
    
    def test_trim_dialog_invalid_times(self):
        """اختبار نافذة قص الفيديو بأوقات غير صحيحة"""
        with patch('tkinter.Toplevel.wait_window'), \
             patch('tkinter.messagebox.showerror') as mock_error:
            
            dialog = TrimDialog.__new__(TrimDialog)
            dialog.parent = self.root
            dialog.result = None
            dialog.video_duration = 120.0
            
            # أوقات غير صحيحة (البداية أكبر من النهاية)
            dialog.start_var = tk.DoubleVar(value=30.0)
            dialog.end_var = tk.DoubleVar(value=20.0)
            dialog.validator = VideoValidator()
            
            # يجب أن يفشل التحقق
            is_valid = dialog.validate_input()
            self.assertFalse(is_valid)
            
            # يجب عرض رسالة خطأ
            mock_error.assert_called_once()
    
    def test_text_dialog_validation(self):
        """اختبار التحقق من صحة نافذة النص"""
        with patch('tkinter.Toplevel.wait_window'):
            dialog = TextDialog.__new__(TextDialog)
            dialog.parent = self.root
            dialog.result = None
            
            # إعداد المتغيرات
            dialog.text_var = tk.StringVar(value="نص تجريبي")
            dialog.position_var = tk.StringVar(value="center")
            dialog.font_size_var = tk.IntVar(value=24)
            dialog.color_var = tk.StringVar(value="white")
            
            dialog.validator = TextValidator()
            dialog.param_validator = ParameterValidator()
            
            # يجب أن يكون التحقق ناجحاً
            is_valid = dialog.validate_input()
            self.assertTrue(is_valid)
    
    def test_text_dialog_empty_text(self):
        """اختبار نافذة النص مع نص فارغ"""
        with patch('tkinter.Toplevel.wait_window'), \
             patch('tkinter.messagebox.showerror') as mock_error:
            
            dialog = TextDialog.__new__(TextDialog)
            dialog.parent = self.root
            dialog.result = None
            
            # نص فارغ
            dialog.text_var = tk.StringVar(value="")
            dialog.position_var = tk.StringVar(value="center")
            dialog.font_size_var = tk.IntVar(value=24)
            dialog.color_var = tk.StringVar(value="white")
            
            dialog.validator = TextValidator()
            dialog.param_validator = ParameterValidator()
            
            # يجب أن يفشل التحقق
            is_valid = dialog.validate_input()
            self.assertFalse(is_valid)
            
            # يجب عرض رسالة خطأ
            mock_error.assert_called_once()

class TestValidators(unittest.TestCase):
    """اختبارات أدوات التحقق"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.video_validator = VideoValidator()
        self.text_validator = TextValidator()
        self.param_validator = ParameterValidator()
    
    def test_video_validator_valid_time_range(self):
        """اختبار التحقق من نطاق زمني صحيح"""
        is_valid, message = self.video_validator.validate_time_range(10.0, 20.0, 120.0)
        self.assertTrue(is_valid)
        self.assertEqual(message, "نطاق زمني صحيح")
    
    def test_video_validator_invalid_time_range(self):
        """اختبار التحقق من نطاق زمني غير صحيح"""
        # وقت البداية أكبر من النهاية
        is_valid, message = self.video_validator.validate_time_range(30.0, 20.0, 120.0)
        self.assertFalse(is_valid)
        self.assertIn("وقت النهاية يجب أن يكون أكبر", message)
    
    def test_video_validator_negative_start_time(self):
        """اختبار وقت بداية سالب"""
        is_valid, message = self.video_validator.validate_time_range(-5.0, 20.0, 120.0)
        self.assertFalse(is_valid)
        self.assertIn("وقت البداية لا يمكن أن يكون سالباً", message)
    
    def test_text_validator_valid_text(self):
        """اختبار التحقق من نص صحيح"""
        is_valid, message = self.text_validator.validate_text_overlay("نص تجريبي")
        self.assertTrue(is_valid)
        self.assertEqual(message, "نص صحيح")
    
    def test_text_validator_empty_text(self):
        """اختبار التحقق من نص فارغ"""
        is_valid, message = self.text_validator.validate_text_overlay("")
        self.assertFalse(is_valid)
        self.assertEqual(message, "النص فارغ")
    
    def test_text_validator_long_text(self):
        """اختبار نص طويل جداً"""
        long_text = "نص طويل جداً " * 100  # نص طويل
        is_valid, message = self.text_validator.validate_text_overlay(long_text)
        self.assertFalse(is_valid)
        self.assertIn("النص طويل جداً", message)
    
    def test_text_validator_sanitize_text(self):
        """اختبار تنظيف النص"""
        dirty_text = "نص مع 'علامات' \"اقتباس\" و\\شرطة"
        clean_text = self.text_validator.sanitize_text(dirty_text)
        
        # يجب إزالة أو استبدال الأحرف المشكلة
        self.assertNotIn("'", clean_text)
        self.assertNotIn('"', clean_text)
    
    def test_parameter_validator_valid_resolution(self):
        """اختبار التحقق من دقة صحيحة"""
        is_valid, message = self.param_validator.validate_resolution(1920, 1080)
        self.assertTrue(is_valid)
        self.assertEqual(message, "دقة صحيحة")
    
    def test_parameter_validator_invalid_resolution(self):
        """اختبار التحقق من دقة غير صحيحة"""
        # دقة سالبة
        is_valid, message = self.param_validator.validate_resolution(-1920, 1080)
        self.assertFalse(is_valid)
        self.assertIn("أبعاد الدقة يجب أن تكون أكبر من صفر", message)
    
    def test_parameter_validator_odd_resolution(self):
        """اختبار دقة بأرقام فردية"""
        is_valid, message = self.param_validator.validate_resolution(1921, 1081)
        self.assertFalse(is_valid)
        self.assertIn("أبعاد الدقة يجب أن تكون أرقام زوجية", message)
    
    def test_parameter_validator_valid_font_size(self):
        """اختبار حجم خط صحيح"""
        is_valid, message = self.param_validator.validate_font_size(24)
        self.assertTrue(is_valid)
        self.assertEqual(message, "حجم خط صحيح")
    
    def test_parameter_validator_invalid_font_size(self):
        """اختبار حجم خط غير صحيح"""
        # حجم خط سالب
        is_valid, message = self.param_validator.validate_font_size(-10)
        self.assertFalse(is_valid)
        self.assertIn("حجم الخط يجب أن يكون أكبر من صفر", message)
    
    def test_parameter_validator_valid_color(self):
        """اختبار لون صحيح"""
        # لون أساسي
        is_valid, message = self.param_validator.validate_color("white")
        self.assertTrue(is_valid)
        
        # لون hex
        is_valid, message = self.param_validator.validate_color("#FF0000")
        self.assertTrue(is_valid)
    
    def test_parameter_validator_invalid_color(self):
        """اختبار لون غير صحيح"""
        is_valid, message = self.param_validator.validate_color("invalid_color")
        self.assertFalse(is_valid)
        self.assertIn("تنسيق لون غير صحيح", message)

if __name__ == '__main__':
    # تشغيل الاختبارات
    unittest.main(verbosity=2)
