#!/usr/bin/env python3
"""
اختبار شامل لنظام Gemini الجديد مع التبديل التلقائي
Comprehensive test for the new Gemini system with automatic key rotation
"""

import sys
import os
from pathlib import Path

# إضافة مجلد src إلى مسار Python
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_gemini_key_manager():
    """اختبار مدير مفاتيح Gemini"""
    
    print("🔑 اختبار مدير مفاتيح Gemini")
    print("=" * 40)
    
    try:
        from ai.gemini_key_manager import gemini_key_manager
        
        # عرض معلومات المفاتيح
        status = gemini_key_manager.get_keys_status_summary()
        
        print(f"✅ إجمالي المفاتيح: {status['total_keys']}")
        print(f"✅ المفاتيح المتاحة: {status['available_keys']}")
        print(f"✅ المفاتيح المحظورة: {status['blocked_keys']}")
        print(f"✅ المفتاح الحالي: {status['current_key_suffix']}")
        
        # عرض تفاصيل أول 5 مفاتيح
        print("\n📋 تفاصيل المفاتيح (أول 5):")
        for key_detail in status['keys_details'][:5]:
            current_marker = "👈 حالي" if key_detail['is_current'] else ""
            print(f"  {key_detail['index']+1}. {key_detail['key_suffix']} - {key_detail['status']} {current_marker}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير المفاتيح: {e}")
        return False

def test_gemini_client():
    """اختبار عميل Gemini الجديد"""
    
    print("\n🤖 اختبار عميل Gemini")
    print("=" * 40)
    
    try:
        from ai.gemini_client import GeminiClient
        
        client = GeminiClient()
        
        if not client.is_available():
            print("❌ عميل Gemini غير متوفر")
            return False
        
        print("✅ تم إنشاء عميل Gemini بنجاح")
        
        # اختبار الترجمة
        print("\n🌐 اختبار الترجمة:")
        test_text = "Hello, this is a test message for translation"
        
        translated = client.translate_text(test_text, "ar", "en")
        
        if translated:
            print(f"✅ الترجمة نجحت: {translated[:100]}...")
            
            # اختبار توليد العنوان
            print("\n📝 اختبار توليد العنوان:")
            title = client.generate_video_title("amazing gaming moment", "exciting")
            
            if title:
                print(f"✅ العنوان: {title}")
                
                # اختبار توليد الهاشتاغات
                print("\n🏷️ اختبار توليد الهاشتاغات:")
                hashtags = client.generate_hashtags("gaming video exciting moment", "exciting")
                
                if hashtags:
                    print(f"✅ الهاشتاغات: {', '.join(hashtags[:5])}...")
                    return True
                else:
                    print("⚠️  فشل في توليد الهاشتاغات")
            else:
                print("⚠️  فشل في توليد العنوان")
        else:
            print("⚠️  فشل في الترجمة")
        
        return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عميل Gemini: {e}")
        return False

def test_google_cloud_libraries():
    """اختبار مكتبات Google Cloud"""
    
    print("\n☁️ اختبار مكتبات Google Cloud")
    print("=" * 40)
    
    libraries = [
        ("google.cloud.videointelligence", "Video Intelligence"),
        ("google.cloud.speech", "Speech-to-Text"),
        ("google.cloud.translate", "Translation")
    ]
    
    working_libraries = 0
    
    for lib_name, display_name in libraries:
        try:
            __import__(lib_name)
            print(f"✅ {display_name}: متوفر")
            working_libraries += 1
        except ImportError:
            print(f"❌ {display_name}: غير متوفر")
    
    print(f"\nالنتيجة: {working_libraries}/{len(libraries)} مكتبات تعمل")
    return working_libraries > 0

def test_updated_livestream_analyzer():
    """اختبار محلل البثوث المباشرة المحدث"""
    
    print("\n🎬 اختبار محلل البثوث المباشرة المحدث")
    print("=" * 50)
    
    try:
        from ai.livestream_analyzer import LivestreamAnalyzer
        
        analyzer = LivestreamAnalyzer()
        print("✅ تم إنشاء محلل البثوث المباشرة")
        
        # اختبار تصنيف اللحظات
        print("\n🔍 اختبار تصنيف اللحظات:")
        test_cases = [
            ("wow this is absolutely insane omg", "exciting"),
            ("haha that's hilarious lol", "funny"),
            ("what the hell just happened", "shocking"),
            ("يا إلهي هذا مجنون رهيب جداً", "exciting"),
            ("ضحك مضحك جداً هههه", "funny")
        ]
        
        for text, expected in test_cases:
            moment_type, confidence = analyzer._classify_moment_type(text)
            status = "✅" if confidence > 0.5 else "⚠️"
            print(f"{status} '{text}' -> {moment_type} ({confidence:.2f})")
        
        # اختبار توليد العناوين الذكية
        print("\n📝 اختبار توليد العناوين الذكية:")
        test_moment = {
            'type': 'exciting',
            'text': 'incredible gaming moment with amazing reaction',
            'description': 'streamer has amazing reaction to unexpected game event'
        }
        
        title = analyzer._generate_clip_title(test_moment)
        print(f"✅ عنوان مولد: {title}")
        
        # اختبار توليد الهاشتاغات الذكية
        print("\n🏷️ اختبار توليد الهاشتاغات الذكية:")
        hashtags = analyzer._generate_clip_hashtags(test_moment)
        print(f"✅ هاشتاغات مولدة: {', '.join(hashtags[:5])}...")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المحلل: {e}")
        return False

def test_assemblyai_integration():
    """اختبار تكامل AssemblyAI"""
    
    print("\n🎵 اختبار تكامل AssemblyAI")
    print("=" * 40)
    
    try:
        from ai.assemblyai_client import AssemblyAIClient
        
        client = AssemblyAIClient()
        
        if not client.is_available():
            print("❌ AssemblyAI غير متوفر")
            return False
        
        print("✅ عميل AssemblyAI متوفر")
        
        # اختبار تحويل صوت تجريبي
        print("🎵 اختبار تحويل صوت تجريبي...")
        test_audio_url = "https://assembly.ai/wildfires.mp3"
        
        transcript = client.transcribe_audio(test_audio_url, "en")
        
        if transcript:
            print(f"✅ التحويل نجح: {transcript[:100]}...")
            return True
        else:
            print("⚠️  فشل في التحويل")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار AssemblyAI: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🧪 اختبار شامل للنظام المحدث مع APIs الجديدة")
    print("=" * 60)
    
    results = {}
    
    # اختبار مدير مفاتيح Gemini
    results["key_manager"] = test_gemini_key_manager()
    
    # اختبار عميل Gemini
    results["gemini_client"] = test_gemini_client()
    
    # اختبار مكتبات Google Cloud
    results["google_cloud"] = test_google_cloud_libraries()
    
    # اختبار AssemblyAI
    results["assemblyai"] = test_assemblyai_integration()
    
    # اختبار محلل البثوث المباشرة المحدث
    results["livestream_analyzer"] = test_updated_livestream_analyzer()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("🎯 النتيجة النهائية")
    print("=" * 60)
    
    working_components = sum(results.values())
    total_components = len(results)
    
    for component, status in results.items():
        status_icon = "✅" if status else "❌"
        component_name = {
            "key_manager": "مدير مفاتيح Gemini",
            "gemini_client": "عميل Gemini",
            "google_cloud": "مكتبات Google Cloud",
            "assemblyai": "AssemblyAI",
            "livestream_analyzer": "محلل البثوث المباشرة"
        }.get(component, component)
        
        print(f"{status_icon} {component_name}: {'يعمل' if status else 'لا يعمل'}")
    
    print(f"\nالنتيجة: {working_components}/{total_components} مكونات تعمل")
    
    if working_components >= 3:
        print("\n🎉 النظام جاهز للاستخدام!")
        print("الميزات المتاحة:")
        
        if results["assemblyai"]:
            print("  ✅ تحويل الكلام إلى نص عالي الجودة")
        
        if results["gemini_client"]:
            print("  ✅ ترجمة ذكية مع التبديل التلقائي للمفاتيح")
            print("  ✅ توليد عناوين وهاشتاغات ذكية")
        
        if results["livestream_analyzer"]:
            print("  ✅ تحليل البثوث المباشرة الطويلة")
            print("  ✅ استخراج اللحظات الفيروسية")
        
        print("\nيمكنك الآن:")
        print("1. تشغيل محلل البثوث: python run_livestream_analyzer.py")
        print("2. تشغيل التطبيق الكامل: python main.py")
        
        return 0
    else:
        print("\n⚠️  النظام يحتاج إصلاحات إضافية")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
