#!/usr/bin/env python3
"""
اختبار شامل لجميع APIs المدمجة
Comprehensive test for all integrated APIs
"""

import sys
import os
from pathlib import Path

# إضافة مجلد src إلى مسار Python
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_apis():
    """اختبار جميع APIs"""
    
    print("🔑 اختبار APIs المدمجة")
    print("=" * 50)
    
    results = {
        "huggingface": False,
        "gemini": False,
        "assemblyai": False,
        "google_cloud": False
    }
    
    # اختبار Hugging Face
    print("\n1. اختبار Hugging Face API...")
    try:
        from ai.huggingface_client import HuggingFaceClient
        hf_client = HuggingFaceClient()
        
        if hf_client.api_key:
            print("✅ مفتاح Hugging Face متوفر")
            results["huggingface"] = True
        else:
            print("❌ مفتاح Hugging Face غير متوفر")
            
    except Exception as e:
        print(f"❌ خطأ في Hugging Face: {e}")
    
    # اختبار Gemini
    print("\n2. اختبار Google Gemini API...")
    try:
        from ai.gemini_client import GeminiClient
        gemini_client = GeminiClient()
        
        if gemini_client.is_available():
            print("✅ مفتاح Gemini متوفر")
            
            # اختبار الترجمة
            test_text = "Hello, this is a test"
            translated = gemini_client.translate_text(test_text, "ar", "en")
            
            if translated:
                print(f"✅ اختبار الترجمة نجح: {translated[:50]}...")
                results["gemini"] = True
            else:
                print("⚠️  الترجمة لم تعمل")
        else:
            print("❌ مفتاح Gemini غير متوفر")
            
    except Exception as e:
        print(f"❌ خطأ في Gemini: {e}")
    
    # اختبار AssemblyAI
    print("\n3. اختبار AssemblyAI API...")
    try:
        from ai.assemblyai_client import AssemblyAIClient
        assemblyai_client = AssemblyAIClient()
        
        if assemblyai_client.is_available():
            print("✅ مفتاح AssemblyAI متوفر")
            
            # اختبار تحويل صوت تجريبي
            test_audio_url = "https://assembly.ai/wildfires.mp3"
            print("🎵 اختبار تحويل صوت تجريبي...")
            
            transcript = assemblyai_client.transcribe_audio(test_audio_url, "en")
            
            if transcript:
                print(f"✅ اختبار التحويل نجح: {transcript[:100]}...")
                results["assemblyai"] = True
            else:
                print("⚠️  التحويل لم يعمل")
        else:
            print("❌ مفتاح AssemblyAI غير متوفر")
            
    except Exception as e:
        print(f"❌ خطأ في AssemblyAI: {e}")
    
    # اختبار Google Cloud
    print("\n4. اختبار Google Cloud APIs...")
    try:
        from ai.google_cloud_client import GoogleCloudClient
        gc_client = GoogleCloudClient()
        
        if gc_client.is_available():
            print("✅ Google Cloud متوفر")
            results["google_cloud"] = True
        else:
            print("❌ Google Cloud غير متوفر")
            
    except Exception as e:
        print(f"❌ خطأ في Google Cloud: {e}")
    
    # ملخص النتائج
    print("\n" + "=" * 50)
    print("📊 ملخص نتائج الاختبار")
    print("=" * 50)
    
    working_apis = sum(results.values())
    total_apis = len(results)
    
    for api_name, status in results.items():
        status_icon = "✅" if status else "❌"
        print(f"{status_icon} {api_name.upper()}: {'يعمل' if status else 'لا يعمل'}")
    
    print(f"\nالنتيجة: {working_apis}/{total_apis} APIs تعمل")
    
    if working_apis >= 2:
        print("🎉 عدد كافٍ من APIs يعمل - الأداة جاهزة!")
        return True
    else:
        print("⚠️  عدد قليل من APIs يعمل - قد تكون الميزات محدودة")
        return False

def test_livestream_analyzer():
    """اختبار محلل البثوث المباشرة مع APIs الجديدة"""
    
    print("\n🎬 اختبار محلل البثوث المباشرة")
    print("=" * 50)
    
    try:
        from ai.livestream_analyzer import LivestreamAnalyzer
        
        analyzer = LivestreamAnalyzer()
        print("✅ تم إنشاء محلل البثوث المباشرة")
        
        # اختبار تصنيف اللحظات
        test_cases = [
            ("wow this is insane omg no way", "exciting"),
            ("haha that's so funny lol", "funny"),
            ("what the hell just happened", "shocking"),
            ("يا إلهي هذا مجنون رهيب", "exciting"),
            ("ضحك مضحك جداً هههه", "funny")
        ]
        
        print("\n🔍 اختبار تصنيف اللحظات:")
        for text, expected in test_cases:
            moment_type, confidence = analyzer._classify_moment_type(text)
            status = "✅" if moment_type == expected or confidence > 0.5 else "⚠️"
            print(f"{status} '{text}' -> {moment_type} ({confidence:.2f})")
        
        # اختبار توليد العناوين
        print("\n📝 اختبار توليد العناوين:")
        test_moment = {
            'type': 'exciting',
            'text': 'amazing gameplay moment',
            'description': 'incredible gaming moment'
        }
        
        title = analyzer._generate_clip_title(test_moment)
        print(f"✅ عنوان مولد: {title}")
        
        # اختبار توليد الهاشتاغات
        print("\n🏷️ اختبار توليد الهاشتاغات:")
        hashtags = analyzer._generate_clip_hashtags(test_moment)
        print(f"✅ هاشتاغات مولدة: {', '.join(hashtags[:5])}...")
        
        print("\n✅ جميع اختبارات المحلل نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المحلل: {e}")
        return False

def test_environment():
    """اختبار البيئة والمتطلبات"""
    
    print("\n🔧 اختبار البيئة")
    print("=" * 30)
    
    # فحص ملف .env
    env_file = Path(".env")
    if env_file.exists():
        print("✅ ملف .env موجود")
        
        # قراءة المفاتيح
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        keys_to_check = [
            "HUGGINGFACE_API_KEY",
            "GOOGLE_GEMINI_API_KEY", 
            "ASSEMBLYAI_API_KEY",
            "GOOGLE_CLOUD_VIDEO_INTELLIGENCE_API_KEY",
            "GOOGLE_CLOUD_SPEECH_API_KEY"
        ]
        
        for key in keys_to_check:
            if key in content and not content.split(key + "=")[1].split("\n")[0].strip() in ["", "your_key_here"]:
                print(f"✅ {key} مُعرَّف")
            else:
                print(f"⚠️  {key} غير مُعرَّف أو فارغ")
    else:
        print("❌ ملف .env غير موجود")
    
    # فحص المكتبات
    print("\n📚 فحص المكتبات:")
    libraries = [
        "assemblyai",
        "requests", 
        "opencv-python",
        "numpy",
        "librosa"
    ]
    
    for lib in libraries:
        try:
            if lib == "opencv-python":
                import cv2
                print(f"✅ {lib} (OpenCV)")
            elif lib == "assemblyai":
                import assemblyai
                print(f"✅ {lib}")
            else:
                __import__(lib)
                print(f"✅ {lib}")
        except ImportError:
            print(f"❌ {lib} غير مثبت")

def main():
    """الدالة الرئيسية"""
    
    print("🧪 اختبار شامل للأداة مع APIs الجديدة")
    print("=" * 60)
    
    # اختبار البيئة
    test_environment()
    
    # اختبار APIs
    apis_working = test_apis()
    
    # اختبار المحلل
    analyzer_working = test_livestream_analyzer()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("🎯 النتيجة النهائية")
    print("=" * 60)
    
    if apis_working and analyzer_working:
        print("🎉 جميع الاختبارات نجحت!")
        print("الأداة جاهزة لتحليل البثوث المباشرة")
        print("\nيمكنك الآن:")
        print("1. تشغيل محلل البثوث: python run_livestream_analyzer.py")
        print("2. تشغيل التطبيق الكامل: python main.py")
        return 0
    else:
        print("⚠️  بعض الاختبارات فشلت")
        print("الأداة ستعمل بميزات محدودة")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
