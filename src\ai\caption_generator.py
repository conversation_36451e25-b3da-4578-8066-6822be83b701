"""
مولد التعليقات التوضيحية والترجمة
Caption generator and translation module
"""

import logging
import re
from typing import Optional, Dict, List, Any, Tuple
from pathlib import Path
import json

from config.settings import AISettings
from ai.huggingface_client import HuggingFaceClient
from ai.google_cloud_client import GoogleCloudClient
from core.video_processor import VideoProcessor
from utils.file_utils import FileManager

logger = logging.getLogger(__name__)

class CaptionGenerator:
    """مولد التعليقات التوضيحية والترجمة"""
    
    def __init__(self):
        self.hf_client = HuggingFaceClient()
        self.gc_client = GoogleCloudClient()
        self.video_processor = VideoProcessor()
        self.file_manager = FileManager()
        
        # إعدادات التعليقات
        self.max_caption_length = 80  # أحرف
        self.max_caption_duration = 5.0  # ثواني
        self.min_caption_duration = 1.0  # ثواني
        
        logger.info("تم تهيئة مولد التعليقات التوضيحية")
    
    def generate_captions_from_video(self, video_path: str, 
                                   language: str = "ar") -> Optional[List[Dict]]:
        """توليد التعليقات التوضيحية من الفيديو"""
        try:
            logger.info(f"بدء توليد التعليقات التوضيحية للفيديو: {video_path}")
            
            # استخراج الصوت
            audio_path = self.file_manager.create_temp_file(suffix='.wav')
            success = self.video_processor.extract_audio(video_path, str(audio_path))
            
            if not success:
                logger.error("فشل في استخراج الصوت من الفيديو")
                return None
            
            # تحويل الكلام إلى نص
            transcript = self._transcribe_audio(str(audio_path), language)
            
            if not transcript:
                logger.error("فشل في تحويل الكلام إلى نص")
                return None
            
            # إنشاء التعليقات التوضيحية مع التوقيتات
            captions = self._create_timed_captions(transcript, video_path)
            
            # تنظيف الملف المؤقت
            audio_path.unlink(missing_ok=True)
            
            logger.info(f"تم توليد {len(captions)} تعليق توضيحي")
            return captions
            
        except Exception as e:
            logger.error(f"خطأ في توليد التعليقات التوضيحية: {e}")
            return None
    
    def _transcribe_audio(self, audio_path: str, language: str) -> Optional[str]:
        """تحويل الصوت إلى نص"""
        transcript = None
        
        # محاولة استخدام Google Cloud أولاً
        if self.gc_client.is_available():
            language_code = self._get_google_language_code(language)
            transcript = self.gc_client.transcribe_audio(audio_path, language_code)
        
        # محاولة استخدام Hugging Face كبديل
        if not transcript and self.hf_client.api_key:
            transcript = self.hf_client.speech_to_text(audio_path, language)
        
        return transcript
    
    def _get_google_language_code(self, language: str) -> str:
        """تحويل رمز اللغة إلى تنسيق Google Cloud"""
        language_map = {
            "ar": "ar-SA",
            "en": "en-US",
            "fr": "fr-FR",
            "es": "es-ES",
            "de": "de-DE"
        }
        return language_map.get(language, "ar-SA")
    
    def _create_timed_captions(self, transcript: str, video_path: str) -> List[Dict]:
        """إنشاء تعليقات توضيحية مع التوقيتات"""
        captions = []
        
        try:
            # تنظيف النص
            cleaned_text = self._clean_transcript(transcript)
            
            # تقسيم النص إلى جمل
            sentences = self._split_into_sentences(cleaned_text)
            
            if not sentences:
                return captions
            
            # الحصول على مدة الفيديو
            video_info = self.video_processor.get_video_info(video_path)
            video_duration = video_info['duration']
            
            # توزيع الجمل على مدة الفيديو
            captions = self._distribute_sentences_over_time(
                sentences, video_duration
            )
            
            # تحسين التوقيتات
            captions = self._optimize_caption_timing(captions)
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء التوقيتات: {e}")
        
        return captions
    
    def _clean_transcript(self, transcript: str) -> str:
        """تنظيف النص المستخرج"""
        # إزالة الأصوات والكلمات غير المرغوبة
        unwanted_patterns = [
            r'\b(um|uh|ah|er|hmm)\b',  # أصوات التردد
            r'\[.*?\]',  # النصوص بين أقواس مربعة
            r'\(.*?\)',  # النصوص بين أقواس
            r'\s+',      # مسافات متعددة
        ]
        
        cleaned = transcript
        for pattern in unwanted_patterns:
            cleaned = re.sub(pattern, ' ', cleaned, flags=re.IGNORECASE)
        
        # تنظيف المسافات
        cleaned = ' '.join(cleaned.split())
        
        return cleaned.strip()
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """تقسيم النص إلى جمل"""
        # تقسيم بناءً على علامات الترقيم
        sentence_endings = r'[.!?؟]'
        sentences = re.split(sentence_endings, text)
        
        # تنظيف الجمل
        cleaned_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if sentence and len(sentence) > 3:  # تجاهل الجمل القصيرة جداً
                cleaned_sentences.append(sentence)
        
        # تقسيم الجمل الطويلة
        final_sentences = []
        for sentence in cleaned_sentences:
            if len(sentence) > self.max_caption_length:
                # تقسيم الجملة الطويلة
                parts = self._split_long_sentence(sentence)
                final_sentences.extend(parts)
            else:
                final_sentences.append(sentence)
        
        return final_sentences
    
    def _split_long_sentence(self, sentence: str) -> List[str]:
        """تقسيم الجملة الطويلة إلى أجزاء"""
        parts = []
        words = sentence.split()
        
        current_part = []
        current_length = 0
        
        for word in words:
            word_length = len(word) + 1  # +1 للمسافة
            
            if current_length + word_length > self.max_caption_length and current_part:
                # حفظ الجزء الحالي
                parts.append(' '.join(current_part))
                current_part = [word]
                current_length = len(word)
            else:
                current_part.append(word)
                current_length += word_length
        
        # إضافة الجزء الأخير
        if current_part:
            parts.append(' '.join(current_part))
        
        return parts
    
    def _distribute_sentences_over_time(self, sentences: List[str], 
                                      video_duration: float) -> List[Dict]:
        """توزيع الجمل على مدة الفيديو"""
        captions = []
        
        if not sentences:
            return captions
        
        # حساب المدة لكل جملة
        total_chars = sum(len(sentence) for sentence in sentences)
        
        current_time = 0.0
        
        for sentence in sentences:
            # حساب مدة الجملة بناءً على طولها
            char_ratio = len(sentence) / total_chars if total_chars > 0 else 1 / len(sentences)
            base_duration = video_duration * char_ratio
            
            # تطبيق الحدود الدنيا والعليا
            duration = max(self.min_caption_duration, 
                          min(self.max_caption_duration, base_duration))
            
            # التأكد من عدم تجاوز مدة الفيديو
            if current_time + duration > video_duration:
                duration = video_duration - current_time
            
            if duration > 0:
                caption = {
                    "start_time": current_time,
                    "end_time": current_time + duration,
                    "duration": duration,
                    "text": sentence,
                    "char_count": len(sentence)
                }
                captions.append(caption)
                
                current_time += duration
            
            # التوقف إذا وصلنا لنهاية الفيديو
            if current_time >= video_duration:
                break
        
        return captions
    
    def _optimize_caption_timing(self, captions: List[Dict]) -> List[Dict]:
        """تحسين توقيتات التعليقات التوضيحية"""
        optimized = []
        
        for i, caption in enumerate(captions):
            # التأكد من عدم التداخل مع التعليق التالي
            if i < len(captions) - 1:
                next_caption = captions[i + 1]
                if caption["end_time"] > next_caption["start_time"]:
                    # تعديل وقت النهاية لتجنب التداخل
                    caption["end_time"] = next_caption["start_time"] - 0.1
                    caption["duration"] = caption["end_time"] - caption["start_time"]
            
            # التأكد من الحد الأدنى للمدة
            if caption["duration"] >= self.min_caption_duration:
                optimized.append(caption)
        
        return optimized
    
    def translate_captions(self, captions: List[Dict], 
                          target_language: str = "en") -> Optional[List[Dict]]:
        """ترجمة التعليقات التوضيحية"""
        try:
            logger.info(f"بدء ترجمة التعليقات إلى {target_language}")
            
            translated_captions = []
            
            for caption in captions:
                original_text = caption["text"]
                
                # ترجمة النص
                translated_text = self._translate_text(original_text, target_language)
                
                if translated_text:
                    translated_caption = caption.copy()
                    translated_caption["text"] = translated_text
                    translated_caption["original_text"] = original_text
                    translated_caption["target_language"] = target_language
                    translated_captions.append(translated_caption)
                else:
                    # الاحتفاظ بالنص الأصلي في حالة فشل الترجمة
                    translated_captions.append(caption)
            
            logger.info(f"تم ترجمة {len(translated_captions)} تعليق توضيحي")
            return translated_captions
            
        except Exception as e:
            logger.error(f"خطأ في ترجمة التعليقات: {e}")
            return None
    
    def _translate_text(self, text: str, target_language: str) -> Optional[str]:
        """ترجمة النص"""
        translated = None
        
        # محاولة استخدام Google Cloud أولاً
        if self.gc_client.is_available():
            translated = self.gc_client.translate_text_google(text, target_language)
        
        # محاولة استخدام Hugging Face كبديل
        if not translated and self.hf_client.api_key:
            source_lang = "ar"  # افتراض أن النص الأصلي عربي
            translated = self.hf_client.translate_text(text, source_lang, target_language)
        
        return translated
    
    def export_captions_srt(self, captions: List[Dict], output_path: str) -> bool:
        """تصدير التعليقات التوضيحية بتنسيق SRT"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                for i, caption in enumerate(captions, 1):
                    # تحويل الوقت إلى تنسيق SRT
                    start_time = self._seconds_to_srt_time(caption["start_time"])
                    end_time = self._seconds_to_srt_time(caption["end_time"])
                    
                    # كتابة التعليق
                    f.write(f"{i}\n")
                    f.write(f"{start_time} --> {end_time}\n")
                    f.write(f"{caption['text']}\n\n")
            
            logger.info(f"تم تصدير التعليقات التوضيحية: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تصدير SRT: {e}")
            return False
    
    def _seconds_to_srt_time(self, seconds: float) -> str:
        """تحويل الثواني إلى تنسيق وقت SRT"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"
    
    def export_captions_vtt(self, captions: List[Dict], output_path: str) -> bool:
        """تصدير التعليقات التوضيحية بتنسيق WebVTT"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("WEBVTT\n\n")
                
                for caption in captions:
                    start_time = self._seconds_to_vtt_time(caption["start_time"])
                    end_time = self._seconds_to_vtt_time(caption["end_time"])
                    
                    f.write(f"{start_time} --> {end_time}\n")
                    f.write(f"{caption['text']}\n\n")
            
            logger.info(f"تم تصدير التعليقات التوضيحية: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تصدير VTT: {e}")
            return False
    
    def _seconds_to_vtt_time(self, seconds: float) -> str:
        """تحويل الثواني إلى تنسيق وقت WebVTT"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        
        return f"{hours:02d}:{minutes:02d}:{secs:06.3f}"
    
    def save_captions_json(self, captions: List[Dict], output_path: str) -> bool:
        """حفظ التعليقات التوضيحية بتنسيق JSON"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(captions, f, ensure_ascii=False, indent=2)
            
            logger.info(f"تم حفظ التعليقات التوضيحية: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في حفظ JSON: {e}")
            return False
    
    def load_captions_json(self, input_path: str) -> Optional[List[Dict]]:
        """تحميل التعليقات التوضيحية من ملف JSON"""
        try:
            with open(input_path, 'r', encoding='utf-8') as f:
                captions = json.load(f)
            
            logger.info(f"تم تحميل التعليقات التوضيحية: {input_path}")
            return captions
            
        except Exception as e:
            logger.error(f"خطأ في تحميل JSON: {e}")
            return None
