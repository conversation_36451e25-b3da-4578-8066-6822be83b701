"""
أدوات التعامل مع الملفات
File handling utilities
"""

import os
import shutil
import tempfile
import hashlib
from pathlib import Path
from typing import List, Optional, Tuple, Union
import logging

from config.settings import AppSettings

logger = logging.getLogger(__name__)

class FileManager:
    """مدير الملفات والمجلدات"""
    
    def __init__(self):
        self.temp_dir = AppSettings.TEMP_DIR
        self.output_dir = AppSettings.OUTPUT_DIR
        self.supported_video_formats = AppSettings.SUPPORTED_VIDEO_FORMATS
        self.supported_audio_formats = AppSettings.SUPPORTED_AUDIO_FORMATS
        self.supported_image_formats = AppSettings.SUPPORTED_IMAGE_FORMATS
    
    def is_video_file(self, file_path: Union[str, Path]) -> bool:
        """التحقق من كون الملف فيديو"""
        file_path = Path(file_path)
        return file_path.suffix.lower() in self.supported_video_formats
    
    def is_audio_file(self, file_path: Union[str, Path]) -> bool:
        """التحقق من كون الملف صوتي"""
        file_path = Path(file_path)
        return file_path.suffix.lower() in self.supported_audio_formats
    
    def is_image_file(self, file_path: Union[str, Path]) -> bool:
        """التحقق من كون الملف صورة"""
        file_path = Path(file_path)
        return file_path.suffix.lower() in self.supported_image_formats
    
    def get_file_size(self, file_path: Union[str, Path]) -> int:
        """الحصول على حجم الملف بالبايت"""
        try:
            return Path(file_path).stat().st_size
        except OSError:
            return 0
    
    def get_file_size_human(self, file_path: Union[str, Path]) -> str:
        """الحصول على حجم الملف بصيغة قابلة للقراءة"""
        size = self.get_file_size(file_path)
        
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"
    
    def create_temp_file(self, suffix: str = '.tmp', prefix: str = 'video_editor_') -> Path:
        """إنشاء ملف مؤقت"""
        temp_file = tempfile.NamedTemporaryFile(
            suffix=suffix, 
            prefix=prefix, 
            dir=self.temp_dir, 
            delete=False
        )
        temp_file.close()
        return Path(temp_file.name)
    
    def create_temp_dir(self, prefix: str = 'video_editor_') -> Path:
        """إنشاء مجلد مؤقت"""
        temp_dir = tempfile.mkdtemp(prefix=prefix, dir=self.temp_dir)
        return Path(temp_dir)
    
    def cleanup_temp_files(self, older_than_hours: int = 24):
        """تنظيف الملفات المؤقتة القديمة"""
        try:
            import time
            current_time = time.time()
            cutoff_time = current_time - (older_than_hours * 3600)
            
            for item in self.temp_dir.iterdir():
                try:
                    if item.stat().st_mtime < cutoff_time:
                        if item.is_file():
                            item.unlink()
                        elif item.is_dir():
                            shutil.rmtree(item)
                        logger.info(f"تم حذف الملف المؤقت: {item}")
                except OSError as e:
                    logger.warning(f"لا يمكن حذف الملف المؤقت {item}: {e}")
                    
        except Exception as e:
            logger.error(f"خطأ في تنظيف الملفات المؤقتة: {e}")
    
    def safe_filename(self, filename: str) -> str:
        """تنظيف اسم الملف من الأحرف غير المسموحة"""
        # إزالة الأحرف غير المسموحة
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # تقصير الاسم إذا كان طويلاً
        if len(filename) > 200:
            name, ext = os.path.splitext(filename)
            filename = name[:200-len(ext)] + ext
        
        return filename
    
    def get_unique_filename(self, directory: Union[str, Path], 
                          filename: str) -> Path:
        """الحصول على اسم ملف فريد في المجلد"""
        directory = Path(directory)
        base_path = directory / filename
        
        if not base_path.exists():
            return base_path
        
        name, ext = os.path.splitext(filename)
        counter = 1
        
        while True:
            new_filename = f"{name}_{counter}{ext}"
            new_path = directory / new_filename
            if not new_path.exists():
                return new_path
            counter += 1
    
    def calculate_file_hash(self, file_path: Union[str, Path]) -> str:
        """حساب hash للملف للتحقق من التكرار"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"خطأ في حساب hash للملف: {e}")
            return ""
    
    def copy_file_safe(self, src: Union[str, Path], 
                      dst: Union[str, Path]) -> bool:
        """نسخ ملف بأمان مع التحقق"""
        try:
            src_path = Path(src)
            dst_path = Path(dst)
            
            if not src_path.exists():
                logger.error(f"الملف المصدر غير موجود: {src_path}")
                return False
            
            # إنشاء المجلد الهدف إذا لم يكن موجوداً
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            # نسخ الملف
            shutil.copy2(src_path, dst_path)
            
            # التحقق من النسخ
            if dst_path.exists() and dst_path.stat().st_size == src_path.stat().st_size:
                logger.info(f"تم نسخ الملف بنجاح: {dst_path}")
                return True
            else:
                logger.error(f"فشل في نسخ الملف: {dst_path}")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في نسخ الملف: {e}")
            return False
    
    def move_file_safe(self, src: Union[str, Path], 
                      dst: Union[str, Path]) -> bool:
        """نقل ملف بأمان"""
        try:
            if self.copy_file_safe(src, dst):
                Path(src).unlink()
                logger.info(f"تم نقل الملف بنجاح: {dst}")
                return True
            return False
        except Exception as e:
            logger.error(f"خطأ في نقل الملف: {e}")
            return False
    
    def get_available_space(self, path: Union[str, Path]) -> int:
        """الحصول على المساحة المتاحة بالبايت"""
        try:
            statvfs = os.statvfs(path)
            return statvfs.f_frsize * statvfs.f_bavail
        except (OSError, AttributeError):
            # للأنظمة التي لا تدعم statvfs
            try:
                import shutil
                total, used, free = shutil.disk_usage(path)
                return free
            except OSError:
                return 0
    
    def validate_output_path(self, output_path: Union[str, Path]) -> Tuple[bool, str]:
        """التحقق من صحة مسار الإخراج"""
        try:
            output_path = Path(output_path)
            
            # التحقق من إمكانية الكتابة في المجلد الأب
            parent_dir = output_path.parent
            if not parent_dir.exists():
                try:
                    parent_dir.mkdir(parents=True, exist_ok=True)
                except OSError:
                    return False, f"لا يمكن إنشاء المجلد: {parent_dir}"
            
            if not os.access(parent_dir, os.W_OK):
                return False, f"لا توجد صلاحية كتابة في المجلد: {parent_dir}"
            
            # التحقق من المساحة المتاحة (على الأقل 100MB)
            available_space = self.get_available_space(parent_dir)
            if available_space < 100 * 1024 * 1024:  # 100MB
                return False, f"مساحة تخزين غير كافية: {self.get_file_size_human(available_space)} متاحة"
            
            return True, "مسار صحيح"
            
        except Exception as e:
            return False, f"خطأ في التحقق من المسار: {e}"
