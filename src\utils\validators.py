"""
أدوات التحقق من صحة البيانات
Data validation utilities
"""

import os
import re
from pathlib import Path
from typing import List, Optional, Tuple, Union, Dict
import logging

from config.settings import AppSettings, AISettings

logger = logging.getLogger(__name__)

class VideoValidator:
    """مُحقق صحة ملفات الفيديو"""
    
    def __init__(self):
        self.max_file_size = 500 * 1024 * 1024  # 500MB
        self.max_duration = AISettings.MAX_VIDEO_DURATION  # 30 دقيقة
        self.supported_formats = AppSettings.SUPPORTED_VIDEO_FORMATS
    
    def validate_video_file(self, file_path: Union[str, Path]) -> Tuple[bool, str]:
        """التحقق من صحة ملف الفيديو"""
        try:
            file_path = Path(file_path)
            
            # التحقق من وجود الملف
            if not file_path.exists():
                return False, "الملف غير موجود"
            
            # التحقق من التنسيق
            if file_path.suffix.lower() not in self.supported_formats:
                return False, f"تنسيق غير مدعوم: {file_path.suffix}"
            
            # التحقق من حجم الملف
            file_size = file_path.stat().st_size
            if file_size > self.max_file_size:
                return False, f"حجم الملف كبير جداً: {file_size / (1024*1024):.1f}MB"
            
            if file_size == 0:
                return False, "الملف فارغ"
            
            # التحقق من إمكانية القراءة
            if not os.access(file_path, os.R_OK):
                return False, "لا توجد صلاحية قراءة للملف"
            
            return True, "ملف صحيح"
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من الفيديو: {e}")
            return False, f"خطأ في التحقق: {e}"
    
    def validate_video_duration(self, duration: float) -> Tuple[bool, str]:
        """التحقق من مدة الفيديو"""
        if duration <= 0:
            return False, "مدة الفيديو غير صحيحة"
        
        if duration > self.max_duration:
            return False, f"مدة الفيديو طويلة جداً: {duration/60:.1f} دقيقة (الحد الأقصى: {self.max_duration/60} دقيقة)"
        
        return True, "مدة صحيحة"
    
    def validate_time_range(self, start_time: float, end_time: float, 
                           video_duration: float) -> Tuple[bool, str]:
        """التحقق من نطاق زمني للقص"""
        if start_time < 0:
            return False, "وقت البداية لا يمكن أن يكون سالباً"
        
        if end_time <= start_time:
            return False, "وقت النهاية يجب أن يكون أكبر من وقت البداية"
        
        if start_time >= video_duration:
            return False, "وقت البداية يتجاوز مدة الفيديو"
        
        if end_time > video_duration:
            return False, "وقت النهاية يتجاوز مدة الفيديو"
        
        clip_duration = end_time - start_time
        if clip_duration < 0.1:  # 100ms على الأقل
            return False, "مدة المقطع قصيرة جداً (أقل من 0.1 ثانية)"
        
        return True, "نطاق زمني صحيح"

class TextValidator:
    """مُحقق صحة النصوص"""
    
    def __init__(self):
        self.max_text_length = 500
        self.min_text_length = 1
    
    def validate_text_overlay(self, text: str) -> Tuple[bool, str]:
        """التحقق من صحة نص التراكب"""
        if not text or not text.strip():
            return False, "النص فارغ"
        
        text = text.strip()
        
        if len(text) < self.min_text_length:
            return False, "النص قصير جداً"
        
        if len(text) > self.max_text_length:
            return False, f"النص طويل جداً: {len(text)} حرف (الحد الأقصى: {self.max_text_length})"
        
        # التحقق من الأحرف الخاصة التي قد تسبب مشاكل في FFmpeg
        problematic_chars = ["'", '"', '\\', '\n', '\r']
        for char in problematic_chars:
            if char in text:
                return False, f"النص يحتوي على أحرف غير مسموحة: {char}"
        
        return True, "نص صحيح"
    
    def sanitize_text(self, text: str) -> str:
        """تنظيف النص من الأحرف المشكلة"""
        if not text:
            return ""
        
        # استبدال الأحرف المشكلة
        text = text.replace("'", "\\'")
        text = text.replace('"', '\\"')
        text = text.replace('\\', '\\\\')
        text = text.replace('\n', ' ')
        text = text.replace('\r', ' ')
        
        # إزالة المسافات الزائدة
        text = ' '.join(text.split())
        
        return text[:self.max_text_length]

class ParameterValidator:
    """مُحقق صحة المعاملات"""
    
    @staticmethod
    def validate_resolution(width: int, height: int) -> Tuple[bool, str]:
        """التحقق من صحة الدقة"""
        if width <= 0 or height <= 0:
            return False, "أبعاد الدقة يجب أن تكون أكبر من صفر"
        
        if width > 7680 or height > 4320:  # 8K max
            return False, "دقة عالية جداً (أكبر من 8K)"
        
        if width < 64 or height < 64:
            return False, "دقة منخفضة جداً (أقل من 64x64)"
        
        # التحقق من أن الأبعاد زوجية (مطلوب لبعض الترميزات)
        if width % 2 != 0 or height % 2 != 0:
            return False, "أبعاد الدقة يجب أن تكون أرقام زوجية"
        
        return True, "دقة صحيحة"
    
    @staticmethod
    def validate_quality_setting(quality: str) -> Tuple[bool, str]:
        """التحقق من إعداد الجودة"""
        valid_qualities = list(AppSettings.VIDEO_QUALITY_PRESETS.keys())
        
        if quality not in valid_qualities:
            return False, f"إعداد جودة غير صحيح. الخيارات المتاحة: {', '.join(valid_qualities)}"
        
        return True, "إعداد جودة صحيح"
    
    @staticmethod
    def validate_zoom_factor(zoom_factor: float) -> Tuple[bool, str]:
        """التحقق من معامل التكبير"""
        if zoom_factor <= 0:
            return False, "معامل التكبير يجب أن يكون أكبر من صفر"
        
        if zoom_factor > 10:
            return False, "معامل التكبير كبير جداً (أكبر من 10x)"
        
        if zoom_factor < 0.1:
            return False, "معامل التكبير صغير جداً (أقل من 0.1x)"
        
        return True, "معامل تكبير صحيح"
    
    @staticmethod
    def validate_font_size(font_size: int) -> Tuple[bool, str]:
        """التحقق من حجم الخط"""
        if font_size <= 0:
            return False, "حجم الخط يجب أن يكون أكبر من صفر"
        
        if font_size > 200:
            return False, "حجم الخط كبير جداً (أكبر من 200)"
        
        if font_size < 8:
            return False, "حجم الخط صغير جداً (أقل من 8)"
        
        return True, "حجم خط صحيح"
    
    @staticmethod
    def validate_color(color: str) -> Tuple[bool, str]:
        """التحقق من صحة اللون"""
        # ألوان أساسية مدعومة
        basic_colors = [
            'white', 'black', 'red', 'green', 'blue', 'yellow', 
            'cyan', 'magenta', 'orange', 'purple', 'pink', 'brown'
        ]
        
        if color.lower() in basic_colors:
            return True, "لون صحيح"
        
        # التحقق من تنسيق hex
        hex_pattern = r'^#[0-9A-Fa-f]{6}$'
        if re.match(hex_pattern, color):
            return True, "لون hex صحيح"
        
        # التحقق من تنسيق RGB
        rgb_pattern = r'^rgb\(\s*\d{1,3}\s*,\s*\d{1,3}\s*,\s*\d{1,3}\s*\)$'
        if re.match(rgb_pattern, color):
            return True, "لون RGB صحيح"
        
        return False, f"تنسيق لون غير صحيح: {color}"

class APIValidator:
    """مُحقق صحة إعدادات API"""
    
    @staticmethod
    def validate_api_key(api_key: str, service_name: str) -> Tuple[bool, str]:
        """التحقق من صحة مفتاح API"""
        if not api_key or not api_key.strip():
            return False, f"مفتاح {service_name} API فارغ"
        
        api_key = api_key.strip()
        
        # التحقق من الطول الأدنى
        if len(api_key) < 10:
            return False, f"مفتاح {service_name} API قصير جداً"
        
        # التحقق من الأحرف المسموحة
        if not re.match(r'^[A-Za-z0-9_\-\.]+$', api_key):
            return False, f"مفتاح {service_name} API يحتوي على أحرف غير مسموحة"
        
        return True, f"مفتاح {service_name} API صحيح"
    
    @staticmethod
    def validate_project_id(project_id: str) -> Tuple[bool, str]:
        """التحقق من صحة معرف المشروع"""
        if not project_id or not project_id.strip():
            return False, "معرف المشروع فارغ"
        
        project_id = project_id.strip()
        
        # التحقق من تنسيق Google Cloud Project ID
        if not re.match(r'^[a-z][a-z0-9\-]{4,28}[a-z0-9]$', project_id):
            return False, "تنسيق معرف المشروع غير صحيح"
        
        return True, "معرف مشروع صحيح"
