# 🎬 Video Editor Pro - <PERSON><PERSON><PERSON><PERSON> البثوث المباشرة

## نظرة عامة

**Video Editor Pro** هو أداة تحرير فيديو احترافية ومجانية بالكامل، مصممة خصيصاً لمنشئي المحتوى الذين يريدون **استخراج اللحظات الفيروسية** من البثوث المباشرة الطويلة.

### 🎯 الميزة الرئيسية: محلل البثوث المباشرة

تحليل ذكي للبثوث الطويلة (10+ ساعات) لاستخراج:
- 🔥 **اللحظات المثيرة** - ردود أفعال قوية، إنجازات مذهلة
- 😂 **اللحظات المضحكة** - مواقف كوميدية، أخطاء مسلية  
- 😱 **اللحظات الصادمة** - مفاجآت غير متوقعة، أحداث دراماتيكية

### ✨ ما يميز الأداة

- **تحليل تلقائي بالذكاء الاصطناعي** - لا حاجة للمشاهدة اليدوية
- **إنشاء مقاطع شورتس جاهزة** - تنسيق عمودي (9:16) مثالي للمنصات
- **ترجمة تلقائية** - نصوص عربية وإنجليزية على المقاطع
- **عناوين وهاشتاغات ذكية** - محتوى جاهز للنشر
- **دعم الملفات الكبيرة** - معالجة بثوث تصل لـ 10+ ساعات

## 🚀 البدء السريع

### 1. التثبيت

```bash
# استنساخ المشروع
git clone https://github.com/your-username/video-editor-pro.git
cd video-editor-pro

# إنشاء البيئة الافتراضية
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate     # Windows

# تثبيت المتطلبات
pip install -r requirements.txt
```

### 2. إعداد FFmpeg

**Windows:**
- حمّل FFmpeg من [الموقع الرسمي](https://ffmpeg.org/download.html)
- أضف مجلد `bin` إلى متغير البيئة PATH

**macOS:**
```bash
brew install ffmpeg
```

**Linux:**
```bash
sudo apt update && sudo apt install ffmpeg
```

### 3. إعداد مفاتيح API (اختياري للميزات المتقدمة)

```bash
cp .env.example .env
```

أضف مفاتيحك في ملف `.env`:
```
HUGGINGFACE_API_KEY=your_key_here
GOOGLE_CLOUD_PROJECT_ID=your_project_id
```

### 4. تشغيل الأداة

```bash
python main.py
```

## 🎬 كيفية استخدام محلل البثوث المباشرة

### الخطوة 1: فتح المحلل
- من القائمة الرئيسية: **ذكاء اصطناعي** → **محلل البثوث المباشرة**
- أو من الشريط الجانبي: انقر على **🎬 محلل البثوث**

### الخطوة 2: اختيار البث
- انقر على **تصفح** واختر ملف البث المباشر
- الأداة تدعم: MP4, AVI, MOV, MKV, WMV, FLV, WebM
- **لا يوجد حد لحجم الملف** - يمكن معالجة بثوث 10+ ساعات

### الخطوة 3: تخصيص الإعدادات
- **عدد المقاطع**: كم مقطع تريد (1-20)
- **مدة المقطع**: 15-60 ثانية لكل مقطع
- **نوع اللحظات**: الكل، مثيرة، مضحكة، أو صادمة
- **خيارات إضافية**:
  - ✅ إضافة ترجمة تلقائية
  - ✅ تحويل للعمودي (شورتس)
  - ✅ توليد عناوين تلقائية

### الخطوة 4: بدء التحليل
- انقر على **🚀 بدء التحليل**
- انتظر انتهاء التحليل (قد يستغرق وقتاً حسب طول البث)
- شاهد النتائج في الجدول

### الخطوة 5: استخدام النتائج
- **▶️ معاينة المقطع**: مشاهدة المقطع قبل النشر
- **📤 تصدير المقطع**: حفظ المقطع في مكان محدد
- **📋 نسخ معلومات النشر**: عنوان، وصف، هاشتاغات جاهزة
- **💾 حفظ جميع النتائج**: تصدير كامل للنتائج

## 🎯 مثال عملي: تحليل بث IShowSpeed

```
📹 الملف: ishowspeed_stream_10hours.mp4
⏱️ المدة: 10.2 ساعة (36,720 ثانية)
💾 الحجم: 15.8 GB
📐 الدقة: 1920x1080

النتائج:
🔥 مثير    | 02:15:30 | 45s | 95% | ردة فعل مجنونة على اللعبة
😂 مضحك   | 04:42:15 | 30s | 88% | موقف كوميدي مع الدونيشن
😱 صادم    | 07:18:45 | 60s | 92% | حدث غير متوقع في اللعب
🔥 مثير    | 08:55:20 | 35s | 89% | لحظة انتصار ملحمية
😂 مضحك   | 09:33:10 | 25s | 85% | تفاعل مضحك مع الشات
```

## 🤖 كيف يعمل الذكاء الاصطناعي

### 1. تحليل الصوت
- **كشف الذروات الصوتية**: العثور على لحظات الصراخ والإثارة
- **تحويل الكلام إلى نص**: فهم ما يقوله الستريمر
- **تحليل المشاعر**: تحديد نوع المشاعر (إثارة، ضحك، صدمة)

### 2. تحليل النص
- **الكلمات المفتاحية**: البحث عن كلمات مثل "OMG", "No way", "Insane"
- **السياق**: فهم السياق العام للحديث
- **التصنيف**: تحديد نوع اللحظة بناءً على المحتوى

### 3. دمج النتائج
- **ترتيب حسب الثقة**: أفضل اللحظات أولاً
- **تجنب التداخل**: عدم اختيار مقاطع متداخلة
- **تحسين المدة**: ضبط طول المقطع ليكون مثالياً

## 🆓 مجاني بالكامل

- ✅ **مفتوح المصدر**: كود مفتوح بالكامل
- ✅ **بدون إعلانات**: تجربة نظيفة
- ✅ **بدون حدود**: معالجة ملفات بأي حجم
- ✅ **APIs مجانية**: استخدام خدمات مجانية للذكاء الاصطناعي

### حدود الاستخدام المجاني
- **Hugging Face**: 100 طلب يومياً
- **Google Cloud**: 1000 دقيقة شهرياً
- يمكن الاستخدام بدون APIs مع ميزات محدودة

## 🔧 المتطلبات التقنية

### الحد الأدنى
- **نظام التشغيل**: Windows 10, macOS 10.14, Ubuntu 18.04
- **Python**: 3.8+
- **الذاكرة**: 4GB RAM
- **التخزين**: 2GB مساحة فارغة
- **الإنترنت**: للميزات المدعومة بالذكاء الاصطناعي

### المُوصى به للبثوث الطويلة
- **الذاكرة**: 8GB+ RAM
- **المعالج**: رباعي النواة أو أفضل
- **التخزين**: SSD مع 50GB+ مساحة فارغة
- **الإنترنت**: اتصال سريع ومستقر

## 📞 الدعم

- 📖 **الوثائق**: [دليل المستخدم](docs/user_guide.md)
- 🐛 **المشاكل**: [GitHub Issues](https://github.com/your-username/video-editor-pro/issues)
- 💬 **النقاشات**: [GitHub Discussions](https://github.com/your-username/video-editor-pro/discussions)

## 📄 الترخيص

هذا المشروع مرخص تحت [MIT License](LICENSE) - مجاني للاستخدام التجاري والشخصي.

## 🙏 شكر خاص

- **FFmpeg**: لمعالجة الفيديو القوية
- **Hugging Face**: لخدمات الذكاء الاصطناعي المجانية
- **Google Cloud**: لخدمات تحليل الفيديو
- **Python Community**: للمكتبات الرائعة

---

**⭐ إذا أعجبتك الأداة، لا تنس إعطاء نجمة للمشروع!**

*صُنع بـ ❤️ لمجتمع منشئي المحتوى العربي*
