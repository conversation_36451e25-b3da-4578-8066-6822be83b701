"""
نافذة تحليل البثوث المباشرة
Livestream analysis window specialized for viral moment extraction
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import logging
from pathlib import Path
from typing import Optional, List, Dict, Any
import json

from ai.livestream_analyzer import LivestreamAnalyzer
from utils.file_utils import FileManager
from utils.validators import VideoValidator
from utils.youtube_downloader import YouTubeDownloader

logger = logging.getLogger(__name__)

class LivestreamAnalysisWindow:
    """نافذة تحليل البثوث المباشرة المتخصصة"""
    
    def __init__(self, parent: tk.Tk):
        self.parent = parent
        self.analyzer = LivestreamAnalyzer()
        self.file_manager = FileManager()
        self.video_validator = VideoValidator()

        # تهيئة محمل YouTube مع معالجة الأخطاء
        try:
            self.youtube_downloader = YouTubeDownloader()
            logger.info("تم تهيئة محمل YouTube بنجاح")
        except Exception as e:
            logger.error(f"خطأ في تهيئة محمل YouTube: {e}")
            self.youtube_downloader = None

        # متغيرات التطبيق
        self.current_video_path: Optional[str] = None
        self.current_video_url: Optional[str] = None
        self.analysis_results: List[Dict[str, Any]] = []
        self.is_analyzing = False
        self.is_downloading = False
        
        # إنشاء النافذة
        self.create_window()
        
        logger.info("تم تشغيل نافذة تحليل البثوث المباشرة")
    
    def create_window(self):
        """إنشاء النافذة المتخصصة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("محلل البثوث المباشرة - Livestream Analyzer")
        self.window.geometry("1000x700")
        self.window.resizable(True, True)
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # إنشاء المحتوى
        self.create_widgets()
        
        # ربط الأحداث
        self.bind_events()
    
    def create_widgets(self):
        """إنشاء عناصر واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان النافذة
        title_label = ttk.Label(main_frame, 
                               text="🎬 محلل البثوث المباشرة - استخراج اللحظات الفيروسية",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # قسم اختيار الفيديو
        self.create_video_selection_section(main_frame)
        
        # قسم إعدادات التحليل
        self.create_analysis_settings_section(main_frame)
        
        # قسم التحليل والنتائج
        self.create_analysis_section(main_frame)
        
        # قسم النتائج
        self.create_results_section(main_frame)
        
        # شريط الحالة
        self.create_status_bar(main_frame)
    
    def create_video_selection_section(self, parent):
        """قسم اختيار الفيديو"""
        video_frame = ttk.LabelFrame(parent, text="📁 اختيار البث المباشر", padding="10")
        video_frame.pack(fill=tk.X, pady=(0, 10))

        # خيارات الإدخال
        input_options_frame = ttk.Frame(video_frame)
        input_options_frame.pack(fill=tk.X, pady=(0, 10))

        self.input_type_var = tk.StringVar(value="file")
        ttk.Radiobutton(input_options_frame, text="📁 ملف محلي",
                       variable=self.input_type_var, value="file",
                       command=self.on_input_type_change).pack(side=tk.LEFT, padx=(0, 20))

        ttk.Radiobutton(input_options_frame, text="🔗 رابط YouTube",
                       variable=self.input_type_var, value="url",
                       command=self.on_input_type_change).pack(side=tk.LEFT)

        # مسار الفيديو أو الرابط
        path_frame = ttk.Frame(video_frame)
        path_frame.pack(fill=tk.X, pady=(0, 10))

        self.path_label = ttk.Label(path_frame, text="مسار الفيديو:")
        self.path_label.pack(side=tk.LEFT)

        self.video_path_var = tk.StringVar()
        self.video_path_entry = ttk.Entry(path_frame, textvariable=self.video_path_var, width=60)
        self.video_path_entry.pack(side=tk.LEFT, padx=(10, 5), fill=tk.X, expand=True)

        # إطار الأزرار
        buttons_frame = ttk.Frame(path_frame)
        buttons_frame.pack(side=tk.RIGHT)

        self.browse_button = ttk.Button(buttons_frame, text="تصفح...", command=self.browse_video)
        self.browse_button.pack(side=tk.RIGHT, padx=(5, 0))

        self.paste_button = ttk.Button(buttons_frame, text="📋 لصق", command=self.paste_url)
        self.paste_button.pack(side=tk.RIGHT, padx=(5, 0))
        self.paste_button.pack_forget()  # إخفاء في البداية

        self.download_button = ttk.Button(buttons_frame, text="📥 تحميل", command=self.download_from_url)
        self.download_button.pack(side=tk.RIGHT, padx=(5, 0))
        self.download_button.pack_forget()  # إخفاء في البداية

        # تلميحات للمستخدم
        self.hints_frame = ttk.Frame(video_frame)
        self.hints_frame.pack(fill=tk.X, pady=(5, 0))

        self.hints_label = ttk.Label(self.hints_frame, text="💡 تلميح: اختر نوع الإدخال أعلاه",
                                    font=("Arial", 8), foreground="gray")
        self.hints_label.pack(anchor=tk.W)

        # معلومات الفيديو
        self.video_info_frame = ttk.Frame(video_frame)
        self.video_info_frame.pack(fill=tk.X, pady=(5, 0))

        self.video_info_text = tk.Text(self.video_info_frame, height=4,
                                      state=tk.DISABLED, wrap=tk.WORD)
        self.video_info_text.pack(fill=tk.X)
    
    def create_analysis_settings_section(self, parent):
        """قسم إعدادات التحليل"""
        settings_frame = ttk.LabelFrame(parent, text="⚙️ إعدادات التحليل", padding="10")
        settings_frame.pack(fill=tk.X, pady=(0, 10))
        
        # الصف الأول من الإعدادات
        row1 = ttk.Frame(settings_frame)
        row1.pack(fill=tk.X, pady=(0, 10))
        
        # عدد المقاطع المطلوبة
        ttk.Label(row1, text="عدد المقاطع:").pack(side=tk.LEFT)
        self.clips_count_var = tk.IntVar(value=5)
        clips_spin = ttk.Spinbox(row1, from_=1, to=20, width=5, 
                                textvariable=self.clips_count_var)
        clips_spin.pack(side=tk.LEFT, padx=(5, 20))
        
        # مدة المقطع
        ttk.Label(row1, text="مدة المقطع (ثانية):").pack(side=tk.LEFT)
        self.clip_duration_var = tk.IntVar(value=30)
        duration_spin = ttk.Spinbox(row1, from_=15, to=60, width=5,
                                   textvariable=self.clip_duration_var)
        duration_spin.pack(side=tk.LEFT, padx=(5, 20))
        
        # نوع اللحظات
        ttk.Label(row1, text="نوع اللحظات:").pack(side=tk.LEFT)
        self.moment_type_var = tk.StringVar(value="الكل")
        moment_combo = ttk.Combobox(row1, textvariable=self.moment_type_var,
                                   values=["الكل", "مثيرة", "مضحكة", "صادمة"],
                                   state="readonly", width=10)
        moment_combo.pack(side=tk.LEFT, padx=(5, 0))
        
        # الصف الثاني من الإعدادات
        row2 = ttk.Frame(settings_frame)
        row2.pack(fill=tk.X)
        
        # إعدادات متقدمة
        self.add_captions_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(row2, text="إضافة ترجمة تلقائية", 
                       variable=self.add_captions_var).pack(side=tk.LEFT, padx=(0, 20))
        
        self.convert_vertical_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(row2, text="تحويل للعمودي (شورتس)", 
                       variable=self.convert_vertical_var).pack(side=tk.LEFT, padx=(0, 20))
        
        self.auto_titles_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(row2, text="توليد عناوين تلقائية", 
                       variable=self.auto_titles_var).pack(side=tk.LEFT)
    
    def create_analysis_section(self, parent):
        """قسم التحليل"""
        analysis_frame = ttk.LabelFrame(parent, text="🔍 التحليل", padding="10")
        analysis_frame.pack(fill=tk.X, pady=(0, 10))
        
        # أزرار التحليل
        buttons_frame = ttk.Frame(analysis_frame)
        buttons_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.analyze_button = ttk.Button(buttons_frame, text="🚀 بدء التحليل", 
                                        command=self.start_analysis,
                                        style="Accent.TButton")
        self.analyze_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(buttons_frame, text="⏹️ إيقاف", 
                                     command=self.stop_analysis,
                                     state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(buttons_frame, text="📁 فتح مجلد النتائج", 
                  command=self.open_results_folder).pack(side=tk.RIGHT)
        
        # شريط التقدم
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(analysis_frame, 
                                           variable=self.progress_var,
                                           mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=(0, 5))
        
        # نص التقدم
        self.progress_label = ttk.Label(analysis_frame, text="جاهز للتحليل")
        self.progress_label.pack()
    
    def create_results_section(self, parent):
        """قسم النتائج"""
        results_frame = ttk.LabelFrame(parent, text="📊 النتائج", padding="10")
        results_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # جدول النتائج
        columns = ("النوع", "الوقت", "المدة", "الثقة", "الوصف")
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show="headings", height=8)
        
        # تكوين الأعمدة
        self.results_tree.heading("النوع", text="نوع اللحظة")
        self.results_tree.heading("الوقت", text="الوقت")
        self.results_tree.heading("المدة", text="المدة")
        self.results_tree.heading("الثقة", text="الثقة %")
        self.results_tree.heading("الوصف", text="الوصف")
        
        self.results_tree.column("النوع", width=80)
        self.results_tree.column("الوقت", width=100)
        self.results_tree.column("المدة", width=80)
        self.results_tree.column("الثقة", width=80)
        self.results_tree.column("الوصف", width=300)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # أزرار النتائج
        results_buttons = ttk.Frame(results_frame)
        results_buttons.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(results_buttons, text="▶️ معاينة المقطع", 
                  command=self.preview_clip).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(results_buttons, text="📤 تصدير المقطع", 
                  command=self.export_clip).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(results_buttons, text="📋 نسخ معلومات النشر", 
                  command=self.copy_publish_info).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(results_buttons, text="💾 حفظ جميع النتائج", 
                  command=self.save_all_results).pack(side=tk.RIGHT)
    
    def create_status_bar(self, parent):
        """شريط الحالة"""
        self.status_frame = ttk.Frame(parent)
        self.status_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.status_label = ttk.Label(self.status_frame, text="جاهز")
        self.status_label.pack(side=tk.LEFT)
        
        # معلومات النظام
        system_info = self.analyzer.file_manager.get_available_space(Path.cwd())
        space_gb = system_info / (1024**3) if system_info > 0 else 0
        
        self.system_label = ttk.Label(self.status_frame, 
                                     text=f"المساحة المتاحة: {space_gb:.1f}GB")
        self.system_label.pack(side=tk.RIGHT)
    
    def bind_events(self):
        """ربط الأحداث"""
        # النقر المزدوج على النتيجة
        self.results_tree.bind("<Double-1>", lambda e: self.preview_clip())
        
        # إغلاق النافذة
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)

        # ربط اختصارات لوحة المفاتيح
        self.setup_keyboard_shortcuts()
    
    def browse_video(self):
        """تصفح واختيار ملف الفيديو"""
        try:
            file_path = filedialog.askopenfilename(
                title="اختر ملف البث المباشر",
                filetypes=[
                    ("ملفات الفيديو", "*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm"),
                    ("جميع الملفات", "*.*")
                ]
            )
            
            if file_path:
                # التحقق من صحة الملف
                is_valid, message = self.video_validator.validate_video_file(file_path)
                if not is_valid:
                    messagebox.showerror("خطأ", f"ملف غير صحيح: {message}")
                    return
                
                self.current_video_path = file_path
                self.current_video_url = None
                self.video_path_var.set(file_path)
                self.load_video_info()
                self.update_status(f"تم تحميل: {Path(file_path).name}")

        except Exception as e:
            logger.error(f"خطأ في اختيار الفيديو: {e}")
            messagebox.showerror("خطأ", f"لا يمكن فتح الفيديو: {e}")

    def paste_url(self):
        """لصق رابط من الحافظة"""
        try:
            # الحصول على محتوى الحافظة
            clipboard_content = self.window.clipboard_get()

            if clipboard_content:
                # تنظيف الرابط
                url = clipboard_content.strip()

                # التحقق من صحة الرابط
                if hasattr(self, 'youtube_downloader') and self.youtube_downloader.is_youtube_url(url):
                    self.video_path_var.set(url)
                    self.update_status("تم لصق رابط YouTube")

                    # عرض معاينة سريعة للرابط
                    if "youtube.com" in url or "youtu.be" in url:
                        # استخراج معرف الفيديو للمعاينة
                        video_id = self.extract_video_id(url)
                        if video_id:
                            self.update_status(f"تم لصق رابط فيديو: {video_id}")

                    # تحديث التلميح
                    self.hints_label.config(text=f"💡 تم لصق الرابط - اضغط 'تحميل' للمتابعة")

                elif url.startswith(('http://', 'https://')):
                    messagebox.showwarning("تحذير", "الرابط المنسوخ ليس من YouTube صحيح")
                else:
                    # إذا لم يكن رابط، قد يكون نص عادي
                    self.video_path_var.set(url)
                    self.hints_label.config(text="⚠️ تأكد من أن هذا رابط YouTube صحيح")
            else:
                messagebox.showinfo("معلومات", "الحافظة فارغة")

        except tk.TclError:
            messagebox.showwarning("تحذير", "لا يمكن الوصول للحافظة - تأكد من وجود محتوى منسوخ")
        except AttributeError as e:
            logger.error(f"خطأ في الوصول للمكونات: {e}")
            messagebox.showerror("خطأ", "خطأ في تهيئة المكونات")
        except Exception as e:
            logger.error(f"خطأ في لصق الرابط: {e}")
            messagebox.showerror("خطأ", f"خطأ في لصق الرابط: {e}")

    def extract_video_id(self, url: str) -> Optional[str]:
        """استخراج معرف الفيديو من رابط YouTube"""
        import re

        patterns = [
            r'(?:youtube\.com/watch\?v=|youtu\.be/|youtube\.com/live/)([a-zA-Z0-9_-]{11})',
            r'youtube\.com/embed/([a-zA-Z0-9_-]{11})',
        ]

        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)

        return None

    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""

        # ربط Ctrl+V للصق في حقل الرابط
        def on_ctrl_v(event):
            try:
                if self.input_type_var.get() == "url" and event.widget == self.video_path_entry:
                    self.paste_url()
                    return "break"  # منع السلوك الافتراضي
            except Exception as e:
                logger.error(f"خطأ في اختصار Ctrl+V: {e}")

        # ربط Enter لبدء التحميل أو التصفح
        def on_enter(event):
            try:
                if event.widget == self.video_path_entry:
                    if self.input_type_var.get() == "url":
                        url = self.video_path_var.get().strip()
                        if url:
                            self.download_from_url()
                        else:
                            messagebox.showinfo("معلومات", "يرجى إدخال رابط YouTube أولاً")
                    else:
                        self.browse_video()
                    return "break"
            except Exception as e:
                logger.error(f"خطأ في اختصار Enter: {e}")

        # ربط الاختصارات
        try:
            self.video_path_entry.bind("<Control-v>", on_ctrl_v)
            self.video_path_entry.bind("<Return>", on_enter)

            # ربط F5 لإعادة تحميل معلومات الفيديو
            def on_f5(event):
                try:
                    if hasattr(self, 'current_video_path') and self.current_video_path:
                        self.load_video_info()
                        self.update_status("تم إعادة تحميل معلومات الفيديو")
                except Exception as e:
                    logger.error(f"خطأ في اختصار F5: {e}")

            self.window.bind("<F5>", on_f5)

            # ربط Escape لإلغاء العمليات الجارية
            def on_escape(event):
                try:
                    if hasattr(self, 'is_downloading') and self.is_downloading:
                        response = messagebox.askyesno("تأكيد", "هل تريد إلغاء التحميل الجاري؟")
                        if response:
                            self.is_downloading = False
                            if hasattr(self, 'download_button'):
                                self.download_button.config(state="normal", text="📥 تحميل")
                            self.update_status("تم إلغاء التحميل")
                except Exception as e:
                    logger.error(f"خطأ في اختصار Escape: {e}")

            self.window.bind("<Escape>", on_escape)

        except Exception as e:
            logger.error(f"خطأ في إعداد اختصارات لوحة المفاتيح: {e}")

    def on_input_type_change(self):
        """تغيير نوع الإدخال (ملف أو رابط)"""
        input_type = self.input_type_var.get()

        if input_type == "file":
            self.path_label.config(text="مسار الفيديو:")
            self.video_path_entry.config(state="readonly")
            self.browse_button.pack(side=tk.RIGHT, padx=(5, 0))
            self.paste_button.pack_forget()
            self.download_button.pack_forget()
            self.video_path_var.set("")
            self.hints_label.config(text="💡 تلميح: اضغط 'تصفح...' لاختيار ملف فيديو من جهازك")

        elif input_type == "url":
            self.path_label.config(text="رابط YouTube:")
            self.video_path_entry.config(state="normal")
            self.browse_button.pack_forget()
            self.paste_button.pack(side=tk.RIGHT, padx=(5, 0))
            self.download_button.pack(side=tk.RIGHT, padx=(5, 0))
            self.video_path_var.set("")
            self.hints_label.config(text="💡 تلميح: الصق رابط YouTube أو اضغط 'لصق' (Ctrl+V) ثم 'تحميل'")

    def download_from_url(self):
        """تحميل فيديو من رابط YouTube"""

        # التحقق من توفر محمل YouTube
        if not self.youtube_downloader:
            messagebox.showerror("خطأ", "محمل YouTube غير متوفر. تأكد من تثبيت مكتبة yt-dlp")
            return

        url = self.video_path_var.get().strip()

        if not url:
            messagebox.showerror("خطأ", "يرجى إدخال رابط YouTube")
            return

        if not self.youtube_downloader.is_youtube_url(url):
            messagebox.showerror("خطأ", "الرابط ليس من YouTube صحيح")
            return

        # بدء التحميل في thread منفصل
        self.is_downloading = True
        self.download_button.config(state="disabled", text="جاري جلب المعلومات...")

        # إنشاء نافذة تحميل بسيطة لجلب المعلومات
        loading_window = self.create_loading_window("جلب معلومات الفيديو")

        def download_thread():
            try:
                # تحديث حالة التحميل
                self.window.after(0, lambda: loading_window['status_label'].config(text="جاري الاتصال بـ YouTube..."))

                # الحصول على معلومات الفيديو أولاً
                video_info = self.youtube_downloader.get_video_info(url)

                if not video_info:
                    self.window.after(0, lambda: loading_window['window'].destroy())
                    self.window.after(0, lambda: messagebox.showerror("خطأ", "لا يمكن الحصول على معلومات الفيديو"))
                    return

                # إغلاق نافذة التحميل
                self.window.after(0, lambda: loading_window['window'].destroy())

                # عرض معلومات الفيديو
                self.window.after(0, lambda: self.show_video_info_dialog(video_info, url))

            except Exception as e:
                self.window.after(0, lambda: loading_window['window'].destroy())
                self.window.after(0, lambda: messagebox.showerror("خطأ", f"خطأ في تحميل الفيديو: {e}"))
            finally:
                self.is_downloading = False
                self.window.after(0, lambda: self.download_button.config(state="normal", text="📥 تحميل"))

        threading.Thread(target=download_thread, daemon=True).start()

    def show_video_info_dialog(self, video_info: Dict[str, Any], url: str):
        """عرض معلومات الفيديو وتأكيد التحميل"""

        dialog = tk.Toplevel(self.window)
        dialog.title("معلومات الفيديو")
        dialog.geometry("500x400")
        dialog.transient(self.window)
        dialog.grab_set()

        # إطار المعلومات
        info_frame = ttk.Frame(dialog)
        info_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # عنوان الفيديو
        ttk.Label(info_frame, text="العنوان:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        title_text = tk.Text(info_frame, height=2, wrap=tk.WORD)
        title_text.insert(tk.END, video_info.get('title', 'غير معروف'))
        title_text.config(state=tk.DISABLED)
        title_text.pack(fill=tk.X, pady=(0, 10))

        # معلومات إضافية
        info_text = f"""القناة: {video_info.get('uploader', 'غير معروف')}
المدة: {self.format_duration(video_info.get('duration', 0))}
المشاهدات: {video_info.get('view_count', 0):,}
الدقة: {video_info.get('resolution', 'غير معروف')}
حالة البث: {'مباشر' if video_info.get('is_live') else 'مسجل'}"""

        info_label = ttk.Label(info_frame, text=info_text, justify=tk.LEFT)
        info_label.pack(anchor=tk.W, pady=(0, 20))

        # أزرار التحكم
        buttons_frame = ttk.Frame(info_frame)
        buttons_frame.pack(fill=tk.X)

        def confirm_download():
            dialog.destroy()
            self.start_actual_download(url, video_info)

        def cancel_download():
            dialog.destroy()

        ttk.Button(buttons_frame, text="تحميل", command=confirm_download).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(buttons_frame, text="إلغاء", command=cancel_download).pack(side=tk.RIGHT)

    def start_actual_download(self, url: str, video_info: Dict[str, Any]):
        """بدء التحميل الفعلي"""

        self.is_downloading = True
        self.download_button.config(state="disabled", text="جاري التحميل...")

        # إنشاء نافذة التقدم المحسنة
        progress_window = self.create_progress_window("تحميل الفيديو")
        progress_bar = progress_window['progress_bar']
        status_label = progress_window['status_label']
        window = progress_window['window']

        # تحديث معلومات الفيديو في النافذة
        video_title = video_info.get('title', 'غير معروف')
        duration = self.format_duration(video_info.get('duration', 0))
        uploader = video_info.get('uploader', 'غير معروف')
        resolution = video_info.get('resolution', 'غير معروف')

        self.window.after(0, lambda: progress_window['video_title_label'].config(
            text=f"العنوان: {video_title}"
        ))

        self.window.after(0, lambda: progress_window['video_info_label'].config(
            text=f"القناة: {uploader} | المدة: {duration} | الدقة: {resolution}"
        ))

        # ربط زر الإلغاء
        cancel_requested = tk.BooleanVar(value=False)

        def cancel_download():
            if messagebox.askyesno("تأكيد الإلغاء", "هل تريد إلغاء التحميل؟", parent=window):
                cancel_requested.set(True)
                progress_window['cancel_button'].config(state="disabled", text="جاري الإلغاء...")
                status_label.config(text="جاري إلغاء التحميل...")

        progress_window['cancel_button'].config(command=cancel_download)

        def progress_callback(progress_info):
            """تحديث شريط التقدم المحسن"""
            percentage = progress_info.get('percentage', 0)
            status = progress_info.get('status', 'downloading')

            # تحديث شريط التقدم والنسبة المئوية
            self.window.after(0, lambda: progress_bar.config(value=percentage))
            self.window.after(0, lambda: progress_window['percentage_label'].config(text=f"{percentage:.1f}%"))

            if status == 'downloading':
                # تحديث السرعة
                speed = progress_info.get('speed', 0)
                if speed:
                    speed_mb = speed / (1024 * 1024)
                    speed_text = f"السرعة: {speed_mb:.1f} MB/s"
                else:
                    speed_text = "السرعة: --"

                self.window.after(0, lambda: progress_window['speed_label'].config(text=speed_text))

                # تحديث الحجم
                downloaded = progress_info.get('downloaded_bytes', 0)
                total = progress_info.get('total_bytes', 0)

                if downloaded and total:
                    downloaded_mb = downloaded / (1024 * 1024)
                    total_mb = total / (1024 * 1024)
                    size_text = f"الحجم: {downloaded_mb:.1f}/{total_mb:.1f} MB"
                elif downloaded:
                    downloaded_mb = downloaded / (1024 * 1024)
                    size_text = f"الحجم: {downloaded_mb:.1f} MB"
                else:
                    size_text = "الحجم: --"

                self.window.after(0, lambda: progress_window['size_label'].config(text=size_text))

                # تحديث الوقت المتبقي
                eta = progress_info.get('eta', 0)
                if eta and eta > 0:
                    eta_minutes = eta // 60
                    eta_seconds = eta % 60
                    if eta_minutes > 0:
                        eta_text = f"الوقت المتبقي: {eta_minutes:.0f}:{eta_seconds:02.0f}"
                    else:
                        eta_text = f"الوقت المتبقي: {eta_seconds:.0f}s"
                else:
                    eta_text = "الوقت المتبقي: --"

                self.window.after(0, lambda: progress_window['eta_label'].config(text=eta_text))

                # تحديث الحالة
                status_text = f"جاري التحميل... {percentage:.1f}%"
                self.window.after(0, lambda: status_label.config(text=status_text))

            elif status == 'finished':
                self.window.after(0, lambda: status_label.config(text="تم التحميل بنجاح! ✅"))
                self.window.after(0, lambda: progress_window['speed_label'].config(text="السرعة: مكتمل"))
                self.window.after(0, lambda: progress_window['eta_label'].config(text="الوقت المتبقي: 0s"))

        def download_thread():
            try:
                # فحص الإلغاء قبل البدء
                if cancel_requested.get():
                    return

                # تحديد اسم ملف مخصص
                safe_title = self.youtube_downloader._sanitize_filename(video_info.get('title', 'video'))
                custom_filename = f"livestream_{safe_title}"

                # تحديث الحالة
                self.window.after(0, lambda: status_label.config(text="بدء التحميل..."))

                # بدء التحميل مع فحص الإلغاء
                downloaded_file = None

                # محاولة التحميل مع فحص دوري للإلغاء
                try:
                    downloaded_file = self.youtube_downloader.download_video(
                        url,
                        progress_callback=progress_callback,
                        custom_filename=custom_filename
                    )
                except Exception as download_error:
                    if cancel_requested.get():
                        self.window.after(0, lambda: status_label.config(text="تم إلغاء التحميل"))
                        return
                    else:
                        raise download_error

                # فحص الإلغاء بعد التحميل
                if cancel_requested.get():
                    # حذف الملف المحمل جزئياً إذا وجد
                    if downloaded_file and Path(downloaded_file).exists():
                        try:
                            Path(downloaded_file).unlink()
                        except:
                            pass
                    self.window.after(0, lambda: status_label.config(text="تم إلغاء التحميل"))
                    return

                if downloaded_file and Path(downloaded_file).exists():
                    # تحديث المسار
                    self.current_video_path = downloaded_file
                    self.current_video_url = url

                    # حفظ الفيديو في التاريخ
                    try:
                        video_info_for_history = self.youtube_downloader.get_video_info(url)
                        if video_info_for_history:
                            video_id = self.analyzer.save_video_to_history(
                                title=video_info_for_history.get('title', 'فيديو بدون عنوان'),
                                url=url,
                                file_path=downloaded_file,
                                duration=video_info_for_history.get('duration', 0),
                                info_file_path=None
                            )
                            logger.info(f"تم حفظ الفيديو في التاريخ: {video_id}")
                    except Exception as e:
                        logger.warning(f"فشل في حفظ الفيديو في التاريخ: {e}")

                    # تحديث الواجهة
                    self.window.after(0, lambda: self.video_path_var.set(downloaded_file))
                    self.window.after(0, lambda: self.load_video_info())

                    # إغلاق نافذة التقدم أولاً
                    self.window.after(0, lambda: window.destroy())

                    # عرض رسالة النجاح
                    file_size = Path(downloaded_file).stat().st_size / (1024 * 1024)
                    success_message = f"تم تحميل الفيديو بنجاح! ✅\n\nالملف: {Path(downloaded_file).name}\nالحجم: {file_size:.1f} MB\nالمسار: {downloaded_file}\n\n💡 يمكنك الآن الوصول إليه من 'إدارة الفيديوهات'"
                    self.window.after(0, lambda: messagebox.showinfo("نجح التحميل", success_message))

                else:
                    self.window.after(0, lambda: messagebox.showerror("خطأ", "فشل في تحميل الفيديو"))

            except Exception as e:
                if not cancel_requested.get():
                    error_message = f"خطأ في التحميل: {str(e)}"
                    self.window.after(0, lambda: messagebox.showerror("خطأ", error_message))
                    self.window.after(0, lambda: status_label.config(text=f"فشل التحميل: {str(e)}"))
            finally:
                self.is_downloading = False
                if not cancel_requested.get():
                    self.window.after(0, lambda: window.destroy())
                self.window.after(0, lambda: self.download_button.config(state="normal", text="📥 تحميل"))

        threading.Thread(target=download_thread, daemon=True).start()

    def format_duration(self, seconds: int) -> str:
        """تنسيق المدة بالثواني إلى نص مقروء"""
        if seconds <= 0:
            return "غير معروف"

        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60

        if hours > 0:
            return f"{hours}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes}:{seconds:02d}"

    def create_progress_window(self, title: str) -> Dict[str, Any]:
        """إنشاء نافذة شريط التقدم المحسنة"""

        window = tk.Toplevel(self.window)
        window.title(title)
        window.geometry("500x350")
        window.transient(self.window)
        window.grab_set()
        window.resizable(False, False)

        # توسيط النافذة
        window.update_idletasks()
        x = (window.winfo_screenwidth() // 2) - (500 // 2)
        y = (window.winfo_screenheight() // 2) - (350 // 2)
        window.geometry(f"500x350+{x}+{y}")

        # منع إغلاق النافذة
        window.protocol("WM_DELETE_WINDOW", lambda: None)

        # الإطار الرئيسي
        main_frame = ttk.Frame(window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # عنوان العملية
        title_label = ttk.Label(main_frame, text=title, font=("Arial", 12, "bold"))
        title_label.pack(pady=(0, 15))

        # معلومات الفيديو
        info_frame = ttk.LabelFrame(main_frame, text="معلومات الفيديو", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 15))

        video_title_label = ttk.Label(info_frame, text="العنوان: جاري التحميل...", wraplength=450)
        video_title_label.pack(anchor=tk.W, pady=(0, 5))

        video_info_label = ttk.Label(info_frame, text="المعلومات: جاري جلب البيانات...")
        video_info_label.pack(anchor=tk.W)

        # إطار التقدم
        progress_frame = ttk.LabelFrame(main_frame, text="تقدم التحميل", padding="10")
        progress_frame.pack(fill=tk.X, pady=(0, 15))

        # شريط التقدم الرئيسي
        progress_bar = ttk.Progressbar(progress_frame, mode='determinate', maximum=100)
        progress_bar.pack(fill=tk.X, pady=(0, 10))

        # تسمية النسبة المئوية
        percentage_label = ttk.Label(progress_frame, text="0%", font=("Arial", 10, "bold"))
        percentage_label.pack(pady=(0, 5))

        # تفاصيل التحميل
        details_frame = ttk.Frame(progress_frame)
        details_frame.pack(fill=tk.X)

        # السرعة والحجم
        speed_label = ttk.Label(details_frame, text="السرعة: --")
        speed_label.pack(side=tk.LEFT)

        size_label = ttk.Label(details_frame, text="الحجم: --")
        size_label.pack(side=tk.RIGHT)

        # الوقت المتبقي
        eta_label = ttk.Label(progress_frame, text="الوقت المتبقي: --")
        eta_label.pack(pady=(5, 0))

        # تسمية الحالة
        status_label = ttk.Label(main_frame, text="جاري التحضير...", font=("Arial", 9))
        status_label.pack(pady=(0, 10))

        # إطار الأزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)

        # زر الإلغاء
        cancel_button = ttk.Button(buttons_frame, text="إلغاء", state="normal")
        cancel_button.pack(side=tk.RIGHT)

        # زر إخفاء/إظهار التفاصيل
        details_button = ttk.Button(buttons_frame, text="إخفاء التفاصيل")
        details_button.pack(side=tk.LEFT)

        # متغير لتتبع حالة التفاصيل
        details_visible = tk.BooleanVar(value=True)

        def toggle_details():
            if details_visible.get():
                info_frame.pack_forget()
                details_frame.pack_forget()
                eta_label.pack_forget()
                window.geometry("500x200")
                details_button.config(text="إظهار التفاصيل")
                details_visible.set(False)
            else:
                info_frame.pack(fill=tk.X, pady=(0, 15), before=progress_frame)
                details_frame.pack(fill=tk.X, in_=progress_frame, after=percentage_label)
                eta_label.pack(pady=(5, 0), in_=progress_frame)
                window.geometry("500x350")
                details_button.config(text="إخفاء التفاصيل")
                details_visible.set(True)

        details_button.config(command=toggle_details)

        return {
            'window': window,
            'progress_bar': progress_bar,
            'status_label': status_label,
            'percentage_label': percentage_label,
            'speed_label': speed_label,
            'size_label': size_label,
            'eta_label': eta_label,
            'video_title_label': video_title_label,
            'video_info_label': video_info_label,
            'cancel_button': cancel_button,
            'details_visible': details_visible
        }

    def create_loading_window(self, title: str) -> Dict[str, Any]:
        """إنشاء نافذة تحميل بسيطة"""

        window = tk.Toplevel(self.window)
        window.title(title)
        window.geometry("400x150")
        window.transient(self.window)
        window.grab_set()
        window.resizable(False, False)

        # توسيط النافذة
        window.update_idletasks()
        x = (window.winfo_screenwidth() // 2) - (400 // 2)
        y = (window.winfo_screenheight() // 2) - (150 // 2)
        window.geometry(f"400x150+{x}+{y}")

        # الإطار الرئيسي
        main_frame = ttk.Frame(window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # أيقونة التحميل
        loading_label = ttk.Label(main_frame, text="⏳", font=("Arial", 24))
        loading_label.pack(pady=(0, 10))

        # نص الحالة
        status_label = ttk.Label(main_frame, text="جاري التحميل...", font=("Arial", 10))
        status_label.pack(pady=(0, 10))

        # شريط تقدم غير محدد
        progress_bar = ttk.Progressbar(main_frame, mode='indeterminate')
        progress_bar.pack(fill=tk.X, pady=(0, 10))
        progress_bar.start(10)  # بدء الحركة

        return {
            'window': window,
            'status_label': status_label,
            'progress_bar': progress_bar
        }

    def load_video_info(self):
        """تحميل معلومات الفيديو"""
        if not self.current_video_path:
            return
        
        try:
            video_info = self.analyzer.video_processor.get_video_info(self.current_video_path)
            
            duration_hours = video_info['duration'] / 3600
            file_size_mb = video_info['size'] / (1024 * 1024)
            
            info_text = f"""📹 الملف: {Path(self.current_video_path).name}
⏱️ المدة: {duration_hours:.1f} ساعة ({video_info['duration']:.0f} ثانية)
💾 الحجم: {file_size_mb:.1f} MB
📐 الدقة: {video_info['video']['width']}x{video_info['video']['height']}
🎬 معدل الإطارات: {video_info['video']['fps']:.1f} fps"""
            
            self.video_info_text.config(state=tk.NORMAL)
            self.video_info_text.delete(1.0, tk.END)
            self.video_info_text.insert(1.0, info_text)
            self.video_info_text.config(state=tk.DISABLED)
            
            # تفعيل زر التحليل
            self.analyze_button.config(state=tk.NORMAL)
            
        except Exception as e:
            logger.error(f"خطأ في تحميل معلومات الفيديو: {e}")
            messagebox.showerror("خطأ", f"لا يمكن قراءة معلومات الفيديو: {e}")
    
    def start_analysis(self):
        """بدء تحليل البث"""
        if not self.current_video_path:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف فيديو أولاً")
            return
        
        if self.is_analyzing:
            messagebox.showinfo("معلومات", "التحليل جاري بالفعل")
            return
        
        # تحديث واجهة المستخدم
        self.is_analyzing = True
        self.analyze_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress_var.set(0)
        self.update_status("بدء التحليل...")
        
        # مسح النتائج السابقة
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        # بدء التحليل في خيط منفصل
        analysis_thread = threading.Thread(target=self.run_analysis)
        analysis_thread.daemon = True
        analysis_thread.start()
    
    def run_analysis(self):
        """تشغيل التحليل في خيط منفصل"""
        try:
            # الحصول على الإعدادات
            target_clips = self.clips_count_var.get()
            
            # تحديث التقدم
            self.window.after(0, lambda: self.update_progress(10, "تحليل البث..."))
            
            # تشغيل التحليل المتقدم
            results = self.analyzer.analyze_long_livestream_advanced(
                self.current_video_path, target_clips
            )
            
            # تحديث التقدم
            self.window.after(0, lambda: self.update_progress(90, "معالجة النتائج..."))
            
            # حفظ النتائج
            self.analysis_results = results
            
            # تحديث واجهة المستخدم
            self.window.after(0, self.analysis_completed)
            
        except Exception as e:
            logger.error(f"خطأ في التحليل: {e}")
            self.window.after(0, lambda: self.analysis_failed(str(e)))
    
    def analysis_completed(self):
        """انتهاء التحليل بنجاح"""
        self.is_analyzing = False
        self.analyze_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress_var.set(100)
        
        # عرض النتائج
        self.display_results()
        
        self.update_status(f"تم التحليل بنجاح - تم العثور على {len(self.analysis_results)} مقطع")
        
        if self.analysis_results:
            messagebox.showinfo("نجح", 
                              f"تم التحليل بنجاح!\nتم العثور على {len(self.analysis_results)} مقطع مثير.")
        else:
            messagebox.showwarning("تحذير", "لم يتم العثور على مقاطع مثيرة في هذا البث.")
    
    def analysis_failed(self, error_message: str):
        """فشل التحليل"""
        self.is_analyzing = False
        self.analyze_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress_var.set(0)
        self.update_status("فشل التحليل")
        
        messagebox.showerror("خطأ", f"فشل في التحليل:\n{error_message}")
    
    def display_results(self):
        """عرض النتائج في الجدول"""
        for result in self.analysis_results:
            # تحويل الوقت إلى تنسيق قابل للقراءة
            # التحقق من وجود المفتاح المناسب
            start_time = result.get('original_start_time', result.get('start_time', 0))
            start_minutes = int(start_time // 60)
            start_seconds = int(start_time % 60)
            time_str = f"{start_minutes:02d}:{start_seconds:02d}"

            # تحويل النوع إلى عربي
            type_map = {
                'exciting': '🔥 مثير',
                'funny': '😂 مضحك',
                'shocking': '😱 صادم'
            }
            type_str = type_map.get(result.get('type', 'unknown'), result.get('type', 'غير محدد'))

            # حساب المدة مع حماية من الأخطاء
            duration = result.get('duration')
            if duration is None:
                # حساب المدة من start_time و end_time
                end_time = result.get('original_end_time', result.get('end_time', start_time + 30))
                duration = end_time - start_time

            duration_str = f"{duration:.1f}s" if duration is not None else "غير محدد"

            # إدراج في الجدول
            self.results_tree.insert("", tk.END, values=(
                type_str,
                time_str,
                duration_str,
                f"{result.get('confidence', 0)*100:.0f}%",
                result.get('description', 'لا يوجد وصف')[:50] + "..." if len(result.get('description', '')) > 50 else result.get('description', 'لا يوجد وصف')
            ))
    
    def update_progress(self, value: float, message: str):
        """تحديث شريط التقدم"""
        self.progress_var.set(value)
        self.progress_label.config(text=message)
    
    def update_status(self, message: str):
        """تحديث شريط الحالة"""
        self.status_label.config(text=message)
    
    def stop_analysis(self):
        """إيقاف التحليل"""
        self.is_analyzing = False
        self.analyze_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress_var.set(0)
        self.update_status("تم إيقاف التحليل")
    
    def preview_clip(self):
        """معاينة المقطع المحدد"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مقطع للمعاينة")
            return
        
        # الحصول على فهرس المقطع
        item_index = self.results_tree.index(selection[0])
        if item_index < len(self.analysis_results):
            result = self.analysis_results[item_index]
            clip_path = result.get('file_path')
            
            if clip_path and Path(clip_path).exists():
                # فتح المقطع في المشغل الافتراضي
                import subprocess
                import platform
                
                try:
                    if platform.system() == "Windows":
                        subprocess.run(["start", clip_path], shell=True)
                    elif platform.system() == "Darwin":  # macOS
                        subprocess.run(["open", clip_path])
                    else:  # Linux
                        subprocess.run(["xdg-open", clip_path])
                except Exception as e:
                    messagebox.showerror("خطأ", f"لا يمكن فتح المقطع: {e}")
            else:
                messagebox.showerror("خطأ", "ملف المقطع غير موجود")
    
    def export_clip(self):
        """تصدير المقطع المحدد"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مقطع للتصدير")
            return
        
        messagebox.showinfo("قريباً", "ميزة التصدير ستكون متاحة قريباً")
    
    def copy_publish_info(self):
        """نسخ معلومات النشر للمقطع"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مقطع")
            return
        
        item_index = self.results_tree.index(selection[0])
        if item_index < len(self.analysis_results):
            result = self.analysis_results[item_index]
            
            # تجميع معلومات النشر
            publish_info = f"""العنوان: {result.get('suggested_title', 'عنوان تلقائي')}

الوصف: {result.get('description', 'وصف تلقائي')}

الهاشتاغات: {' '.join(result.get('hashtags', []))}

نوع المحتوى: {result.get('type', 'غير محدد')}
مستوى الثقة: {result.get('confidence', 0)*100:.0f}%"""
            
            # نسخ إلى الحافظة
            self.window.clipboard_clear()
            self.window.clipboard_append(publish_info)
            
            messagebox.showinfo("تم", "تم نسخ معلومات النشر إلى الحافظة")
    
    def save_all_results(self):
        """حفظ جميع النتائج"""
        if not self.analysis_results:
            messagebox.showwarning("تحذير", "لا توجد نتائج لحفظها")
            return
        
        try:
            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                title="حفظ النتائج",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("جميع الملفات", "*.*")]
            )
            
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.analysis_results, f, ensure_ascii=False, indent=2)
                
                messagebox.showinfo("تم", f"تم حفظ النتائج في: {file_path}")
                
        except Exception as e:
            logger.error(f"خطأ في حفظ النتائج: {e}")
            messagebox.showerror("خطأ", f"فشل في حفظ النتائج: {e}")
    
    def open_results_folder(self):
        """فتح مجلد النتائج"""
        try:
            import subprocess
            import platform
            
            results_dir = self.file_manager.output_dir
            
            if platform.system() == "Windows":
                subprocess.run(["explorer", str(results_dir)])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", str(results_dir)])
            else:  # Linux
                subprocess.run(["xdg-open", str(results_dir)])
                
        except Exception as e:
            logger.error(f"خطأ في فتح مجلد النتائج: {e}")
            messagebox.showerror("خطأ", f"لا يمكن فتح مجلد النتائج: {e}")
    
    def on_closing(self):
        """عند إغلاق النافذة"""
        if self.is_analyzing:
            if messagebox.askokcancel("تأكيد", "التحليل جاري. هل تريد إيقافه والخروج؟"):
                self.stop_analysis()
                self.window.destroy()
        else:
            self.window.destroy()
