"""
مدير الذكاء الاصطناعي الرئيسي
Main AI manager for coordinating AI services
"""

import logging
from typing import Optional, Dict, List, Any, Tuple
from pathlib import Path
import asyncio
import concurrent.futures

from config.settings import AISettings
from ai.huggingface_client import HuggingFaceClient
from ai.google_cloud_client import GoogleCloudClient
from core.video_processor import VideoProcessor
from utils.file_utils import FileManager

logger = logging.getLogger(__name__)

class AIManager:
    """مدير الذكاء الاصطناعي الرئيسي"""
    
    def __init__(self):
        self.hf_client = HuggingFaceClient()
        self.gc_client = GoogleCloudClient()
        self.video_processor = VideoProcessor()
        self.file_manager = FileManager()
        
        # حالة الخدمات
        self.services_status = {
            "huggingface": self.hf_client.api_key is not None,
            "google_cloud": self.gc_client.is_available()
        }
        
        logger.info(f"تم تهيئة مدير الذكاء الاصطناعي - الخدمات المتاحة: {self.services_status}")
    
    def get_services_status(self) -> Dict[str, bool]:
        """الحصول على حالة الخدمات"""
        return self.services_status.copy()
    
    def auto_clip_video(self, video_path: str, target_duration: float = 60.0, 
                       num_clips: int = 3) -> Optional[List[Dict]]:
        """القص التلقائي للفيديو بناءً على تحليل المحتوى"""
        try:
            logger.info(f"بدء القص التلقائي للفيديو: {video_path}")
            
            # الحصول على معلومات الفيديو
            video_info = self.video_processor.get_video_info(video_path)
            if not video_info:
                logger.error("لا يمكن الحصول على معلومات الفيديو")
                return None
            
            video_duration = video_info['duration']
            if video_duration < target_duration:
                logger.warning("مدة الفيديو أقل من المدة المطلوبة للمقطع")
                return None
            
            clips = []
            
            # استراتيجية 1: استخدام Google Cloud لتحليل المحتوى
            if self.services_status["google_cloud"]:
                clips.extend(self._auto_clip_with_google_cloud(
                    video_path, target_duration, num_clips
                ))
            
            # استراتيجية 2: استخدام تحليل الصوت
            if len(clips) < num_clips:
                clips.extend(self._auto_clip_with_audio_analysis(
                    video_path, target_duration, num_clips - len(clips)
                ))
            
            # استراتيجية 3: تقسيم متساوي كخيار احتياطي
            if len(clips) < num_clips:
                clips.extend(self._auto_clip_equal_segments(
                    video_path, target_duration, num_clips - len(clips)
                ))
            
            logger.info(f"تم إنشاء {len(clips)} مقطع تلقائياً")
            return clips[:num_clips]
            
        except Exception as e:
            logger.error(f"خطأ في القص التلقائي: {e}")
            return None
    
    def _auto_clip_with_google_cloud(self, video_path: str, target_duration: float, 
                                   num_clips: int) -> List[Dict]:
        """القص التلقائي باستخدام Google Cloud"""
        clips = []
        
        try:
            # تحليل المحتوى
            analysis = self.gc_client.analyze_video_content(video_path)
            if not analysis:
                return clips
            
            # استخدام تغييرات المشاهد لتحديد نقاط القص
            shots = analysis.get("shots", [])
            if not shots:
                return clips
            
            # اختيار أفضل المشاهد بناءً على التسميات
            labels = analysis.get("labels", [])
            interesting_segments = self._find_interesting_segments(shots, labels)
            
            # إنشاء المقاطع
            for i, segment in enumerate(interesting_segments[:num_clips]):
                start_time = segment["start_time"]
                end_time = min(start_time + target_duration, segment["end_time"])
                
                clip_info = {
                    "start_time": start_time,
                    "end_time": end_time,
                    "duration": end_time - start_time,
                    "confidence": segment.get("confidence", 0.5),
                    "method": "google_cloud_analysis",
                    "description": f"مقطع تلقائي {i+1} - تحليل ذكي"
                }
                clips.append(clip_info)
            
        except Exception as e:
            logger.error(f"خطأ في القص باستخدام Google Cloud: {e}")
        
        return clips
    
    def _auto_clip_with_audio_analysis(self, video_path: str, target_duration: float, 
                                     num_clips: int) -> List[Dict]:
        """القص التلقائي بناءً على تحليل الصوت"""
        clips = []
        
        try:
            # استخراج الصوت
            audio_path = self.file_manager.create_temp_file(suffix='.mp3')
            success = self.video_processor.extract_audio(video_path, str(audio_path))
            
            if not success:
                return clips
            
            # تحليل الصوت للعثور على فترات الصمت
            silence_segments = self._detect_silence_segments(str(audio_path))
            
            # إنشاء مقاطع بناءً على فترات الصمت
            video_info = self.video_processor.get_video_info(video_path)
            video_duration = video_info['duration']
            
            segment_duration = video_duration / (num_clips + 1)
            
            for i in range(num_clips):
                start_time = i * segment_duration
                end_time = min(start_time + target_duration, video_duration)
                
                # تعديل نقاط القص لتجنب قطع الكلام
                start_time, end_time = self._adjust_cut_points(
                    start_time, end_time, silence_segments
                )
                
                clip_info = {
                    "start_time": start_time,
                    "end_time": end_time,
                    "duration": end_time - start_time,
                    "confidence": 0.6,
                    "method": "audio_analysis",
                    "description": f"مقطع تلقائي {i+1} - تحليل صوتي"
                }
                clips.append(clip_info)
            
            # تنظيف الملف المؤقت
            audio_path.unlink(missing_ok=True)
            
        except Exception as e:
            logger.error(f"خطأ في القص بتحليل الصوت: {e}")
        
        return clips
    
    def _auto_clip_equal_segments(self, video_path: str, target_duration: float, 
                                num_clips: int) -> List[Dict]:
        """القص التلقائي بتقسيم متساوي"""
        clips = []
        
        try:
            video_info = self.video_processor.get_video_info(video_path)
            video_duration = video_info['duration']
            
            # حساب المسافة بين المقاطع
            available_duration = video_duration - (num_clips * target_duration)
            if available_duration < 0:
                # تقليل مدة المقاطع
                target_duration = video_duration / num_clips * 0.8
                available_duration = video_duration - (num_clips * target_duration)
            
            gap = available_duration / (num_clips + 1) if num_clips > 0 else 0
            
            for i in range(num_clips):
                start_time = gap + i * (target_duration + gap)
                end_time = start_time + target_duration
                
                clip_info = {
                    "start_time": start_time,
                    "end_time": end_time,
                    "duration": target_duration,
                    "confidence": 0.3,
                    "method": "equal_segments",
                    "description": f"مقطع تلقائي {i+1} - تقسيم متساوي"
                }
                clips.append(clip_info)
            
        except Exception as e:
            logger.error(f"خطأ في التقسيم المتساوي: {e}")
        
        return clips
    
    def _find_interesting_segments(self, shots: List[Dict], labels: List[Dict]) -> List[Dict]:
        """العثور على المقاطع المثيرة للاهتمام"""
        interesting_segments = []
        
        # كلمات مفتاحية تدل على محتوى مثير
        interesting_keywords = [
            "person", "face", "speech", "music", "action", "movement",
            "شخص", "وجه", "كلام", "موسيقى", "حركة", "نشاط"
        ]
        
        for shot in shots:
            confidence = 0.3  # ثقة أساسية
            
            # البحث عن تسميات مثيرة في هذا المقطع
            shot_start = shot["start_time"]
            shot_end = shot["end_time"]
            
            for label in labels:
                for segment in label["segments"]:
                    # التحقق من التداخل الزمني
                    if (segment["start_time"] <= shot_end and 
                        segment["end_time"] >= shot_start):
                        
                        # التحقق من الكلمات المفتاحية
                        description = label["description"].lower()
                        if any(keyword in description for keyword in interesting_keywords):
                            confidence += label["confidence"] * 0.3
            
            interesting_segments.append({
                "start_time": shot_start,
                "end_time": shot_end,
                "confidence": min(confidence, 1.0)
            })
        
        # ترتيب حسب الثقة
        interesting_segments.sort(key=lambda x: x["confidence"], reverse=True)
        return interesting_segments
    
    def _detect_silence_segments(self, audio_path: str) -> List[Tuple[float, float]]:
        """كشف فترات الصمت في الصوت"""
        # هذه دالة مبسطة - يمكن تحسينها باستخدام مكتبات تحليل الصوت
        silence_segments = []
        
        try:
            # استخدام pydub لتحليل الصوت
            from pydub import AudioSegment
            from pydub.silence import detect_silence
            
            audio = AudioSegment.from_file(audio_path)
            
            # كشف فترات الصمت (أكثر من 500ms بمستوى أقل من -40dB)
            silence_ranges = detect_silence(
                audio, 
                min_silence_len=500,  # 500ms
                silence_thresh=-40    # -40dB
            )
            
            # تحويل إلى ثواني
            for start_ms, end_ms in silence_ranges:
                start_sec = start_ms / 1000.0
                end_sec = end_ms / 1000.0
                silence_segments.append((start_sec, end_sec))
            
        except ImportError:
            logger.warning("مكتبة pydub غير متوفرة لتحليل الصوت")
        except Exception as e:
            logger.error(f"خطأ في كشف فترات الصمت: {e}")
        
        return silence_segments
    
    def _adjust_cut_points(self, start_time: float, end_time: float, 
                          silence_segments: List[Tuple[float, float]]) -> Tuple[float, float]:
        """تعديل نقاط القص لتجنب قطع الكلام"""
        adjusted_start = start_time
        adjusted_end = end_time
        
        # البحث عن أقرب فترة صمت لنقطة البداية
        for silence_start, silence_end in silence_segments:
            if abs(silence_start - start_time) < 2.0:  # ضمن ثانيتين
                adjusted_start = silence_start
                break
            elif abs(silence_end - start_time) < 2.0:
                adjusted_start = silence_end
                break
        
        # البحث عن أقرب فترة صمت لنقطة النهاية
        for silence_start, silence_end in silence_segments:
            if abs(silence_start - end_time) < 2.0:
                adjusted_end = silence_start
                break
            elif abs(silence_end - end_time) < 2.0:
                adjusted_end = silence_end
                break
        
        return adjusted_start, adjusted_end
    
    def generate_auto_captions(self, video_path: str, language: str = "ar") -> Optional[List[Dict]]:
        """توليد التعليقات التوضيحية التلقائية"""
        try:
            logger.info(f"بدء توليد التعليقات التوضيحية للفيديو: {video_path}")
            
            # استخراج الصوت
            audio_path = self.file_manager.create_temp_file(suffix='.wav')
            success = self.video_processor.extract_audio(video_path, str(audio_path))
            
            if not success:
                logger.error("فشل في استخراج الصوت")
                return None
            
            # تحويل الكلام إلى نص
            transcript = None
            
            # محاولة استخدام Google Cloud أولاً
            if self.services_status["google_cloud"]:
                language_code = "ar-SA" if language == "ar" else "en-US"
                transcript = self.gc_client.transcribe_audio(str(audio_path), language_code)
            
            # محاولة استخدام Hugging Face كبديل
            if not transcript and self.services_status["huggingface"]:
                transcript = self.hf_client.speech_to_text(str(audio_path), language)
            
            if not transcript:
                logger.error("فشل في تحويل الكلام إلى نص")
                return None
            
            # تقسيم النص إلى جمل وتوقيتات
            captions = self._create_timed_captions(transcript, video_path)
            
            # تنظيف الملف المؤقت
            audio_path.unlink(missing_ok=True)
            
            logger.info(f"تم توليد {len(captions)} تعليق توضيحي")
            return captions
            
        except Exception as e:
            logger.error(f"خطأ في توليد التعليقات التوضيحية: {e}")
            return None
    
    def _create_timed_captions(self, transcript: str, video_path: str) -> List[Dict]:
        """إنشاء تعليقات توضيحية مع التوقيتات"""
        captions = []
        
        try:
            # تقسيم النص إلى جمل
            sentences = transcript.split('.')
            sentences = [s.strip() for s in sentences if s.strip()]
            
            # الحصول على مدة الفيديو
            video_info = self.video_processor.get_video_info(video_path)
            video_duration = video_info['duration']
            
            # توزيع الجمل على مدة الفيديو
            if len(sentences) > 0:
                duration_per_sentence = video_duration / len(sentences)
                
                for i, sentence in enumerate(sentences):
                    start_time = i * duration_per_sentence
                    end_time = min((i + 1) * duration_per_sentence, video_duration)
                    
                    caption = {
                        "start_time": start_time,
                        "end_time": end_time,
                        "text": sentence,
                        "duration": end_time - start_time
                    }
                    captions.append(caption)
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء التوقيتات: {e}")
        
        return captions
