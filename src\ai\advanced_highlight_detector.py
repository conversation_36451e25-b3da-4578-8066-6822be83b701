"""
نظام اكتشاف اللقطات المتقدم
Advanced Highlight Detection System

يستخدم تقنيات متقدمة للتحليل الصوتي والبصري والنصي لاكتشاف اللحظات المثيرة والمضحكة والصادمة
"""

import logging
import numpy as np
import cv2
import librosa
import re
from typing import List, Dict, Any, Tuple, Optional
from pathlib import Path
import json
import statistics
from dataclasses import dataclass
from enum import Enum

from ai.gemini_client import GeminiClient
from ai.huggingface_client import HuggingFaceClient
from core.video_processor import VideoProcessor
from utils.file_utils import FileManager
from ai.reaction_detector import ReactionDetector

logger = logging.getLogger(__name__)

class HighlightType(Enum):
    """أنواع اللقطات البارزة"""
    EXCITING = "exciting"
    FUNNY = "funny"
    SHOCKING = "shocking"
    EMOTIONAL = "emotional"
    EPIC = "epic"

@dataclass
class HighlightMoment:
    """بيانات لحظة بارزة"""
    start_time: float
    end_time: float
    highlight_type: HighlightType
    confidence: float
    text: str
    audio_features: Dict[str, float]
    visual_features: Dict[str, float]
    sentiment_score: float
    source: str
    description: str = ""

class AdvancedHighlightDetector:
    """كاشف اللقطات المتقدم"""
    
    def __init__(self):
        self.gemini_client = GeminiClient()
        self.hf_client = HuggingFaceClient()
        self.video_processor = VideoProcessor()
        self.file_manager = FileManager()
        self.reaction_detector = ReactionDetector()
        
        # إعدادات التحليل
        self.min_confidence = 0.6
        self.chunk_duration = 30  # 30 ثانية لكل قطعة تحليل
        self.overlap_duration = 5  # 5 ثواني تداخل بين القطع
        
        # أوزان التحليل المختلفة
        self.weights = {
            'audio': 0.3,
            'visual': 0.3,
            'text': 0.25,
            'sentiment': 0.15
        }
        
        # كلمات مفتاحية محسنة مع أوزان
        self.enhanced_keywords = {
            HighlightType.EXCITING: {
                'high_impact': ['wow', 'omg', 'insane', 'crazy', 'unbelievable', 'epic', 'legendary'],
                'medium_impact': ['amazing', 'incredible', 'awesome', 'sick', 'fire', 'lit'],
                'low_impact': ['nice', 'good', 'cool', 'sweet']
            },
            HighlightType.FUNNY: {
                'high_impact': ['hilarious', 'lmao', 'rofl', 'dead', 'dying'],
                'medium_impact': ['funny', 'lol', 'haha', 'comedy', 'joke'],
                'low_impact': ['amusing', 'chuckle', 'smile']
            },
            HighlightType.SHOCKING: {
                'high_impact': ['shocked', 'stunned', 'speechless', 'mind-blown'],
                'medium_impact': ['surprised', 'unexpected', 'plot twist', 'reveal'],
                'low_impact': ['interesting', 'curious', 'strange']
            }
        }
        
        # أنماط نصية متقدمة
        self.text_patterns = {
            'excitement': [
                r'\b(oh\s*my\s*god|omg|what\s*the\s*hell|wtf|no\s*way)\b',
                r'\b(let\'s\s*go|yooo+|bruh|damn|holy)\b',
                r'[!]{2,}',  # علامات تعجب متعددة
                r'[A-Z]{3,}',  # كلمات بأحرف كبيرة
            ],
            'laughter': [
                r'\b(ha){2,}\b',
                r'\b(lol|lmao|rofl)\b',
                r'[😂🤣😆😄😃]',  # رموز تعبيرية للضحك
            ],
            'shock': [
                r'\b(what|how|why)\s*[?!]+',
                r'\b(impossible|unbelievable|can\'t\s*believe)\b',
            ]
        }
        
        logger.info("تم تهيئة كاشف اللقطات المتقدم")

    def detect_highlights(self, video_path: str, target_count: int = 5) -> List[HighlightMoment]:
        """اكتشاف اللقطات البارزة في الفيديو"""
        try:
            logger.info(f"بدء اكتشاف اللقطات المتقدم: {video_path}")
            
            # الحصول على معلومات الفيديو
            video_info = self.video_processor.get_video_info(video_path)
            if not video_info:
                logger.error("لا يمكن الحصول على معلومات الفيديو")
                return []
            
            duration = video_info['duration']
            logger.info(f"مدة الفيديو: {duration/60:.1f} دقيقة")
            
            # تقسيم الفيديو إلى قطع للتحليل
            chunks = self._create_analysis_chunks(duration)
            logger.info(f"تم تقسيم الفيديو إلى {len(chunks)} قطعة للتحليل")
            
            # تحليل كل قطعة
            all_highlights = []
            for i, (start_time, end_time) in enumerate(chunks):
                logger.info(f"تحليل القطعة {i+1}/{len(chunks)}: {start_time:.1f}-{end_time:.1f}s")
                
                chunk_highlights = self._analyze_chunk_advanced(
                    video_path, start_time, end_time
                )
                all_highlights.extend(chunk_highlights)
            
            # فلترة وترتيب أفضل اللقطات
            best_highlights = self._select_best_highlights(all_highlights, target_count)

            # تحسين اللقطات باستخدام كشف ردات الفعل
            enhanced_highlights = self._enhance_highlights_with_reactions(video_path, best_highlights)

            logger.info(f"تم اكتشاف {len(enhanced_highlights)} لقطة بارزة محسنة")
            return enhanced_highlights
            
        except Exception as e:
            logger.error(f"خطأ في اكتشاف اللقطات: {e}")
            return []

    def _create_analysis_chunks(self, duration: float) -> List[Tuple[float, float]]:
        """تقسيم الفيديو إلى قطع للتحليل مع تداخل"""
        chunks = []
        current_time = 0
        
        while current_time < duration:
            end_time = min(current_time + self.chunk_duration, duration)
            chunks.append((current_time, end_time))
            current_time += self.chunk_duration - self.overlap_duration
            
        return chunks

    def _analyze_chunk_advanced(self, video_path: str, start_time: float, 
                               end_time: float) -> List[HighlightMoment]:
        """تحليل متقدم لقطعة من الفيديو"""
        try:
            highlights = []
            
            # 1. التحليل الصوتي المتقدم
            audio_features = self._analyze_audio_advanced(video_path, start_time, end_time)
            
            # 2. التحليل البصري المتقدم
            visual_features = self._analyze_visual_advanced(video_path, start_time, end_time)
            
            # 3. التحليل النصي والدلالي المتقدم
            text_analysis = self._analyze_text_advanced(video_path, start_time, end_time)
            
            # 4. دمج النتائج وحساب النقاط
            combined_highlights = self._combine_advanced_analysis(
                audio_features, visual_features, text_analysis, start_time, end_time
            )
            
            highlights.extend(combined_highlights)
            
            return highlights
            
        except Exception as e:
            logger.error(f"خطأ في التحليل المتقدم للقطعة: {e}")
            return []

    def _analyze_audio_advanced(self, video_path: str, start_time: float, 
                               end_time: float) -> Dict[str, Any]:
        """تحليل صوتي متقدم"""
        try:
            # استخراج الصوت
            audio_path = self.file_manager.create_temp_file(suffix='.wav')
            success = self._extract_chunk_audio(video_path, str(audio_path), start_time, end_time)
            
            if not success:
                return {}
            
            # تحميل الصوت باستخدام librosa
            y, sr = librosa.load(str(audio_path))
            
            features = {}
            
            # 1. تحليل الطاقة والشدة
            rms = librosa.feature.rms(y=y)[0]
            features['energy_mean'] = float(np.mean(rms))
            features['energy_std'] = float(np.std(rms))
            features['energy_peaks'] = len(librosa.util.peak_pick(rms, pre_max=3, post_max=3, pre_avg=3, post_avg=5, delta=0.1, wait=1))
            
            # 2. تحليل النبرة والتردد
            pitches, magnitudes = librosa.piptrack(y=y, sr=sr)
            pitch_values = []
            for t in range(pitches.shape[1]):
                index = magnitudes[:, t].argmax()
                pitch = pitches[index, t]
                if pitch > 0:
                    pitch_values.append(pitch)
            
            if pitch_values:
                features['pitch_mean'] = float(np.mean(pitch_values))
                features['pitch_std'] = float(np.std(pitch_values))
                features['pitch_range'] = float(max(pitch_values) - min(pitch_values))
            
            # 3. تحليل الطيف الترددي
            spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
            features['spectral_centroid_mean'] = float(np.mean(spectral_centroids))
            features['spectral_centroid_std'] = float(np.std(spectral_centroids))
            
            # 4. كشف الصمت والكلام
            intervals = librosa.effects.split(y, top_db=20)
            features['speech_ratio'] = len(intervals) / len(y) * sr if len(y) > 0 else 0
            
            # تنظيف الملف المؤقت
            audio_path.unlink(missing_ok=True)
            
            return features

        except Exception as e:
            logger.error(f"خطأ في التحليل الصوتي المتقدم: {e}")
            return {}

    def _analyze_visual_advanced(self, video_path: str, start_time: float,
                                end_time: float) -> Dict[str, Any]:
        """تحليل بصري متقدم"""
        try:
            features = {}

            # فتح الفيديو
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)

            # الانتقال إلى نقطة البداية
            cap.set(cv2.CAP_PROP_POS_FRAMES, int(start_time * fps))

            frames = []
            frame_count = 0
            max_frames = int((end_time - start_time) * fps)

            # قراءة الإطارات
            while frame_count < max_frames:
                ret, frame = cap.read()
                if not ret:
                    break

                # أخذ عينة كل ثانية
                if frame_count % int(fps) == 0:
                    frames.append(frame)

                frame_count += 1

            cap.release()

            if not frames:
                return {}

            # 1. تحليل الحركة
            motion_scores = []
            for i in range(1, len(frames)):
                # تحويل إلى رمادي
                gray1 = cv2.cvtColor(frames[i-1], cv2.COLOR_BGR2GRAY)
                gray2 = cv2.cvtColor(frames[i], cv2.COLOR_BGR2GRAY)

                # حساب الفرق
                diff = cv2.absdiff(gray1, gray2)
                motion_score = np.mean(diff)
                motion_scores.append(motion_score)

            if motion_scores:
                features['motion_mean'] = float(np.mean(motion_scores))
                features['motion_std'] = float(np.std(motion_scores))
                features['motion_peaks'] = len([s for s in motion_scores if s > np.mean(motion_scores) + 2*np.std(motion_scores)])

            # 2. تحليل الألوان والسطوع
            brightness_values = []
            color_variance_values = []

            for frame in frames:
                # السطوع
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                brightness = np.mean(gray)
                brightness_values.append(brightness)

                # تباين الألوان
                color_variance = np.var(frame)
                color_variance_values.append(color_variance)

            if brightness_values:
                features['brightness_mean'] = float(np.mean(brightness_values))
                features['brightness_std'] = float(np.std(brightness_values))
                features['brightness_changes'] = len([i for i in range(1, len(brightness_values))
                                                    if abs(brightness_values[i] - brightness_values[i-1]) > 20])

            if color_variance_values:
                features['color_variance_mean'] = float(np.mean(color_variance_values))
                features['color_variance_std'] = float(np.std(color_variance_values))

            # 3. كشف الوجوه (إذا كان متوفراً)
            try:
                face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
                face_counts = []

                for frame in frames:
                    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                    faces = face_cascade.detectMultiScale(gray, 1.1, 4)
                    face_counts.append(len(faces))

                if face_counts:
                    features['faces_detected'] = max(face_counts)
                    features['face_consistency'] = 1.0 - (np.std(face_counts) / (np.mean(face_counts) + 1))

            except Exception as e:
                logger.warning(f"تعذر تحليل الوجوه: {e}")

            return features

        except Exception as e:
            logger.error(f"خطأ في التحليل البصري المتقدم: {e}")
            return {}

    def _analyze_text_advanced(self, video_path: str, start_time: float,
                              end_time: float) -> Dict[str, Any]:
        """تحليل نصي ودلالي متقدم"""
        try:
            # استخراج النص من الصوت
            audio_path = self.file_manager.create_temp_file(suffix='.wav')
            success = self._extract_chunk_audio(video_path, str(audio_path), start_time, end_time)

            if not success:
                return {}

            # تحويل الكلام إلى نص
            transcript = self._transcribe_audio_advanced(str(audio_path))
            audio_path.unlink(missing_ok=True)

            if not transcript:
                return {}

            analysis = {}

            # 1. تحليل المشاعر باستخدام Gemini
            sentiment_analysis = self._analyze_sentiment_with_gemini(transcript)
            analysis.update(sentiment_analysis)

            # 2. تحليل الأنماط النصية
            pattern_analysis = self._analyze_text_patterns(transcript)
            analysis.update(pattern_analysis)

            # 3. تحليل الكلمات المفتاحية المحسن
            keyword_analysis = self._analyze_keywords_enhanced(transcript)
            analysis.update(keyword_analysis)

            # 4. تحليل السياق والموضوع
            context_analysis = self._analyze_context_and_topic(transcript)
            analysis.update(context_analysis)

            analysis['transcript'] = transcript
            analysis['word_count'] = len(transcript.split())

            return analysis

        except Exception as e:
            logger.error(f"خطأ في التحليل النصي المتقدم: {e}")
            return {}

    def _transcribe_audio_advanced(self, audio_path: str) -> Optional[str]:
        """تحويل الكلام إلى نص باستخدام أفضل الخدمات المتاحة"""
        try:
            # محاولة استخدام Hugging Face أولاً
            if self.hf_client.api_key:
                transcript = self.hf_client.speech_to_text(audio_path)  # إزالة معامل اللغة
                if transcript:
                    return transcript

            # محاولة استخدام خدمات أخرى إذا فشل HF
            # يمكن إضافة خدمات أخرى هنا

            return None

        except Exception as e:
            logger.error(f"خطأ في تحويل الكلام إلى نص: {e}")
            return None

    def _analyze_sentiment_with_gemini(self, text: str) -> Dict[str, Any]:
        """تحليل المشاعر باستخدام Gemini"""
        try:
            prompt = f"""
            حلل المشاعر في النص التالي وأعطني:
            1. نوع المشاعر الرئيسية (إيجابية/سلبية/محايدة)
            2. شدة المشاعر (0-1)
            3. المشاعر الفرعية (فرح، حزن، غضب، دهشة، خوف، اشمئزاز)
            4. مستوى الإثارة (0-1)

            النص: "{text}"

            أجب بتنسيق JSON فقط:
            {{
                "primary_sentiment": "positive/negative/neutral",
                "sentiment_intensity": 0.8,
                "emotions": {{
                    "joy": 0.7,
                    "surprise": 0.5,
                    "anger": 0.1,
                    "sadness": 0.0,
                    "fear": 0.0,
                    "disgust": 0.0
                }},
                "excitement_level": 0.8
            }}
            """

            response = self.gemini_client.generate_content(prompt)
            if response:
                # محاولة تحليل JSON
                try:
                    import json
                    sentiment_data = json.loads(response)
                    return {
                        'sentiment_primary': sentiment_data.get('primary_sentiment', 'neutral'),
                        'sentiment_intensity': sentiment_data.get('sentiment_intensity', 0.5),
                        'emotions': sentiment_data.get('emotions', {}),
                        'excitement_level': sentiment_data.get('excitement_level', 0.5)
                    }
                except json.JSONDecodeError:
                    logger.warning("فشل في تحليل استجابة Gemini كـ JSON")

            return {}

        except Exception as e:
            logger.error(f"خطأ في تحليل المشاعر: {e}")
            return {}

    def _analyze_text_patterns(self, text: str) -> Dict[str, Any]:
        """تحليل الأنماط النصية"""
        analysis = {}
        text_lower = text.lower()

        # تحليل أنماط الإثارة
        excitement_matches = 0
        for pattern in self.text_patterns['excitement']:
            matches = len(re.findall(pattern, text_lower, re.IGNORECASE))
            excitement_matches += matches

        # تحليل أنماط الضحك
        laughter_matches = 0
        for pattern in self.text_patterns['laughter']:
            matches = len(re.findall(pattern, text_lower, re.IGNORECASE))
            laughter_matches += matches

        # تحليل أنماط الصدمة
        shock_matches = 0
        for pattern in self.text_patterns['shock']:
            matches = len(re.findall(pattern, text_lower, re.IGNORECASE))
            shock_matches += matches

        analysis['excitement_patterns'] = excitement_matches
        analysis['laughter_patterns'] = laughter_matches
        analysis['shock_patterns'] = shock_matches

        # تحليل علامات الترقيم
        analysis['exclamation_marks'] = text.count('!')
        analysis['question_marks'] = text.count('?')
        analysis['caps_words'] = len(re.findall(r'\b[A-Z]{2,}\b', text))

        return analysis

    def _analyze_keywords_enhanced(self, text: str) -> Dict[str, Any]:
        """تحليل محسن للكلمات المفتاحية مع أوزان"""
        analysis = {}
        text_lower = text.lower()

        for highlight_type, keyword_groups in self.enhanced_keywords.items():
            total_score = 0

            # كلمات عالية التأثير (وزن 3)
            for keyword in keyword_groups.get('high_impact', []):
                count = text_lower.count(keyword)
                total_score += count * 3

            # كلمات متوسطة التأثير (وزن 2)
            for keyword in keyword_groups.get('medium_impact', []):
                count = text_lower.count(keyword)
                total_score += count * 2

            # كلمات منخفضة التأثير (وزن 1)
            for keyword in keyword_groups.get('low_impact', []):
                count = text_lower.count(keyword)
                total_score += count * 1

            analysis[f'{highlight_type.value}_keyword_score'] = total_score

        return analysis

    def _analyze_context_and_topic(self, text: str) -> Dict[str, Any]:
        """تحليل السياق والموضوع"""
        analysis = {}

        # تحليل طول النص وكثافة الكلمات
        words = text.split()
        analysis['text_density'] = len(words) / max(len(text), 1)
        analysis['avg_word_length'] = np.mean([len(word) for word in words]) if words else 0

        # تحليل التكرار
        word_freq = {}
        for word in words:
            word_lower = word.lower().strip('.,!?')
            if len(word_lower) > 3:  # تجاهل الكلمات القصيرة
                word_freq[word_lower] = word_freq.get(word_lower, 0) + 1

        # أكثر الكلمات تكراراً
        if word_freq:
            most_common = max(word_freq.items(), key=lambda x: x[1])
            analysis['most_repeated_word'] = most_common[0]
            analysis['max_word_frequency'] = most_common[1]

        return analysis

    def _extract_chunk_audio(self, video_path: str, audio_path: str,
                           start_time: float, end_time: float) -> bool:
        """استخراج الصوت من قطعة محددة"""
        try:
            import subprocess

            cmd = [
                self.video_processor.ffmpeg_path,
                '-i', video_path,
                '-ss', str(start_time),
                '-t', str(end_time - start_time),
                '-vn',  # بدون فيديو
                '-acodec', 'pcm_s16le',
                '-ar', '16000',  # معدل عينة 16kHz
                '-ac', '1',  # قناة واحدة
                '-y',
                audio_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0

        except Exception as e:
            logger.error(f"خطأ في استخراج الصوت: {e}")
            return False

    def _combine_advanced_analysis(self, audio_features: Dict[str, Any],
                                  visual_features: Dict[str, Any],
                                  text_analysis: Dict[str, Any],
                                  start_time: float, end_time: float) -> List[HighlightMoment]:
        """دمج نتائج التحليل المتقدم"""
        highlights = []

        try:
            # حساب النقاط لكل نوع من أنواع اللقطات
            scores = {}

            for highlight_type in HighlightType:
                score = self._calculate_highlight_score(
                    highlight_type, audio_features, visual_features, text_analysis
                )
                scores[highlight_type] = score

            # العثور على أفضل نوع
            best_type = max(scores, key=scores.get)
            best_score = scores[best_type]

            # إنشاء لقطة بارزة إذا كانت النقاط عالية بما فيه الكفاية
            if best_score >= self.min_confidence:
                highlight = HighlightMoment(
                    start_time=start_time,
                    end_time=end_time,
                    highlight_type=best_type,
                    confidence=best_score,
                    text=text_analysis.get('transcript', ''),
                    audio_features=audio_features,
                    visual_features=visual_features,
                    sentiment_score=text_analysis.get('excitement_level', 0.5),
                    source='advanced_analysis',
                    description=self._generate_highlight_description(best_type, best_score, text_analysis)
                )
                highlights.append(highlight)

            return highlights

        except Exception as e:
            logger.error(f"خطأ في دمج التحليل: {e}")
            return []

    def _calculate_highlight_score(self, highlight_type: HighlightType,
                                  audio_features: Dict[str, Any],
                                  visual_features: Dict[str, Any],
                                  text_analysis: Dict[str, Any]) -> float:
        """حساب نقاط اللقطة البارزة"""
        try:
            scores = []

            # 1. النقاط الصوتية
            audio_score = self._calculate_audio_score(highlight_type, audio_features)
            scores.append(audio_score * self.weights['audio'])

            # 2. النقاط البصرية
            visual_score = self._calculate_visual_score(highlight_type, visual_features)
            scores.append(visual_score * self.weights['visual'])

            # 3. النقاط النصية
            text_score = self._calculate_text_score(highlight_type, text_analysis)
            scores.append(text_score * self.weights['text'])

            # 4. نقاط المشاعر
            sentiment_score = self._calculate_sentiment_score(highlight_type, text_analysis)
            scores.append(sentiment_score * self.weights['sentiment'])

            # حساب المتوسط المرجح
            total_score = sum(scores)

            return min(total_score, 1.0)  # تحديد الحد الأقصى بـ 1.0

        except Exception as e:
            logger.error(f"خطأ في حساب النقاط: {e}")
            return 0.0

    def _calculate_audio_score(self, highlight_type: HighlightType,
                              audio_features: Dict[str, Any]) -> float:
        """حساب النقاط الصوتية"""
        if not audio_features:
            return 0.0

        score = 0.0

        # طاقة الصوت
        energy_mean = audio_features.get('energy_mean', 0)
        energy_std = audio_features.get('energy_std', 0)
        energy_peaks = audio_features.get('energy_peaks', 0)

        if highlight_type in [HighlightType.EXCITING, HighlightType.EPIC]:
            # اللقطات المثيرة تحتاج طاقة عالية
            score += min(energy_mean * 2, 0.4)
            score += min(energy_peaks * 0.1, 0.3)
        elif highlight_type == HighlightType.FUNNY:
            # اللقطات المضحكة قد تحتاج تنوع في الطاقة
            score += min(energy_std * 3, 0.4)

        # تحليل النبرة
        pitch_std = audio_features.get('pitch_std', 0)
        pitch_range = audio_features.get('pitch_range', 0)

        if highlight_type == HighlightType.SHOCKING:
            # اللقطات الصادمة قد تحتاج تغيرات مفاجئة في النبرة
            score += min(pitch_range * 0.001, 0.3)

        return min(score, 1.0)

    def _calculate_visual_score(self, highlight_type: HighlightType,
                               visual_features: Dict[str, Any]) -> float:
        """حساب النقاط البصرية"""
        if not visual_features:
            return 0.0

        score = 0.0

        # تحليل الحركة
        motion_mean = visual_features.get('motion_mean', 0)
        motion_peaks = visual_features.get('motion_peaks', 0)

        if highlight_type in [HighlightType.EXCITING, HighlightType.EPIC]:
            # اللقطات المثيرة تحتاج حركة عالية
            score += min(motion_mean * 0.01, 0.4)
            score += min(motion_peaks * 0.1, 0.3)

        # تحليل تغيرات السطوع
        brightness_changes = visual_features.get('brightness_changes', 0)

        if highlight_type == HighlightType.SHOCKING:
            # اللقطات الصادمة قد تحتاج تغيرات في الإضاءة
            score += min(brightness_changes * 0.05, 0.3)

        # وجود الوجوه
        faces_detected = visual_features.get('faces_detected', 0)
        if faces_detected > 0:
            score += 0.2  # مكافأة لوجود وجوه

        return min(score, 1.0)

    def _calculate_text_score(self, highlight_type: HighlightType,
                             text_analysis: Dict[str, Any]) -> float:
        """حساب النقاط النصية"""
        if not text_analysis:
            return 0.0

        score = 0.0

        # نقاط الكلمات المفتاحية
        keyword_score = text_analysis.get(f'{highlight_type.value}_keyword_score', 0)
        score += min(keyword_score * 0.1, 0.4)

        # نقاط الأنماط النصية
        if highlight_type == HighlightType.EXCITING:
            excitement_patterns = text_analysis.get('excitement_patterns', 0)
            score += min(excitement_patterns * 0.2, 0.3)
        elif highlight_type == HighlightType.FUNNY:
            laughter_patterns = text_analysis.get('laughter_patterns', 0)
            score += min(laughter_patterns * 0.2, 0.3)
        elif highlight_type == HighlightType.SHOCKING:
            shock_patterns = text_analysis.get('shock_patterns', 0)
            score += min(shock_patterns * 0.2, 0.3)

        # علامات الترقيم
        exclamation_marks = text_analysis.get('exclamation_marks', 0)
        caps_words = text_analysis.get('caps_words', 0)

        score += min(exclamation_marks * 0.05, 0.2)
        score += min(caps_words * 0.1, 0.1)

        return min(score, 1.0)

    def _calculate_sentiment_score(self, highlight_type: HighlightType,
                                  text_analysis: Dict[str, Any]) -> float:
        """حساب نقاط المشاعر"""
        if not text_analysis:
            return 0.0

        score = 0.0

        # مستوى الإثارة العام
        excitement_level = text_analysis.get('excitement_level', 0.5)
        score += excitement_level * 0.5

        # المشاعر المحددة
        emotions = text_analysis.get('emotions', {})

        if highlight_type == HighlightType.FUNNY:
            joy = emotions.get('joy', 0)
            score += joy * 0.5
        elif highlight_type == HighlightType.SHOCKING:
            surprise = emotions.get('surprise', 0)
            score += surprise * 0.5
        elif highlight_type in [HighlightType.EXCITING, HighlightType.EPIC]:
            # مزيج من المشاعر الإيجابية
            positive_emotions = emotions.get('joy', 0) + emotions.get('surprise', 0)
            score += positive_emotions * 0.25

        return min(score, 1.0)

    def _generate_highlight_description(self, highlight_type: HighlightType,
                                       score: float, text_analysis: Dict[str, Any]) -> str:
        """توليد وصف للقطة البارزة"""
        descriptions = {
            HighlightType.EXCITING: "لحظة مثيرة ومليئة بالطاقة",
            HighlightType.FUNNY: "لحظة مضحكة ومسلية",
            HighlightType.SHOCKING: "لحظة صادمة ومفاجئة",
            HighlightType.EMOTIONAL: "لحظة عاطفية مؤثرة",
            HighlightType.EPIC: "لحظة أسطورية لا تُنسى"
        }

        base_description = descriptions.get(highlight_type, "لحظة بارزة")
        confidence_text = f"بثقة {score*100:.0f}%"

        # إضافة تفاصيل من النص إذا كان متوفراً
        transcript = text_analysis.get('transcript', '')
        if transcript and len(transcript) > 10:
            preview = transcript[:50] + "..." if len(transcript) > 50 else transcript
            return f"{base_description} ({confidence_text}) - \"{preview}\""

        return f"{base_description} ({confidence_text})"

    def _select_best_highlights(self, highlights: List[HighlightMoment],
                               target_count: int) -> List[HighlightMoment]:
        """اختيار أفضل اللقطات البارزة"""
        if not highlights:
            return []

        # ترتيب حسب الثقة
        sorted_highlights = sorted(highlights, key=lambda h: h.confidence, reverse=True)

        # إزالة التداخلات
        filtered_highlights = self._remove_overlapping_highlights(sorted_highlights)

        # اختيار أفضل اللقطات
        best_highlights = filtered_highlights[:target_count]

        # ترتيب حسب الوقت
        best_highlights.sort(key=lambda h: h.start_time)

        return best_highlights

    def _remove_overlapping_highlights(self, highlights: List[HighlightMoment]) -> List[HighlightMoment]:
        """إزالة اللقطات المتداخلة"""
        if not highlights:
            return []

        filtered = [highlights[0]]

        for current in highlights[1:]:
            # التحقق من التداخل مع اللقطات المقبولة
            has_overlap = False
            for accepted in filtered:
                if (current.start_time < accepted.end_time and
                    current.end_time > accepted.start_time):
                    has_overlap = True
                    break

            if not has_overlap:
                filtered.append(current)

        return filtered

    def _enhance_highlights_with_reactions(self, video_path: str,
                                          highlights: List[HighlightMoment]) -> List[HighlightMoment]:
        """تحسين اللقطات باستخدام كشف ردات الفعل"""
        try:
            if not highlights:
                return highlights

            logger.info("بدء تحسين اللقطات باستخدام كشف ردات الفعل...")

            # إنشاء قائمة باللحظات المحفزة
            trigger_moments = [(h.start_time, h.end_time) for h in highlights]

            # كشف ردات الفعل
            reactions = self.reaction_detector.detect_reactions_in_video(video_path, trigger_moments)

            if not reactions:
                logger.info("لم يتم العثور على ردات فعل واضحة")
                return highlights

            # تحسين كل لقطة
            enhanced_highlights = []
            for highlight in highlights:
                # البحث عن ردة فعل مناسبة لهذه اللقطة
                suitable_reactions = [
                    r for r in reactions
                    if abs(r.start_time - highlight.end_time) <= 3.0  # ردة فعل خلال 3 ثوانٍ
                ]

                if suitable_reactions:
                    # اختيار أفضل ردة فعل (أعلى شدة)
                    best_reaction = max(suitable_reactions, key=lambda r: r.intensity)

                    # توسيع اللقطة لتشمل ردة الفعل
                    new_start, new_end = self.reaction_detector.extend_highlight_with_reaction(
                        highlight.start_time, highlight.end_time, [best_reaction]
                    )

                    # إنشاء لقطة محسنة
                    enhanced_highlight = HighlightMoment(
                        start_time=new_start,
                        end_time=new_end,
                        highlight_type=highlight.highlight_type,
                        confidence=min(highlight.confidence + best_reaction.confidence * 0.2, 1.0),
                        text=highlight.text,
                        audio_features=highlight.audio_features,
                        visual_features=highlight.visual_features,
                        sentiment_score=highlight.sentiment_score,
                        source=f"{highlight.source}_with_reaction",
                        description=f"{highlight.description} + ردة فعل {best_reaction.emotion_type}"
                    )

                    enhanced_highlights.append(enhanced_highlight)
                    logger.info(f"تم تحسين اللقطة {highlight.start_time:.1f}s بردة فعل {best_reaction.emotion_type}")
                else:
                    # لا توجد ردة فعل مناسبة، الاحتفاظ باللقطة الأصلية
                    enhanced_highlights.append(highlight)

            logger.info(f"تم تحسين {len([h for h in enhanced_highlights if 'reaction' in h.source])} لقطة بردات فعل")
            return enhanced_highlights

        except Exception as e:
            logger.error(f"خطأ في تحسين اللقطات بردات الفعل: {e}")
            return highlights  # إرجاع اللقطات الأصلية في حالة الخطأ

    def _generate_engaging_title(self, highlight) -> str:
        """إنشاء عنوان جذاب للقطة"""
        try:
            # قوالب العناوين حسب نوع اللقطة
            title_templates = {
                'exciting': [
                    "🔥 لحظة لا تصدق!",
                    "⚡ إثارة خالصة!",
                    "🚀 لحظة أسطورية!",
                    "💥 طاقة جنونية!"
                ],
                'funny': [
                    "😂 ضحك حتى البكاء!",
                    "🤣 كوميديا خالصة!",
                    "😆 لحظة مضحكة جداً!",
                    "😄 ضحك لا يتوقف!"
                ],
                'shocking': [
                    "😱 صدمة لا تصدق!",
                    "🤯 مفاجأة مذهلة!",
                    "😲 لم أتوقع هذا!",
                    "🫨 صدمة العمر!"
                ],
                'emotional': [
                    "💔 لحظة مؤثرة جداً",
                    "😢 عواطف جياشة",
                    "❤️ لحظة من القلب",
                    "🥺 مشاعر حقيقية"
                ],
                'epic': [
                    "👑 لحظة تاريخية!",
                    "🏆 إنجاز أسطوري!",
                    "⭐ لحظة لا تُنسى!",
                    "🎯 هدف مثالي!"
                ]
            }

            # اختيار عنوان عشوائي من القوالب
            templates = title_templates.get(highlight.highlight_type.value, ["🎬 لحظة بارزة!"])
            import random
            base_title = random.choice(templates)

            # إضافة معلومات إضافية إذا كان النص متوفراً
            if highlight.text and len(highlight.text) > 10:
                # استخراج كلمة مفتاحية من النص
                words = highlight.text.split()
                important_words = [w for w in words if len(w) > 4 and w.lower() not in ['this', 'that', 'with', 'from']]
                if important_words:
                    keyword = important_words[0].title()
                    base_title += f" | {keyword}"

            return base_title

        except Exception as e:
            logger.error(f"خطأ في إنشاء العنوان: {e}")
            return "🎬 لحظة بارزة!"

    def _generate_hashtags(self, highlight) -> List[str]:
        """إنشاء هاشتاجات للقطة"""
        try:
            hashtags = []

            # هاشتاجات أساسية
            hashtags.extend(['#shorts', '#viral', '#trending'])

            # هاشتاجات حسب نوع اللقطة
            type_hashtags = {
                'exciting': ['#exciting', '#amazing', '#wow', '#epic', '#insane'],
                'funny': ['#funny', '#comedy', '#lol', '#hilarious', '#meme'],
                'shocking': ['#shocking', '#surprise', '#unexpected', '#mindblown'],
                'emotional': ['#emotional', '#touching', '#feelings', '#heartwarming'],
                'epic': ['#epic', '#legendary', '#awesome', '#incredible', '#amazing']
            }

            specific_tags = type_hashtags.get(highlight.highlight_type.value, [])
            hashtags.extend(specific_tags[:3])  # أخذ أول 3 هاشتاجات

            # هاشتاجات من النص
            if highlight.text:
                words = highlight.text.lower().split()
                # البحث عن كلمات مناسبة للهاشتاجات
                for word in words:
                    word = word.strip('.,!?')
                    if (len(word) > 3 and
                        word not in ['this', 'that', 'with', 'from', 'they', 'have', 'will'] and
                        len(hashtags) < 10):
                        hashtags.append(f'#{word}')

            return hashtags[:10]  # حد أقصى 10 هاشتاجات

        except Exception as e:
            logger.error(f"خطأ في إنشاء الهاشتاجات: {e}")
            return ['#shorts', '#viral']
