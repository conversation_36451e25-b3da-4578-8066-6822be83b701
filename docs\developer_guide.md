# دليل المطور - Video Editor Pro

## نظرة عامة

Video Editor Pro هو تطبيق تحرير فيديو مبني بـ Python يستخدم Tkinter للواجهة الرسومية و FFmpeg لمعالجة الفيديو، مع دعم ميزات الذكاء الاصطناعي عبر APIs مجانية.

## 🏗️ هيكل المشروع

```
video-editor-pro/
├── src/                          # الكود المصدري
│   ├── config/                   # إعدادات التطبيق
│   │   ├── __init__.py
│   │   └── settings.py
│   ├── core/                     # الوحدات الأساسية
│   │   ├── __init__.py
│   │   └── video_processor.py    # معالج الفيديو الرئيسي
│   ├── gui/                      # واجهة المستخدم
│   │   ├── __init__.py
│   │   ├── main_window.py        # النافذة الرئيسية
│   │   └── dialogs.py            # نوافذ الحوار
│   ├── ai/                       # وحدات الذكاء الاصطناعي
│   │   ├── __init__.py
│   │   ├── huggingface_client.py # عميل Hugging Face
│   │   ├── google_cloud_client.py# عميل Google Cloud
│   │   ├── ai_manager.py         # مدير الذكاء الاصطناعي
│   │   ├── face_tracking.py      # تتبع الوجوه
│   │   ├── caption_generator.py  # مولد التعليقات
│   │   ├── content_generator.py  # مولد المحتوى
│   │   ├── content_analyzer.py   # محلل المحتوى
│   │   └── auto_publisher.py     # الناشر التلقائي
│   └── utils/                    # الأدوات المساعدة
│       ├── __init__.py
│       ├── file_utils.py         # أدوات الملفات
│       ├── validators.py         # أدوات التحقق
│       └── performance_optimizer.py # محسن الأداء
├── tests/                        # الاختبارات
│   ├── test_video_processor.py
│   ├── test_ai_modules.py
│   ├── test_gui.py
│   └── run_tests.py
├── docs/                         # الوثائق
│   ├── user_guide.md
│   ├── developer_guide.md
│   └── api_reference.md
├── assets/                       # الموارد
│   ├── icons/
│   └── templates/
├── main.py                       # نقطة الدخول الرئيسية
├── requirements.txt              # المتطلبات
├── setup.py                      # إعداد التثبيت
├── .env.example                  # مثال متغيرات البيئة
├── .gitignore
├── LICENSE
└── README.md
```

## 🔧 المكونات الأساسية

### 1. معالج الفيديو (VideoProcessor)

**الملف**: `src/core/video_processor.py`

**الوظائف الرئيسية**:
```python
class VideoProcessor:
    def get_video_info(self, video_path: str) -> Dict
    def trim_video(self, input_path: str, output_path: str, start_time: float, end_time: float) -> bool
    def resize_video(self, input_path: str, output_path: str, width: int, height: int, quality: str) -> bool
    def add_text_overlay(self, input_path: str, output_path: str, text: str, position: str, font_size: int, font_color: str) -> bool
    def convert_format(self, input_path: str, output_path: str, output_format: str, quality: str) -> bool
    def extract_audio(self, input_path: str, output_path: str, audio_format: str) -> bool
```

**مثال الاستخدام**:
```python
from core.video_processor import VideoProcessor

processor = VideoProcessor()

# الحصول على معلومات الفيديو
info = processor.get_video_info("input.mp4")
print(f"المدة: {info['duration']} ثانية")

# قص الفيديو
success = processor.trim_video("input.mp4", "output.mp4", 10.0, 30.0)
```

### 2. مدير الذكاء الاصطناعي (AIManager)

**الملف**: `src/ai/ai_manager.py`

**الوظائف الرئيسية**:
```python
class AIManager:
    def auto_clip_video(self, video_path: str, target_duration: float, num_clips: int) -> Optional[List[Dict]]
    def generate_auto_captions(self, video_path: str, language: str) -> Optional[List[Dict]]
```

### 3. واجهة المستخدم (VideoEditorApp)

**الملف**: `src/gui/main_window.py`

**البنية**:
```python
class VideoEditorApp:
    def __init__(self, root: tk.Tk)
    def setup_main_window(self)
    def create_widgets(self)
    def open_video(self)
    def trim_video(self)
    def add_text(self)
    def export_video(self)
```

## 🔌 APIs والتكامل

### Hugging Face Integration

**الملف**: `src/ai/huggingface_client.py`

```python
class HuggingFaceClient:
    def __init__(self, api_key: Optional[str] = None)
    def speech_to_text(self, audio_file_path: str, language: str = "ar") -> Optional[str]
    def translate_text(self, text: str, source_lang: str = "en", target_lang: str = "ar") -> Optional[str]
    def analyze_sentiment(self, text: str) -> Optional[Dict[str, Any]]
    def generate_text(self, prompt: str, max_length: int = 100) -> Optional[str]
```

### Google Cloud Integration

**الملف**: `src/ai/google_cloud_client.py`

```python
class GoogleCloudClient:
    def detect_faces_in_video(self, video_path: str) -> Optional[List[Dict]]
    def analyze_video_content(self, video_path: str) -> Optional[Dict]
    def transcribe_audio(self, audio_path: str, language_code: str = "ar-SA") -> Optional[str]
```

## 🧪 الاختبارات

### تشغيل الاختبارات

```bash
# جميع الاختبارات
python tests/run_tests.py

# اختبار وحدة محددة
python tests/run_tests.py --module test_video_processor

# اختبارات الأداء
python tests/run_tests.py --performance

# اختبارات التكامل
python tests/run_tests.py --integration

# فحص البيئة
python tests/run_tests.py --check-env
```

### كتابة اختبارات جديدة

```python
import unittest
from core.video_processor import VideoProcessor

class TestNewFeature(unittest.TestCase):
    def setUp(self):
        self.processor = VideoProcessor()
    
    def test_new_functionality(self):
        # اختبار الوظيفة الجديدة
        result = self.processor.new_function("test_input")
        self.assertIsNotNone(result)
        self.assertTrue(result.success)
```

## 🔧 إضافة ميزات جديدة

### 1. إضافة معالج فيديو جديد

```python
# في src/core/video_processor.py
def new_video_effect(self, input_path: str, output_path: str, 
                    effect_params: Dict) -> bool:
    """إضافة تأثير جديد على الفيديو"""
    try:
        cmd = [
            self.ffmpeg_path,
            '-i', input_path,
            '-vf', f'new_effect={effect_params}',
            '-y', output_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        return result.returncode == 0
        
    except Exception as e:
        logger.error(f"خطأ في التأثير الجديد: {e}")
        return False
```

### 2. إضافة ميزة ذكاء اصطناعي

```python
# في src/ai/new_ai_feature.py
class NewAIFeature:
    def __init__(self):
        self.client = HuggingFaceClient()
    
    def process_video(self, video_path: str) -> Dict[str, Any]:
        """معالجة جديدة بالذكاء الاصطناعي"""
        # تنفيذ الميزة الجديدة
        pass
```

### 3. إضافة نافذة حوار جديدة

```python
# في src/gui/dialogs.py
class NewDialog(BaseDialog):
    def __init__(self, parent: tk.Tk, parameters: Dict):
        self.parameters = parameters
        super().__init__(parent, "عنوان النافذة", 400, 300)
    
    def create_widgets(self):
        # إنشاء عناصر واجهة المستخدم
        pass
    
    def validate_input(self) -> bool:
        # التحقق من صحة المدخلات
        pass
    
    def get_result(self) -> Any:
        # إرجاع النتيجة
        pass
```

## 📊 تحسين الأداء

### استخدام محسن الأداء

```python
from utils.performance_optimizer import PerformanceOptimizer

optimizer = PerformanceOptimizer()

# فحص موارد النظام
system_info = optimizer.get_system_info()
print(f"الذاكرة المتاحة: {system_info['memory']['available_gb']:.1f}GB")

# تحسين عدد العمليات المتوازية
worker_count = optimizer.optimize_worker_count("video_processing")

# معالجة متوازية
tasks = [
    {"type": "trim", "input_path": "video1.mp4", "output_path": "out1.mp4", "params": {"start_time": 0, "end_time": 10}},
    {"type": "resize", "input_path": "video2.mp4", "output_path": "out2.mp4", "params": {"width": 1920, "height": 1080}}
]

results = optimizer.parallel_video_processing(tasks)
```

### مراقبة الأداء

```python
# مراقبة الأداء لمدة دقيقة
performance_data = optimizer.monitor_performance(60)
print(f"متوسط استخدام المعالج: {performance_data['cpu']['average']}%")
```

## 🔐 الأمان وأفضل الممارسات

### 1. التحقق من المدخلات

```python
from utils.validators import VideoValidator, TextValidator

video_validator = VideoValidator()
text_validator = TextValidator()

# التحقق من صحة الفيديو
is_valid, message = video_validator.validate_video_file(video_path)
if not is_valid:
    raise ValueError(f"ملف فيديو غير صحيح: {message}")

# التحقق من صحة النص
is_valid, message = text_validator.validate_text_overlay(text)
if not is_valid:
    raise ValueError(f"نص غير صحيح: {message}")
```

### 2. إدارة الأخطاء

```python
import logging

logger = logging.getLogger(__name__)

def safe_video_operation(video_path: str) -> bool:
    try:
        # العملية الرئيسية
        result = process_video(video_path)
        logger.info("تمت العملية بنجاح")
        return result
        
    except FileNotFoundError:
        logger.error(f"الملف غير موجود: {video_path}")
        return False
        
    except Exception as e:
        logger.error(f"خطأ غير متوقع: {e}", exc_info=True)
        return False
```

### 3. إدارة الذاكرة

```python
def process_large_video(video_path: str):
    """معالجة فيديو كبير بكفاءة"""
    
    # فحص الذاكرة المتاحة
    optimizer = PerformanceOptimizer()
    memory_config = optimizer.optimize_memory_usage("video_processing")
    
    if memory_config["memory_available_gb"] < 2:
        # معالجة على دفعات صغيرة
        chunk_size = memory_config["chunk_size"] // 2
    else:
        chunk_size = memory_config["chunk_size"]
    
    # معالجة الفيديو
    process_video_in_chunks(video_path, chunk_size)
```

## 🚀 النشر والتوزيع

### بناء التطبيق

```bash
# تثبيت أدوات البناء
pip install pyinstaller

# بناء ملف تنفيذي واحد
pyinstaller --onefile --windowed main.py

# بناء مع الموارد
pyinstaller --onefile --windowed --add-data "assets;assets" main.py
```

### إنشاء حزمة التوزيع

```bash
# إنشاء حزمة wheel
python setup.py bdist_wheel

# إنشاء حزمة source
python setup.py sdist

# رفع إلى PyPI
pip install twine
twine upload dist/*
```

## 📝 المساهمة في المشروع

### 1. إعداد بيئة التطوير

```bash
# استنساخ المشروع
git clone https://github.com/your-username/video-editor-pro.git
cd video-editor-pro

# إنشاء فرع جديد
git checkout -b feature/new-feature

# إعداد البيئة
python -m venv venv
source venv/bin/activate  # أو venv\Scripts\activate على Windows
pip install -r requirements.txt
pip install -r requirements-dev.txt
```

### 2. معايير الكود

```bash
# تنسيق الكود
black src/ tests/

# فحص الكود
flake8 src/ tests/

# فحص الأنواع
mypy src/
```

### 3. إرسال التغييرات

```bash
# إضافة الملفات
git add .

# إنشاء commit
git commit -m "إضافة ميزة جديدة: وصف الميزة"

# دفع التغييرات
git push origin feature/new-feature

# إنشاء Pull Request على GitHub
```

## 🐛 تتبع الأخطاء

### تفعيل السجلات التفصيلية

```python
import logging

# تفعيل السجلات التفصيلية
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('debug.log'),
        logging.StreamHandler()
    ]
)
```

### أدوات التشخيص

```python
from utils.performance_optimizer import PerformanceOptimizer

optimizer = PerformanceOptimizer()

# فحص النظام
system_info = optimizer.get_system_info()
print(json.dumps(system_info, indent=2, ensure_ascii=False))

# تنظيف الملفات المؤقتة
cleanup_result = optimizer.cleanup_temp_files()
print(f"تم تنظيف {cleanup_result['cleaned_files']} ملف")
```

## 📚 موارد إضافية

### الوثائق الخارجية

- [FFmpeg Documentation](https://ffmpeg.org/documentation.html)
- [Tkinter Tutorial](https://docs.python.org/3/library/tkinter.html)
- [Hugging Face API](https://huggingface.co/docs/api-inference/index)
- [Google Cloud Video Intelligence](https://cloud.google.com/video-intelligence/docs)

### أدوات التطوير المفيدة

- **IDE**: PyCharm, VSCode
- **Git GUI**: GitKraken, SourceTree
- **API Testing**: Postman, Insomnia
- **Profiling**: cProfile, py-spy

---

## 📞 الدعم للمطورين

- **GitHub Issues**: [المشاكل التقنية](https://github.com/your-username/video-editor-pro/issues)
- **Discussions**: [النقاشات](https://github.com/your-username/video-editor-pro/discussions)
- **Wiki**: [الويكي](https://github.com/your-username/video-editor-pro/wiki)

---

*آخر تحديث: ديسمبر 2024*
