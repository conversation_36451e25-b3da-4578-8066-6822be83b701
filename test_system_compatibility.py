#!/usr/bin/env python3
"""
اختبار توافق النظام
System Compatibility Test

يختبر النظام مع وبدون المكتبات المتقدمة
"""

import sys
import os
import logging
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_compatibility.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def test_core_libraries():
    """اختبار المكتبات الأساسية"""
    try:
        print("🔧 اختبار المكتبات الأساسية")
        print("=" * 40)
        
        # OpenCV (أساسي)
        try:
            import cv2
            print("✅ OpenCV متوفر - كشف وجه أساسي")
        except ImportError:
            print("❌ OpenCV غير متوفر - مطلوب للنظام")
            return False
        
        # NumPy (أساسي)
        try:
            import numpy as np
            print("✅ NumPy متوفر - عمليات رياضية")
        except ImportError:
            print("❌ NumPy غير متوفر - مطلوب للنظام")
            return False
        
        # Pillow (أساسي)
        try:
            from PIL import Image
            print("✅ Pillow متوفر - معالجة صور")
        except ImportError:
            print("❌ Pillow غير متوفر - مطلوب للنظام")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المكتبات الأساسية: {e}")
        return False

def test_optional_libraries():
    """اختبار المكتبات الاختيارية"""
    try:
        print(f"\n🎭 اختبار المكتبات المتقدمة (اختيارية)")
        print("=" * 50)
        
        optional_libs = {
            'mediapipe': 'كشف وجه وحركة متقدم',
            'deepface': 'تحليل عواطف متقدم',
            'feat': 'وحدات العمل (Action Units)',
            'librosa': 'تحليل صوتي متقدم',
            'speech_recognition': 'تحويل كلام لنص'
        }
        
        available_libs = []
        missing_libs = []
        
        for lib_name, description in optional_libs.items():
            try:
                if lib_name == 'mediapipe':
                    import mediapipe
                elif lib_name == 'deepface':
                    from deepface import DeepFace
                elif lib_name == 'feat':
                    import feat
                elif lib_name == 'librosa':
                    import librosa
                elif lib_name == 'speech_recognition':
                    import speech_recognition
                
                print(f"✅ {lib_name} متوفر - {description}")
                available_libs.append(lib_name)
                
            except ImportError:
                print(f"⚠️ {lib_name} غير متوفر - {description}")
                missing_libs.append(lib_name)
        
        print(f"\n📊 النتيجة:")
        print(f"   متوفر: {len(available_libs)}/{len(optional_libs)} مكتبة")
        print(f"   مفقود: {len(missing_libs)} مكتبة")
        
        if missing_libs:
            print(f"\n💡 لتثبيت المكتبات المفقودة:")
            for lib in missing_libs:
                if lib == 'mediapipe':
                    print(f"   pip install mediapipe")
                elif lib == 'deepface':
                    print(f"   pip install deepface")
                elif lib == 'feat':
                    print(f"   pip install py-feat")
                elif lib == 'librosa':
                    print(f"   pip install librosa")
                elif lib == 'speech_recognition':
                    print(f"   pip install SpeechRecognition")
        
        return len(available_libs), len(missing_libs)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المكتبات الاختيارية: {e}")
        return 0, len(optional_libs)

def test_reaction_detector():
    """اختبار كاشف ردات الفعل"""
    try:
        print(f"\n🎯 اختبار كاشف ردات الفعل")
        print("=" * 35)
        
        # استيراد الكاشف
        try:
            from ai.reaction_detector import ReactionDetector
            print("✅ تم استيراد كاشف ردات الفعل")
        except ImportError as e:
            print(f"❌ فشل في استيراد الكاشف: {e}")
            return False
        
        # إنشاء الكاشف
        try:
            detector = ReactionDetector()
            print("✅ تم إنشاء الكاشف بنجاح")
        except Exception as e:
            print(f"❌ فشل في إنشاء الكاشف: {e}")
            return False
        
        # فحص الميزات المتوفرة
        print(f"\n🔍 الميزات المتوفرة:")
        
        # MediaPipe
        if hasattr(detector, 'face_mesh') and detector.face_mesh:
            print("   ✅ MediaPipe Face Mesh - كشف وجه متقدم")
        else:
            print("   ⚠️ MediaPipe غير متوفر - سيتم استخدام OpenCV")
        
        if hasattr(detector, 'pose') and detector.pose:
            print("   ✅ MediaPipe Pose - تحليل حركة متقدم")
        else:
            print("   ⚠️ MediaPipe Pose غير متوفر - سيتم استخدام كشف حركة أساسي")
        
        # OpenCV البديل
        if hasattr(detector, 'face_cascade') and detector.face_cascade:
            print("   ✅ OpenCV Face Cascade - كشف وجه أساسي")
        else:
            print("   ❌ OpenCV Face Cascade غير متوفر")
        
        # المحلل المتقدم
        if hasattr(detector, 'advanced_analyzer') and detector.advanced_analyzer:
            print("   ✅ المحلل المتقدم - تحليل عواطف شامل")
        else:
            print("   ⚠️ المحلل المتقدم غير متوفر - سيتم استخدام التحليل الأساسي")
        
        print(f"\n📋 إعدادات الكاشف:")
        print(f"   نافذة البحث: {detector.reaction_window} ثانية")
        print(f"   أنواع ردات الفعل: {len(detector.reaction_types)}")
        print(f"   عتبة الشدة: {detector.intensity_threshold}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الكاشف: {e}")
        return False

def test_basic_functionality():
    """اختبار الوظائف الأساسية"""
    try:
        print(f"\n⚙️ اختبار الوظائف الأساسية")
        print("=" * 35)
        
        # إنشاء صورة اختبار
        import numpy as np
        import cv2
        
        test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
        test_frame[100:200, 100:200] = [255, 255, 255]  # مربع أبيض
        
        print("✅ تم إنشاء إطار اختبار")
        
        # اختبار كاشف ردات الفعل
        from ai.reaction_detector import ReactionDetector
        detector = ReactionDetector()
        
        # اختبار تحليل إطار
        try:
            result = detector._analyze_frame(test_frame, 0.0)
            if result:
                print("✅ تحليل الإطار يعمل")
                print(f"   الوجوه المكتشفة: {result.get('multiple_faces', 0)}")
                print(f"   مستوى الحركة: {result.get('body_movement', 0.0):.3f}")
            else:
                print("⚠️ تحليل الإطار أرجع نتيجة فارغة")
        except Exception as e:
            print(f"❌ خطأ في تحليل الإطار: {e}")
            return False
        
        # اختبار كشف الوجه الأساسي
        try:
            face_result = detector._basic_face_detection(test_frame)
            print("✅ كشف الوجه الأساسي يعمل")
            print(f"   الوجوه: {face_result.get('multiple_faces', 0)}")
        except Exception as e:
            print(f"❌ خطأ في كشف الوجه الأساسي: {e}")
        
        # اختبار كشف الحركة الأساسي
        try:
            movement = detector._basic_movement_detection(test_frame)
            print("✅ كشف الحركة الأساسي يعمل")
            print(f"   مستوى الحركة: {movement:.3f}")
        except Exception as e:
            print(f"❌ خطأ في كشف الحركة الأساسي: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الوظائف الأساسية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    try:
        print("🚀 بدء اختبار توافق النظام")
        print("=" * 50)
        
        # اختبار المكتبات الأساسية
        core_test = test_core_libraries()
        
        # اختبار المكتبات الاختيارية
        available_count, missing_count = test_optional_libraries()
        
        # اختبار كاشف ردات الفعل
        detector_test = test_reaction_detector() if core_test else False
        
        # اختبار الوظائف الأساسية
        functionality_test = test_basic_functionality() if detector_test else False
        
        print(f"\n📋 ملخص النتائج:")
        print(f"   المكتبات الأساسية: {'✅ متوفرة' if core_test else '❌ مفقودة'}")
        print(f"   المكتبات المتقدمة: {available_count}/{available_count + missing_count} متوفرة")
        print(f"   كاشف ردات الفعل: {'✅ يعمل' if detector_test else '❌ لا يعمل'}")
        print(f"   الوظائف الأساسية: {'✅ تعمل' if functionality_test else '❌ لا تعمل'}")
        
        if core_test and detector_test:
            print(f"\n🎉 النظام جاهز للاستخدام!")
            
            if missing_count == 0:
                print(f"   🌟 جميع الميزات المتقدمة متوفرة")
                print(f"   🎭 تحليل عواطف متقدم")
                print(f"   🤲 كشف إيماءات متطور")
                print(f"   🔊 تحليل صوتي شامل")
            else:
                print(f"   ⚡ الوضع الأساسي مفعل")
                print(f"   👤 كشف وجه أساسي (OpenCV)")
                print(f"   🏃 كشف حركة أساسي")
                print(f"   📱 كشف هواتف بسيط")
                
            print(f"\n💡 النظام سيعمل بأفضل ما متوفر من ميزات")
            print(f"   يمكنك تثبيت المكتبات المتقدمة لاحقاً لتحسين الأداء")
        else:
            print(f"\n⚠️ هناك مشاكل تحتاج إلى إصلاح")
            if not core_test:
                print(f"💡 تأكد من تثبيت المكتبات الأساسية أولاً")
        
        return 0 if (core_test and detector_test) else 1
        
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {e}")
        logger.error(f"خطأ عام في الاختبار: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
