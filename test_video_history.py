#!/usr/bin/env python3
"""
اختبار نظام إدارة تاريخ الفيديوهات
Test Video History Management System
"""

import sys
import os
import logging
from pathlib import Path
from datetime import datetime

# إضافة مجلد src إلى المسار
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from utils.video_history_manager import VideoHistoryManager, VideoRecord

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_video_history.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def test_video_history_manager():
    """اختبار مدير تاريخ الفيديوهات"""
    try:
        print("🧪 اختبار نظام إدارة تاريخ الفيديوهات")
        print("=" * 50)
        
        # إنشاء مدير التاريخ
        history_manager = VideoHistoryManager(data_dir="test_data")
        print("✅ تم إنشاء مدير التاريخ بنجاح")
        
        # اختبار إضافة فيديو
        print("\n📹 اختبار إضافة فيديو...")
        
        # البحث عن ملف فيديو موجود
        downloads_dir = Path("downloads")
        video_files = list(downloads_dir.glob("*.mp4")) if downloads_dir.exists() else []
        
        if video_files:
            video_file = video_files[0]
            print(f"📁 استخدام الملف: {video_file.name}")
            
            video_id = history_manager.add_downloaded_video(
                title="فيديو اختبار - Test Video",
                url="https://youtube.com/test",
                file_path=str(video_file),
                duration=300.0,  # 5 دقائق
                info_file_path=None
            )
            
            print(f"✅ تم إضافة الفيديو بنجاح - ID: {video_id}")
            
            # اختبار الحصول على الفيديو
            video_record = history_manager.get_video_by_id(video_id)
            if video_record:
                print(f"✅ تم استرداد الفيديو: {video_record.title}")
            else:
                print("❌ فشل في استرداد الفيديو")
                return False
            
            # اختبار تحديث العلامات
            print("\n🏷️ اختبار تحديث العلامات...")
            tags = ["اختبار", "test", "فيديو"]
            success = history_manager.update_video_tags(video_id, tags)
            if success:
                print("✅ تم تحديث العلامات بنجاح")
            else:
                print("❌ فشل في تحديث العلامات")
            
            # اختبار تحديث الملاحظات
            print("\n📝 اختبار تحديث الملاحظات...")
            notes = "هذا فيديو اختبار لنظام إدارة التاريخ"
            success = history_manager.update_video_notes(video_id, notes)
            if success:
                print("✅ تم تحديث الملاحظات بنجاح")
            else:
                print("❌ فشل في تحديث الملاحظات")
            
            # اختبار إضافة سجل معالجة
            print("\n⚙️ اختبار إضافة سجل معالجة...")
            history_manager.add_processing_record(
                video_id=video_id,
                method="advanced",
                target_clips=5,
                results_count=3,
                processing_time=120.5,
                success=True,
                output_files=["clip1.mp4", "clip2.mp4", "clip3.mp4"]
            )
            print("✅ تم إضافة سجل المعالجة بنجاح")
            
            # اختبار البحث
            print("\n🔍 اختبار البحث...")
            search_results = history_manager.search_videos("اختبار")
            if search_results:
                print(f"✅ تم العثور على {len(search_results)} نتيجة بحث")
            else:
                print("❌ لم يتم العثور على نتائج بحث")
            
            # اختبار الإحصائيات
            print("\n📊 اختبار الإحصائيات...")
            stats = history_manager.get_statistics()
            print(f"📹 إجمالي الفيديوهات: {stats['total_videos']}")
            print(f"✅ الفيديوهات المعالجة: {stats['processed_videos']}")
            print(f"⚙️ محاولات المعالجة: {stats['total_processing_attempts']}")
            print(f"📊 معدل النجاح: {stats['success_rate']:.1f}%")
            print(f"💾 إجمالي الحجم: {stats['total_size_mb']:.1f} MB")
            
            # اختبار الحصول على جميع الفيديوهات
            print("\n📋 اختبار الحصول على جميع الفيديوهات...")
            all_videos = history_manager.get_all_videos()
            print(f"✅ تم الحصول على {len(all_videos)} فيديو")
            
            # اختبار الحصول على الفيديوهات المعالجة
            processed_videos = history_manager.get_processed_videos()
            print(f"✅ الفيديوهات المعالجة: {len(processed_videos)}")
            
            # اختبار تاريخ المعالجة
            processing_history = history_manager.get_video_processing_history(video_id)
            print(f"✅ سجلات المعالجة: {len(processing_history)}")
            
            print(f"\n✅ جميع الاختبارات نجحت!")
            return True
            
        else:
            print("❌ لا توجد ملفات فيديو للاختبار في مجلد downloads")
            print("💡 قم بتحميل فيديو أولاً لاختبار النظام")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        logger.error(f"خطأ في اختبار نظام إدارة التاريخ: {e}")
        return False

def test_gui_integration():
    """اختبار تكامل الواجهة"""
    try:
        print(f"\n🖥️ اختبار تكامل الواجهة")
        print("=" * 30)
        
        # محاولة استيراد نافذة إدارة الفيديوهات
        try:
            from gui.video_history_window import VideoHistoryWindow
            print("✅ تم استيراد نافذة إدارة الفيديوهات بنجاح")
        except ImportError as e:
            print(f"❌ فشل في استيراد نافذة إدارة الفيديوهات: {e}")
            return False
        
        # محاولة استيراد مدير التاريخ
        try:
            from utils.video_history_manager import VideoHistoryManager
            print("✅ تم استيراد مدير التاريخ بنجاح")
        except ImportError as e:
            print(f"❌ فشل في استيراد مدير التاريخ: {e}")
            return False
        
        print("✅ جميع المكونات متوفرة للتكامل")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {e}")
        return False

def cleanup_test_data():
    """تنظيف بيانات الاختبار"""
    try:
        test_data_dir = Path("test_data")
        if test_data_dir.exists():
            import shutil
            shutil.rmtree(test_data_dir)
            print("🧹 تم تنظيف بيانات الاختبار")
    except Exception as e:
        print(f"⚠️ تحذير: فشل في تنظيف بيانات الاختبار: {e}")

def main():
    """الدالة الرئيسية"""
    try:
        print("🚀 بدء اختبار نظام إدارة تاريخ الفيديوهات")
        print("=" * 60)
        
        # اختبار النظام الأساسي
        basic_test = test_video_history_manager()
        
        # اختبار تكامل الواجهة
        gui_test = test_gui_integration()
        
        print(f"\n📋 ملخص النتائج:")
        print(f"   الاختبار الأساسي: {'✅ نجح' if basic_test else '❌ فشل'}")
        print(f"   اختبار الواجهة: {'✅ نجح' if gui_test else '❌ فشل'}")
        
        if basic_test and gui_test:
            print(f"\n🎉 نظام إدارة تاريخ الفيديوهات جاهز للاستخدام!")
            print(f"💡 يمكنك الآن:")
            print(f"   • الوصول إلى الفيديوهات المحملة سابقاً")
            print(f"   • إعادة معالجة الفيديوهات")
            print(f"   • إدارة العلامات والملاحظات")
            print(f"   • عرض الإحصائيات والتقارير")
        else:
            print(f"\n⚠️ هناك مشاكل في النظام تحتاج إلى إصلاح")
        
        # تنظيف بيانات الاختبار
        cleanup_test_data()
        
        return 0 if (basic_test and gui_test) else 1
        
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {e}")
        logger.error(f"خطأ عام في الاختبار: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
